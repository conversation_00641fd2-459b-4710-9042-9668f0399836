<?php

namespace App\Filament\Company\Resources\ArenaDoviz\CombinedExchangeResource\Pages;

use App\Enums\ArenaDoviz\ExchangeType;
use App\Filament\Company\Resources\ArenaDoviz\CombinedExchangeResource;
use App\Models\ArenaDoviz\CurrencyExchange;
use App\Services\ArenaDoviz\AdvancedTransactionService;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class CreateCombinedExchange extends CreateRecord
{
    protected static string $resource = CombinedExchangeResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        return DB::transaction(function () use ($data) {
            // Create the main combined exchange record
            $combinedExchange = CurrencyExchange::create([
                'company_id' => auth()->user()->currentCompany->id,
                'client_id' => $data['buy_from_client_id'], // Primary client (buy from)
                'location_id' => $data['location_id'],
                'exchange_type' => ExchangeType::COMBINED_EXCHANGE,
                'status' => $data['status'],
                'from_currency_code' => $data['buy_from_currency_code'],
                'to_currency_code' => $data['sell_to_currency_code'],
                'from_amount' => $data['buy_amount'],
                'to_amount' => $data['sell_amount'],
                'exchange_rate' => $data['sell_rate'] / $data['buy_rate'], // Combined rate
                'commission_rate' => 0, // Profit is calculated differently for combined exchanges
                'commission_amount' => 0,
                'net_amount' => $data['sell_amount'],
                'exchange_date' => $data['exchange_date'],
                'notes' => $data['notes'] ?? null,
                'reference_number' => $data['reference_number'] ?? null,
                'created_by' => auth()->id(),
            ]);

            // Create the buy leg transaction
            $buyExchange = CurrencyExchange::create([
                'company_id' => auth()->user()->currentCompany->id,
                'client_id' => $data['buy_from_client_id'],
                'location_id' => $data['location_id'],
                'exchange_type' => ExchangeType::BUY,
                'status' => $data['status'],
                'from_currency_code' => $data['buy_from_currency_code'],
                'to_currency_code' => 'TRY_' . $combinedExchange->location->code,
                'from_amount' => $data['buy_amount'],
                'to_amount' => $data['buy_amount'] * $data['buy_rate'],
                'exchange_rate' => $data['buy_rate'],
                'commission_rate' => 0,
                'commission_amount' => 0,
                'net_amount' => $data['buy_amount'] * $data['buy_rate'],
                'exchange_date' => $data['exchange_date'],
                'notes' => 'Combined Exchange - Buy Leg (Ref: ' . $combinedExchange->exchange_number . ')',
                'reference_number' => $combinedExchange->exchange_number . '-BUY',
                'created_by' => auth()->id(),
            ]);

            // Create the sell leg transaction
            $sellExchange = CurrencyExchange::create([
                'company_id' => auth()->user()->currentCompany->id,
                'client_id' => $data['sell_to_client_id'],
                'location_id' => $data['location_id'],
                'exchange_type' => ExchangeType::SELL,
                'status' => $data['status'],
                'from_currency_code' => 'TRY_' . $combinedExchange->location->code,
                'to_currency_code' => $data['sell_to_currency_code'],
                'from_amount' => $data['sell_amount'] * $data['sell_rate'],
                'to_amount' => $data['sell_amount'],
                'exchange_rate' => 1 / $data['sell_rate'],
                'commission_rate' => 0,
                'commission_amount' => 0,
                'net_amount' => $data['sell_amount'],
                'exchange_date' => $data['exchange_date'],
                'notes' => 'Combined Exchange - Sell Leg (Ref: ' . $combinedExchange->exchange_number . ')',
                'reference_number' => $combinedExchange->exchange_number . '-SELL',
                'created_by' => auth()->id(),
            ]);

            // Store references to the individual transactions in the combined exchange notes
            $combinedExchange->update([
                'notes' => ($data['notes'] ?? '') . "\n\nBuy Transaction: " . $buyExchange->exchange_number . 
                          "\nSell Transaction: " . $sellExchange->exchange_number .
                          "\nProfit: " . number_format(($data['sell_amount'] * $data['sell_rate']) - ($data['buy_amount'] * $data['buy_rate']), 2) . ' TRY'
            ]);

            return $combinedExchange;
        });
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
