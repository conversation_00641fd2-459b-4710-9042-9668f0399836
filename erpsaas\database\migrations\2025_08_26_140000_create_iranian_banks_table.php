<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('iranian_banks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->constrained()->cascadeOnDelete();
            
            // Basic bank information
            $table->string('name', 100); // English name
            $table->string('persian_name', 100)->nullable(); // Persian/Farsi name
            $table->string('bank_code', 10)->unique(); // Central Bank assigned code
            $table->string('swift_code', 15)->nullable(); // SWIFT/BIC code for international
            $table->string('central_bank_code', 20)->nullable(); // Central Bank of Iran code
            
            // Bank classification
            $table->enum('bank_type', [
                'commercial', 'specialized', 'development', 
                'cooperative', 'islamic', 'private', 'government'
            ])->default('commercial');
            
            // Capabilities
            $table->boolean('is_active')->default(true);
            $table->boolean('supports_international')->default(false);
            $table->boolean('supports_rial_exchange')->default(true);
            
            // Contact information
            $table->string('contact_phone', 20)->nullable();
            $table->string('contact_email', 100)->nullable();
            $table->string('website', 255)->nullable();
            
            // Address information
            $table->text('address')->nullable();
            $table->string('city', 50)->nullable();
            $table->string('province', 50)->nullable();
            $table->string('postal_code', 20)->nullable();
            
            // Additional information
            $table->year('established_year')->nullable();
            $table->string('logo_url', 255)->nullable();
            $table->text('notes')->nullable();
            
            // Audit fields
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes
            $table->index('bank_code');
            $table->index('swift_code');
            $table->index('is_active');
            $table->index('bank_type');
            $table->index(['supports_international', 'is_active']);
            $table->index(['supports_rial_exchange', 'is_active']);
            $table->index(['city', 'province']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('iranian_banks');
    }
};
