<?php

namespace App\Models\ArenaDoviz;

use App\Concerns\Blamable;
use App\Concerns\CompanyOwned;
use App\Enums\ArenaDoviz\CashDepositStatus;
use App\Models\ArenaDoviz\CurrencyExchange;
use App\Models\Common\Client;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class CashDeposit extends Model
{
    use Blamable;
    use CompanyOwned;
    use HasFactory;
    use SoftDeletes;

    protected $table = 'cash_deposits';

    protected $fillable = [
        'company_id',
        'currency_exchange_id',
        'client_id',
        'deposit_reference',
        'status',
        'currency_code',
        'amount',
        'denomination_breakdown',
        'deposit_date',
        'processed_date',
        'processed_by',
        'vault_location',
        'batch_number',
        'verification_notes',
        'photo_path',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'status' => CashDepositStatus::class,
        'amount' => 'decimal:2',
        'denomination_breakdown' => 'array',
        'deposit_date' => 'datetime',
        'processed_date' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($deposit) {
            if (empty($deposit->deposit_reference)) {
                $deposit->deposit_reference = static::generateDepositReference();
            }
        });
    }

    public function currencyExchange(): BelongsTo
    {
        return $this->belongsTo(CurrencyExchange::class);
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function processedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'processed_by');
    }

    public static function generateDepositReference(): string
    {
        $prefix = 'CD';
        $year = date('Y');
        $month = date('m');
        
        $lastDeposit = static::whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastDeposit ? 
            (int) substr($lastDeposit->deposit_reference, -6) + 1 : 1;

        return sprintf('%s%s%s%06d', $prefix, $year, $month, $sequence);
    }

    public function isPending(): bool
    {
        return $this->status === CashDepositStatus::PENDING;
    }

    public function isVerified(): bool
    {
        return $this->status === CashDepositStatus::VERIFIED;
    }

    public function isProcessed(): bool
    {
        return $this->status === CashDepositStatus::PROCESSED;
    }

    public function isRejected(): bool
    {
        return $this->status === CashDepositStatus::REJECTED;
    }

    public function canBeProcessed(): bool
    {
        return in_array($this->status, [CashDepositStatus::PENDING, CashDepositStatus::VERIFIED]);
    }

    public function getFormattedAmountAttribute(): string
    {
        return \App\Helpers\ArenaDoviz\NumberFormatter::formatByCurrency($this->amount, $this->currency_code);
    }

    public function getDenominationSummaryAttribute(): string
    {
        if (!$this->denomination_breakdown) {
            return 'No breakdown available';
        }

        $summary = [];
        foreach ($this->denomination_breakdown as $denomination => $count) {
            if ($count > 0) {
                $summary[] = "{$count} x {$denomination}";
            }
        }

        return implode(', ', $summary);
    }

    public function getTotalFromBreakdownAttribute(): float
    {
        if (!$this->denomination_breakdown) {
            return 0;
        }

        $total = 0;
        foreach ($this->denomination_breakdown as $denomination => $count) {
            $total += (float) $denomination * (int) $count;
        }

        return $total;
    }

    public function hasDiscrepancy(): bool
    {
        return abs($this->amount - $this->getTotalFromBreakdownAttribute()) > 0.01;
    }

    public function getDiscrepancyAmount(): float
    {
        return $this->amount - $this->getTotalFromBreakdownAttribute();
    }

    public function scopePending($query)
    {
        return $query->where('status', CashDepositStatus::PENDING);
    }

    public function scopeVerified($query)
    {
        return $query->where('status', CashDepositStatus::VERIFIED);
    }

    public function scopeProcessed($query)
    {
        return $query->where('status', CashDepositStatus::PROCESSED);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('deposit_date', today());
    }

    public function scopeByBatch($query, string $batchNumber)
    {
        return $query->where('batch_number', $batchNumber);
    }

    public function scopeByVault($query, string $vaultLocation)
    {
        return $query->where('vault_location', $vaultLocation);
    }

    public function scopeWithDiscrepancy($query)
    {
        return $query->whereRaw('ABS(amount - JSON_EXTRACT(denomination_breakdown, "$")) > 0.01');
    }

    public function scopeByCurrency($query, string $currencyCode)
    {
        return $query->where('currency_code', $currencyCode);
    }

    public function scopeRequiringVerification($query)
    {
        return $query->where('status', CashDepositStatus::PENDING)
                    ->whereNull('processed_by');
    }
}
