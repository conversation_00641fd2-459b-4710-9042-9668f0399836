<?php

namespace Database\Seeders\ArenaDoviz;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ExchangeRateHistorySeeder extends Seeder
{
    /**
     * Seed historical exchange rates for Arena Doviz operations.
     * 
     * Creates realistic exchange rate history including:
     * - Daily rate updates for major currencies
     * - Location-specific rates (Istanbul, Tabriz, Tehran, Dubai)
     * - Buy and sell rate spreads
     * - Rate volatility and trends
     * - Historical data for analytics and reporting
     */
    public function run(): void
    {
        $this->command->info('📈 Seeding Arena Doviz exchange rate history...');
        
        // Create exchange_rates table if it doesn't exist
        $this->createExchangeRatesTable();
        
        $currencies = [
            'USD' => ['base_rate' => 32.50, 'volatility' => 0.02],
            'EUR' => ['base_rate' => 35.20, 'volatility' => 0.025],
            'GBP' => ['base_rate' => 41.20, 'volatility' => 0.03],
            'AED' => ['base_rate' => 8.85, 'volatility' => 0.015],
            'IRR' => ['base_rate' => 0.0008, 'volatility' => 0.05],
            'CHF' => ['base_rate' => 36.80, 'volatility' => 0.02],
            'JPY' => ['base_rate' => 0.22, 'volatility' => 0.025],
        ];
        
        $locations = [
            1 => 'Istanbul',
            2 => 'Tabriz', 
            3 => 'Tehran',
            4 => 'Dubai',
            5 => 'Shanghai',
        ];
        
        // Generate rates for the last 12 months
        $startDate = Carbon::now()->subYear();
        $endDate = Carbon::now();
        
        $rateCount = 0;
        
        for ($date = $startDate->copy(); $date->lte($endDate); $date->addDay()) {
            // Skip weekends for most currencies (some weekend activity)
            if ($date->isWeekend() && rand(1, 100) > 30) {
                continue;
            }
            
            foreach ($currencies as $currencyCode => $config) {
                foreach ($locations as $locationId => $locationName) {
                    $this->createDailyRate($currencyCode, $locationId, $date, $config);
                    $rateCount++;
                }
            }
        }
        
        $this->command->info("✅ Created {$rateCount} exchange rate records");
    }
    
    private function createExchangeRatesTable(): void
    {
        if (DB::getSchemaBuilder()->hasTable('exchange_rates')) {
            return;
        }
        
        DB::statement("
            CREATE TABLE exchange_rates (
                id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
                currency_code VARCHAR(3) NOT NULL,
                location_id BIGINT UNSIGNED NOT NULL,
                buy_rate DECIMAL(15,6) NOT NULL,
                sell_rate DECIMAL(15,6) NOT NULL,
                effective_date DATE NOT NULL,
                created_by BIGINT UNSIGNED,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_rate (currency_code, location_id, effective_date),
                INDEX idx_currency_location (currency_code, location_id),
                INDEX idx_effective_date (effective_date),
                FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
            )
        ");
    }
    
    private function createDailyRate(string $currencyCode, int $locationId, Carbon $date, array $config): void
    {
        $baseRate = $config['base_rate'];
        $volatility = $config['volatility'];
        
        // Apply trend over time (simulate market movements)
        $daysSinceStart = $date->diffInDays(Carbon::now()->subYear());
        $trendFactor = 1 + (sin($daysSinceStart / 30) * 0.1); // Seasonal trend
        
        // Apply daily volatility
        $dailyChange = (rand(-100, 100) / 100) * $volatility;
        $volatilityFactor = 1 + $dailyChange;
        
        // Apply location-specific adjustments
        $locationFactor = $this->getLocationFactor($locationId, $currencyCode);
        
        // Calculate final rate
        $midRate = $baseRate * $trendFactor * $volatilityFactor * $locationFactor;
        
        // Calculate buy/sell spread (0.5% - 2.0%)
        $spread = rand(50, 200) / 10000; // 0.005 to 0.02
        $buyRate = $midRate * (1 - $spread / 2);
        $sellRate = $midRate * (1 + $spread / 2);
        
        // Special handling for IRR (higher spreads)
        if ($currencyCode === 'IRR') {
            $spread = rand(200, 500) / 10000; // 0.02 to 0.05
            $buyRate = $midRate * (1 - $spread / 2);
            $sellRate = $midRate * (1 + $spread / 2);
        }
        
        DB::table('exchange_rates')->updateOrInsert(
            [
                'currency_code' => $currencyCode,
                'location_id' => $locationId,
                'effective_date' => $date->format('Y-m-d'),
            ],
            [
                'buy_rate' => round($buyRate, 6),
                'sell_rate' => round($sellRate, 6),
                'created_by' => 1, // System user
                'created_at' => $date->format('Y-m-d H:i:s'),
                'updated_at' => $date->format('Y-m-d H:i:s'),
            ]
        );
    }
    
    private function getLocationFactor(int $locationId, string $currencyCode): float
    {
        // Location-specific rate adjustments based on local market conditions
        $factors = [
            1 => [ // Istanbul
                'USD' => 1.000,
                'EUR' => 1.000,
                'GBP' => 1.000,
                'AED' => 1.002, // Slightly higher due to trade relations
                'IRR' => 0.998, // Slightly lower due to proximity
                'CHF' => 1.001,
                'JPY' => 1.000,
            ],
            2 => [ // Tabriz
                'USD' => 0.995, // Lower due to regional factors
                'EUR' => 0.996,
                'GBP' => 0.994,
                'AED' => 1.005, // Higher due to regional trade
                'IRR' => 1.010, // Premium for local currency
                'CHF' => 0.997,
                'JPY' => 0.995,
            ],
            3 => [ // Tehran
                'USD' => 0.992, // Lower due to sanctions impact
                'EUR' => 0.994,
                'GBP' => 0.991,
                'AED' => 1.008, // Higher due to trade routes
                'IRR' => 1.015, // Premium in capital
                'CHF' => 0.995,
                'JPY' => 0.993,
            ],
            4 => [ // Dubai
                'USD' => 1.003, // Higher due to financial hub status
                'EUR' => 1.002,
                'GBP' => 1.004,
                'AED' => 0.995, // Lower for local currency
                'IRR' => 1.020, // Premium for Iranian currency
                'CHF' => 1.003,
                'JPY' => 1.002,
            ],
            5 => [ // Shanghai
                'USD' => 0.998, // Slightly lower
                'EUR' => 0.999,
                'GBP' => 0.997,
                'AED' => 1.001,
                'IRR' => 1.005,
                'CHF' => 0.999,
                'JPY' => 1.010, // Higher due to regional proximity
            ],
        ];
        
        return $factors[$locationId][$currencyCode] ?? 1.000;
    }
    
    private function createSpecialRateEvents(): void
    {
        // Create some special rate events (market shocks, holidays, etc.)
        $events = [
            [
                'date' => Carbon::now()->subMonths(6),
                'description' => 'Market volatility event',
                'impact' => 0.05, // 5% impact
                'currencies' => ['USD', 'EUR', 'GBP'],
            ],
            [
                'date' => Carbon::now()->subMonths(3),
                'description' => 'Central bank intervention',
                'impact' => -0.03, // -3% impact
                'currencies' => ['TRY'],
            ],
            [
                'date' => Carbon::now()->subMonth(),
                'description' => 'Holiday period adjustment',
                'impact' => 0.02, // 2% impact
                'currencies' => ['AED', 'IRR'],
            ],
        ];
        
        foreach ($events as $event) {
            $eventDate = $event['date'];
            $impact = $event['impact'];
            
            foreach ($event['currencies'] as $currencyCode) {
                // Apply event impact for a few days
                for ($i = 0; $i < 3; $i++) {
                    $date = $eventDate->copy()->addDays($i);
                    
                    // Get existing rates and apply impact
                    $existingRates = DB::table('exchange_rates')
                        ->where('currency_code', $currencyCode)
                        ->where('effective_date', $date->format('Y-m-d'))
                        ->get();
                    
                    foreach ($existingRates as $rate) {
                        $newBuyRate = $rate->buy_rate * (1 + $impact);
                        $newSellRate = $rate->sell_rate * (1 + $impact);
                        
                        DB::table('exchange_rates')
                            ->where('id', $rate->id)
                            ->update([
                                'buy_rate' => round($newBuyRate, 6),
                                'sell_rate' => round($newSellRate, 6),
                                'updated_at' => now(),
                            ]);
                    }
                }
            }
        }
    }
}
