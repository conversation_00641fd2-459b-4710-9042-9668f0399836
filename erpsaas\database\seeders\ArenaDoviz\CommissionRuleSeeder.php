<?php

namespace Database\Seeders\ArenaDoviz;

use App\Models\ArenaDoviz\CommissionRule;
use Illuminate\Database\Seeder;

class CommissionRuleSeeder extends Seeder
{
    /**
     * Seed default commission rules for Arena Doviz operations.
     */
    public function run(): void
    {
        $this->command->info('💰 Seeding Arena Doviz commission rules...');
        
        $rules = [
            // Standard Buy Commission Rules
            [
                'rule_name' => 'Standard Buy Commission',
                'exchange_type' => 'buy',
                'commission_type' => 'percentage',
                'commission_rate' => 0.25,
                'calculation_method' => 'post_conversion',
                'priority' => 1,
                'is_active' => true,
            ],
            
            // Standard Sell Commission Rules
            [
                'rule_name' => 'Standard Sell Commission',
                'exchange_type' => 'sell',
                'commission_type' => 'percentage',
                'commission_rate' => 0.25,
                'calculation_method' => 'post_conversion',
                'priority' => 1,
                'is_active' => true,
            ],
            
            // Transfer Commission Rules
            [
                'rule_name' => 'Transfer Commission',
                'exchange_type' => 'transfer',
                'commission_type' => 'fixed',
                'fixed_amount' => 50.00,
                'commission_currency' => 'TRY_IST',
                'calculation_method' => 'post_conversion',
                'priority' => 1,
                'is_active' => true,
            ],
            
            // Cash Deposit Commission
            [
                'rule_name' => 'Cash Deposit Commission',
                'exchange_type' => 'cash_deposit',
                'commission_type' => 'percentage',
                'commission_rate' => 0.15,
                'calculation_method' => 'post_conversion',
                'priority' => 1,
                'is_active' => true,
            ],
            
            // High Volume Discount (Tiered)
            [
                'rule_name' => 'High Volume Discount',
                'commission_type' => 'tiered',
                'tier_rules' => [
                    ['min_amount' => 0, 'max_amount' => 10000, 'rate' => 0.5],
                    ['min_amount' => 10000, 'max_amount' => 50000, 'rate' => 0.3],
                    ['min_amount' => 50000, 'max_amount' => 100000, 'rate' => 0.2],
                    ['min_amount' => 100000, 'max_amount' => PHP_FLOAT_MAX, 'rate' => 0.15],
                ],
                'calculation_method' => 'post_conversion',
                'priority' => 2,
                'is_active' => true,
            ],
            
            // VIP Client Commission (Lower rates)
            [
                'rule_name' => 'VIP Client Commission',
                'commission_type' => 'percentage',
                'commission_rate' => 0.15,
                'calculation_method' => 'post_conversion',
                'priority' => 3,
                'min_commission' => 25.00,
                'is_active' => true,
                'notes' => 'Special rate for VIP clients with high transaction volumes',
            ],
            
            // Iranian Banking Commission
            [
                'rule_name' => 'Iranian Banking Commission',
                'from_currency_code' => 'TRY_IST',
                'to_currency_code' => 'IRR_TBZ',
                'commission_type' => 'hybrid',
                'commission_rate' => 0.20,
                'fixed_amount' => 100.00,
                'commission_currency' => 'TRY_IST',
                'calculation_method' => 'post_conversion',
                'priority' => 2,
                'is_active' => true,
            ],
            
            // Cross-Border Transfer Commission
            [
                'rule_name' => 'Cross-Border Transfer',
                'exchange_type' => 'transfer',
                'commission_type' => 'percentage',
                'commission_rate' => 0.75,
                'min_commission' => 100.00,
                'max_commission' => 1000.00,
                'calculation_method' => 'post_conversion',
                'priority' => 2,
                'is_active' => true,
            ],
            
            // Weekend Premium Commission
            [
                'rule_name' => 'Weekend Premium',
                'commission_type' => 'percentage',
                'commission_rate' => 0.35,
                'calculation_method' => 'post_conversion',
                'priority' => 4,
                'valid_from' => now()->startOfWeek()->addDays(5), // Saturday
                'valid_until' => now()->startOfWeek()->addDays(6), // Sunday
                'is_active' => true,
                'notes' => 'Higher commission for weekend transactions',
            ],
            
            // Large Transaction Commission
            [
                'rule_name' => 'Large Transaction Discount',
                'commission_type' => 'percentage',
                'commission_rate' => 0.10,
                'calculation_method' => 'post_conversion',
                'priority' => 5,
                'min_commission' => 500.00,
                'is_active' => true,
                'notes' => 'Special rate for transactions above 100,000 units',
            ],
            
            // Emergency Service Commission
            [
                'rule_name' => 'Emergency Service Premium',
                'commission_type' => 'hybrid',
                'commission_rate' => 0.50,
                'fixed_amount' => 200.00,
                'calculation_method' => 'post_conversion',
                'priority' => 6,
                'is_active' => false, // Activated only when needed
                'notes' => 'Premium rate for urgent/emergency transactions',
            ],
            
            // Combined Exchange Commission
            [
                'rule_name' => 'Combined Exchange Commission',
                'exchange_type' => 'combined_exchange',
                'commission_type' => 'percentage',
                'commission_rate' => 0.40,
                'calculation_method' => 'post_conversion',
                'priority' => 1,
                'is_active' => true,
                'notes' => 'Commission for combined buy/sell transactions',
            ],
        ];
        
        foreach ($rules as $ruleData) {
            CommissionRule::updateOrCreate(
                [
                    'rule_name' => $ruleData['rule_name'],
                    'company_id' => 1,
                ],
                array_merge($ruleData, [
                    'company_id' => 1,
                    'created_by' => 1,
                ])
            );
            
            $this->command->info("Created: {$ruleData['rule_name']}");
        }
        
        $this->command->info('✅ Commission rules seeded successfully!');
    }
}
