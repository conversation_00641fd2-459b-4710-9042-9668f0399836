<?php

namespace App\Console\Commands\ArenaDoviz;

use App\Services\ArenaDoviz\ExportService;
use Illuminate\Console\Command;

class ExportDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'arena-doviz:export 
                            {type : Export type (exchanges, rates, profit, debit-notes, all)}
                            {--format=xlsx : Export format (xlsx, csv)}
                            {--date-from= : Start date filter (Y-m-d)}
                            {--date-to= : End date filter (Y-m-d)}
                            {--status= : Status filter}
                            {--location= : Location ID filter}';

    /**
     * The console command description.
     */
    protected $description = 'Export Arena Doviz data to Excel or CSV files';

    /**
     * Execute the console command.
     */
    public function handle(ExportService $exportService)
    {
        $this->info('📊 Arena Doviz Data Export');
        $this->info('==========================');

        $type = $this->argument('type');
        $format = $this->option('format');
        
        // Build filters
        $filters = array_filter([
            'date_from' => $this->option('date-from'),
            'date_to' => $this->option('date-to'),
            'status' => $this->option('status'),
            'location_id' => $this->option('location'),
        ]);

        $exported = [];

        try {
            if ($type === 'all' || $type === 'exchanges') {
                $this->info('💱 Exporting currency exchanges...');
                $path = $exportService->exportCurrencyExchanges($filters, $format);
                $url = $exportService->getExportUrl($path);
                $exported[] = ['Type' => 'Currency Exchanges', 'Path' => $path, 'URL' => $url];
                $this->info("✅ Currency exchanges exported: {$path}");
            }

            if ($type === 'all' || $type === 'rates') {
                $this->info('📈 Exporting currency rates...');
                $path = $exportService->exportLocationCurrencies($format);
                $url = $exportService->getExportUrl($path);
                $exported[] = ['Type' => 'Currency Rates', 'Path' => $path, 'URL' => $url];
                $this->info("✅ Currency rates exported: {$path}");
            }

            if ($type === 'all' || $type === 'profit') {
                $this->info('💰 Exporting profit analysis...');
                $path = $exportService->exportProfitAnalysis($filters, $format);
                $url = $exportService->getExportUrl($path);
                $exported[] = ['Type' => 'Profit Analysis', 'Path' => $path, 'URL' => $url];
                $this->info("✅ Profit analysis exported: {$path}");
            }

            if ($type === 'all' || $type === 'debit-notes') {
                $this->info('📋 Exporting debit notes...');
                $path = $exportService->exportDebitNotes($filters, $format);
                $url = $exportService->getExportUrl($path);
                $exported[] = ['Type' => 'Debit Notes', 'Path' => $path, 'URL' => $url];
                $this->info("✅ Debit notes exported: {$path}");
            }

            if (empty($exported)) {
                $this->error('❌ Invalid export type. Use: exchanges, rates, profit, debit-notes, or all');
                return 1;
            }

            $this->info('');
            $this->info('📋 Export Summary');
            $this->info('=================');
            $this->table(['Type', 'File Path'], array_map(function($item) {
                return [$item['Type'], $item['Path']];
            }, $exported));

            if (!empty($filters)) {
                $this->info('');
                $this->info('🔍 Applied Filters:');
                foreach ($filters as $key => $value) {
                    $this->info("  • {$key}: {$value}");
                }
            }

            $this->info('');
            $this->info('💡 Files are saved in the storage/app/exports/arena-doviz/ directory');
            $this->info('   You can access them through the web interface or download directly');

        } catch (\Exception $e) {
            $this->error('❌ Export failed: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
