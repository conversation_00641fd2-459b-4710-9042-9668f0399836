<?php

namespace App\Services\ArenaDoviz;

use App\Models\ArenaDoviz\LocationCurrency;
use App\Models\ArenaDoviz\Location;
use App\Models\ArenaDoviz\CommissionRule;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use PhpOffice\PhpSpreadsheet\IOFactory;

class ImportService
{
    /**
     * Import currency rates from Excel/CSV file
     */
    public function importCurrencyRates(UploadedFile $file, int $companyId): array
    {
        $results = [
            'success' => 0,
            'errors' => 0,
            'skipped' => 0,
            'messages' => [],
        ];

        try {
            $spreadsheet = IOFactory::load($file->getPathname());
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();

            // Skip header row
            array_shift($rows);

            DB::beginTransaction();

            foreach ($rows as $index => $row) {
                $rowNumber = $index + 2; // +2 because we skipped header and arrays are 0-indexed
                
                try {
                    $this->processRateRow($row, $companyId, $rowNumber, $results);
                } catch (\Exception $e) {
                    $results['errors']++;
                    $results['messages'][] = "Row {$rowNumber}: " . $e->getMessage();
                    Log::error("Import error on row {$rowNumber}", [
                        'error' => $e->getMessage(),
                        'row_data' => $row,
                    ]);
                }
            }

            DB::commit();
            
        } catch (\Exception $e) {
            DB::rollBack();
            $results['messages'][] = 'Import failed: ' . $e->getMessage();
            Log::error('Currency rates import failed', ['error' => $e->getMessage()]);
        }

        return $results;
    }

    /**
     * Process a single rate row
     */
    private function processRateRow(array $row, int $companyId, int $rowNumber, array &$results): void
    {
        // Expected columns: Currency Code, Location, Buy Rate, Sell Rate, Mid Rate, Active
        if (count($row) < 6) {
            throw new \Exception('Insufficient columns in row');
        }

        $currencyCode = trim($row[0]);
        $locationName = trim($row[1]);
        $buyRate = $this->parseDecimal($row[2]);
        $sellRate = $this->parseDecimal($row[3]);
        $midRate = $this->parseDecimal($row[4]);
        $isActive = $this->parseBoolean($row[5]);

        // Validate required fields
        if (empty($currencyCode) || empty($locationName)) {
            throw new \Exception('Currency code and location are required');
        }

        if ($buyRate <= 0 || $sellRate <= 0) {
            throw new \Exception('Buy rate and sell rate must be greater than 0');
        }

        // Find location
        $location = Location::where('company_id', $companyId)
            ->where('name', $locationName)
            ->first();

        if (!$location) {
            throw new \Exception("Location '{$locationName}' not found");
        }

        // Find or create location currency
        $locationCurrency = LocationCurrency::where('company_id', $companyId)
            ->where('location_currency_code', $currencyCode)
            ->first();

        if ($locationCurrency) {
            // Update existing
            $locationCurrency->update([
                'buy_rate' => $buyRate,
                'sell_rate' => $sellRate,
                'mid_rate' => $midRate ?: (($buyRate + $sellRate) / 2),
                'is_active' => $isActive,
                'rate_date' => now(),
                'last_updated' => now(),
                'rate_source' => 'import',
            ]);
            
            $results['success']++;
            $results['messages'][] = "Row {$rowNumber}: Updated {$currencyCode}";
        } else {
            // Create new (requires more data)
            $results['skipped']++;
            $results['messages'][] = "Row {$rowNumber}: Skipped {$currencyCode} (currency not found, use full import template)";
        }
    }

    /**
     * Import commission rules from Excel/CSV file
     */
    public function importCommissionRules(UploadedFile $file, int $companyId): array
    {
        $results = [
            'success' => 0,
            'errors' => 0,
            'skipped' => 0,
            'messages' => [],
        ];

        try {
            $spreadsheet = IOFactory::load($file->getPathname());
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();

            // Skip header row
            array_shift($rows);

            DB::beginTransaction();

            foreach ($rows as $index => $row) {
                $rowNumber = $index + 2;
                
                try {
                    $this->processCommissionRuleRow($row, $companyId, $rowNumber, $results);
                } catch (\Exception $e) {
                    $results['errors']++;
                    $results['messages'][] = "Row {$rowNumber}: " . $e->getMessage();
                }
            }

            DB::commit();
            
        } catch (\Exception $e) {
            DB::rollBack();
            $results['messages'][] = 'Import failed: ' . $e->getMessage();
        }

        return $results;
    }

    /**
     * Process a single commission rule row
     */
    private function processCommissionRuleRow(array $row, int $companyId, int $rowNumber, array &$results): void
    {
        // Expected columns: Rule Name, Exchange Type, Commission Type, Rate, Fixed Amount, Active, Priority
        if (count($row) < 7) {
            throw new \Exception('Insufficient columns in row');
        }

        $ruleName = trim($row[0]);
        $exchangeType = trim($row[1]);
        $commissionType = trim($row[2]);
        $commissionRate = $this->parseDecimal($row[3]);
        $fixedAmount = $this->parseDecimal($row[4]);
        $isActive = $this->parseBoolean($row[5]);
        $priority = $this->parseInt($row[6]);

        // Validate required fields
        if (empty($ruleName) || empty($commissionType)) {
            throw new \Exception('Rule name and commission type are required');
        }

        // Validate commission type
        $validTypes = ['percentage', 'fixed', 'tiered', 'hybrid'];
        if (!in_array($commissionType, $validTypes)) {
            throw new \Exception("Invalid commission type: {$commissionType}");
        }

        // Create or update commission rule
        $rule = CommissionRule::updateOrCreate(
            [
                'company_id' => $companyId,
                'rule_name' => $ruleName,
            ],
            [
                'exchange_type' => $exchangeType ?: 'all',
                'commission_type' => $commissionType,
                'commission_rate' => $commissionRate,
                'fixed_amount' => $fixedAmount,
                'is_active' => $isActive,
                'priority' => $priority ?: 1,
                'calculation_method' => 'post_conversion',
            ]
        );

        $results['success']++;
        $results['messages'][] = "Row {$rowNumber}: Processed rule '{$ruleName}'";
    }

    /**
     * Generate import template for currency rates
     */
    public function generateRatesTemplate(): string
    {
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('Currency Rates Template');

        // Headers
        $headers = [
            'Currency Code', 'Location', 'Buy Rate', 'Sell Rate', 'Mid Rate', 'Active'
        ];
        $sheet->fromArray($headers, null, 'A1');

        // Sample data
        $sampleData = [
            ['USD_IST', 'Istanbul Office', '32.15', '32.25', '32.20', 'Yes'],
            ['EUR_IST', 'Istanbul Office', '34.80', '34.90', '34.85', 'Yes'],
            ['USD_TBZ', 'Tabriz Branch', '32.18', '32.28', '32.23', 'Yes'],
        ];

        $row = 2;
        foreach ($sampleData as $data) {
            $sheet->fromArray($data, null, "A{$row}");
            $row++;
        }

        // Auto-size columns
        foreach (range('A', 'F') as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }

        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $filename = 'currency_rates_template_' . now()->format('Y-m-d') . '.xlsx';
        $path = storage_path("app/templates/{$filename}");
        
        // Ensure directory exists
        if (!file_exists(dirname($path))) {
            mkdir(dirname($path), 0755, true);
        }
        
        $writer->save($path);

        return $path;
    }

    /**
     * Generate import template for commission rules
     */
    public function generateCommissionRulesTemplate(): string
    {
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('Commission Rules Template');

        // Headers
        $headers = [
            'Rule Name', 'Exchange Type', 'Commission Type', 'Rate (%)', 'Fixed Amount', 'Active', 'Priority'
        ];
        $sheet->fromArray($headers, null, 'A1');

        // Sample data
        $sampleData = [
            ['Standard Buy Commission', 'buy', 'percentage', '0.5', '', 'Yes', '1'],
            ['Standard Sell Commission', 'sell', 'percentage', '0.5', '', 'Yes', '1'],
            ['High Volume Discount', 'all', 'tiered', '', '', 'Yes', '5'],
            ['Fixed Service Fee', 'transfer', 'fixed', '', '25.00', 'Yes', '3'],
        ];

        $row = 2;
        foreach ($sampleData as $data) {
            $sheet->fromArray($data, null, "A{$row}");
            $row++;
        }

        // Auto-size columns
        foreach (range('A', 'G') as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }

        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $filename = 'commission_rules_template_' . now()->format('Y-m-d') . '.xlsx';
        $path = storage_path("app/templates/{$filename}");
        
        // Ensure directory exists
        if (!file_exists(dirname($path))) {
            mkdir(dirname($path), 0755, true);
        }
        
        $writer->save($path);

        return $path;
    }

    /**
     * Parse decimal value from string
     */
    private function parseDecimal(?string $value): float
    {
        if (empty($value)) {
            return 0.0;
        }
        
        return (float) str_replace(',', '', $value);
    }

    /**
     * Parse boolean value from string
     */
    private function parseBoolean(?string $value): bool
    {
        if (empty($value)) {
            return true;
        }
        
        $value = strtolower(trim($value));
        return in_array($value, ['yes', 'true', '1', 'active', 'enabled']);
    }

    /**
     * Parse integer value from string
     */
    private function parseInt(?string $value): int
    {
        if (empty($value)) {
            return 1;
        }
        
        return (int) $value;
    }

    /**
     * Validate import file
     */
    public function validateImportFile(UploadedFile $file): array
    {
        $errors = [];

        // Check file extension
        $allowedExtensions = ['xlsx', 'xls', 'csv'];
        $extension = $file->getClientOriginalExtension();
        
        if (!in_array(strtolower($extension), $allowedExtensions)) {
            $errors[] = 'File must be Excel (.xlsx, .xls) or CSV (.csv) format';
        }

        // Check file size (max 10MB)
        if ($file->getSize() > 10 * 1024 * 1024) {
            $errors[] = 'File size must be less than 10MB';
        }

        // Try to read the file
        try {
            $spreadsheet = IOFactory::load($file->getPathname());
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();
            
            if (count($rows) < 2) {
                $errors[] = 'File must contain at least one data row (plus header)';
            }
        } catch (\Exception $e) {
            $errors[] = 'Unable to read file: ' . $e->getMessage();
        }

        return $errors;
    }
}
