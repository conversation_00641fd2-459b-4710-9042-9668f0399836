<?php

namespace App\Filament\Company\Resources\ArenaDoviz;

use App\Filament\Company\Resources\ArenaDoviz\LocationResource\Pages;
use App\Models\ArenaDoviz\Location;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class LocationResource extends Resource
{
    protected static ?string $model = Location::class;

    protected static ?string $navigationIcon = 'heroicon-o-map-pin';

    protected static ?string $navigationLabel = 'Locations';

    protected static ?string $modelLabel = 'Location';

    protected static ?string $pluralModelLabel = 'Locations';

    protected static ?string $navigationGroup = 'Arena Doviz';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Location Details')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Location Name')
                            ->required()
                            ->maxLength(100),

                        Forms\Components\TextInput::make('code')
                            ->label('Location Code')
                            ->required()
                            ->maxLength(10)
                            ->unique(ignoreRecord: true)
                            ->helperText('Unique code like IST, TBZ, THR, DXB'),

                        Forms\Components\TextInput::make('city')
                            ->label('City')
                            ->required()
                            ->maxLength(100),

                        Forms\Components\TextInput::make('country')
                            ->label('Country')
                            ->required()
                            ->maxLength(100),

                        Forms\Components\Select::make('timezone')
                            ->label('Timezone')
                            ->options([
                                'Europe/Istanbul' => 'Europe/Istanbul (Turkey)',
                                'Asia/Tehran' => 'Asia/Tehran (Iran)',
                                'Asia/Dubai' => 'Asia/Dubai (UAE)',
                                'Asia/Shanghai' => 'Asia/Shanghai (China)',
                                'UTC' => 'UTC',
                            ])
                            ->required()
                            ->default('UTC'),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true),
                    ])->columns(2),

                Forms\Components\Section::make('Contact Information')
                    ->schema([
                        Forms\Components\Textarea::make('address')
                            ->label('Address')
                            ->rows(3)
                            ->columnSpanFull(),

                        Forms\Components\TextInput::make('phone')
                            ->label('Phone')
                            ->tel()
                            ->maxLength(20),

                        Forms\Components\TextInput::make('email')
                            ->label('Email')
                            ->email()
                            ->maxLength(100),

                        Forms\Components\TextInput::make('manager_name')
                            ->label('Manager Name')
                            ->maxLength(100),
                    ])->columns(2),

                Forms\Components\Section::make('Working Hours')
                    ->schema([
                        Forms\Components\KeyValue::make('working_hours')
                            ->label('Working Hours')
                            ->keyLabel('Day')
                            ->valueLabel('Hours (JSON format)')
                            ->helperText('Example: {"open": "09:00", "close": "18:00"} or {"closed": true}')
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Services & Limits')
                    ->schema([
                        Forms\Components\CheckboxList::make('services')
                            ->label('Available Services')
                            ->options([
                                'currency_exchange' => 'Currency Exchange',
                                'money_transfer' => 'Money Transfer',
                                'cash_deposit' => 'Cash Deposit',
                                'delivery' => 'Delivery Service',
                                'iranian_banking' => 'Iranian Banking',
                                'swift_transfer' => 'SWIFT Transfer',
                            ])
                            ->columns(2),

                        Forms\Components\TextInput::make('max_transaction_amount')
                            ->label('Maximum Transaction Amount')
                            ->numeric()
                            ->step(0.01)
                            ->helperText('Maximum amount for a single transaction'),
                    ])->columns(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Location Name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('code')
                    ->label('Code')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color('primary'),

                Tables\Columns\TextColumn::make('city')
                    ->label('City')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('country')
                    ->label('Country')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('manager_name')
                    ->label('Manager')
                    ->searchable()
                    ->limit(30),

                Tables\Columns\TextColumn::make('phone')
                    ->label('Phone')
                    ->searchable(),

                Tables\Columns\TextColumn::make('services')
                    ->label('Services')
                    ->badge()
                    ->separator(',')
                    ->limit(3),

                Tables\Columns\TextColumn::make('max_transaction_amount')
                    ->label('Max Transaction')
                    ->money()
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active'),

                Tables\Filters\SelectFilter::make('country')
                    ->options([
                        'Turkey' => 'Turkey',
                        'Iran' => 'Iran',
                        'UAE' => 'UAE',
                        'China' => 'China',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('name');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLocations::route('/'),
            'create' => Pages\CreateLocation::route('/create'),
            'view' => Pages\ViewLocation::route('/{record}'),
            'edit' => Pages\EditLocation::route('/{record}/edit'),
        ];
    }
}
