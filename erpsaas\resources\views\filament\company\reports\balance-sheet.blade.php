<x-filament::section>
    <x-slot name="heading">
        Balance Sheet Report
    </x-slot>
    
    @if(isset($data['error']))
        <div class="text-center py-8">
            <div class="text-red-500 text-lg font-semibold">Error generating balance sheet</div>
            <div class="text-gray-600 mt-2">{{ $data['error'] }}</div>
        </div>
    @elseif(isset($data['assets']) || isset($data['liabilities']) || isset($data['equity']))
        <div class="space-y-6">
            <!-- Assets Section -->
            @if(isset($data['assets']))
                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Assets</h3>
                    </div>
                    <div class="p-6">
                        <div class="overflow-x-auto">
                            <table class="w-full text-sm">
                                <thead>
                                    <tr class="border-b border-gray-200 dark:border-gray-700">
                                        <th class="text-left py-2 font-medium text-gray-900 dark:text-white">Account</th>
                                        <th class="text-right py-2 font-medium text-gray-900 dark:text-white">Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($data['assets'] as $asset)
                                        <tr class="border-b border-gray-100 dark:border-gray-700">
                                            <td class="py-2 text-gray-700 dark:text-gray-300">{{ $asset['name'] ?? 'Unknown' }}</td>
                                            <td class="py-2 text-right text-gray-900 dark:text-white font-mono">
                                                {{ number_format($asset['amount'] ?? 0, 2) }}
                                            </td>
                                        </tr>
                                    @endforeach
                                    <tr class="border-t-2 border-gray-300 dark:border-gray-600 font-semibold">
                                        <td class="py-2 text-gray-900 dark:text-white">Total Assets</td>
                                        <td class="py-2 text-right text-gray-900 dark:text-white font-mono">
                                            {{ number_format($data['total_assets'] ?? 0, 2) }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Liabilities Section -->
            @if(isset($data['liabilities']))
                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Liabilities</h3>
                    </div>
                    <div class="p-6">
                        <div class="overflow-x-auto">
                            <table class="w-full text-sm">
                                <thead>
                                    <tr class="border-b border-gray-200 dark:border-gray-700">
                                        <th class="text-left py-2 font-medium text-gray-900 dark:text-white">Account</th>
                                        <th class="text-right py-2 font-medium text-gray-900 dark:text-white">Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($data['liabilities'] as $liability)
                                        <tr class="border-b border-gray-100 dark:border-gray-700">
                                            <td class="py-2 text-gray-700 dark:text-gray-300">{{ $liability['name'] ?? 'Unknown' }}</td>
                                            <td class="py-2 text-right text-gray-900 dark:text-white font-mono">
                                                {{ number_format($liability['amount'] ?? 0, 2) }}
                                            </td>
                                        </tr>
                                    @endforeach
                                    <tr class="border-t-2 border-gray-300 dark:border-gray-600 font-semibold">
                                        <td class="py-2 text-gray-900 dark:text-white">Total Liabilities</td>
                                        <td class="py-2 text-right text-gray-900 dark:text-white font-mono">
                                            {{ number_format($data['total_liabilities'] ?? 0, 2) }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Equity Section -->
            @if(isset($data['equity']))
                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Equity</h3>
                    </div>
                    <div class="p-6">
                        <div class="overflow-x-auto">
                            <table class="w-full text-sm">
                                <thead>
                                    <tr class="border-b border-gray-200 dark:border-gray-700">
                                        <th class="text-left py-2 font-medium text-gray-900 dark:text-white">Account</th>
                                        <th class="text-right py-2 font-medium text-gray-900 dark:text-white">Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($data['equity'] as $equity)
                                        <tr class="border-b border-gray-100 dark:border-gray-700">
                                            <td class="py-2 text-gray-700 dark:text-gray-300">{{ $equity['name'] ?? 'Unknown' }}</td>
                                            <td class="py-2 text-right text-gray-900 dark:text-white font-mono">
                                                {{ number_format($equity['amount'] ?? 0, 2) }}
                                            </td>
                                        </tr>
                                    @endforeach
                                    <tr class="border-t-2 border-gray-300 dark:border-gray-600 font-semibold">
                                        <td class="py-2 text-gray-900 dark:text-white">Total Equity</td>
                                        <td class="py-2 text-right text-gray-900 dark:text-white font-mono">
                                            {{ number_format($data['total_equity'] ?? 0, 2) }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Summary -->
            <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800 p-6">
                <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-4">Balance Sheet Summary</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                            {{ number_format($data['total_assets'] ?? 0, 2) }}
                        </div>
                        <div class="text-sm text-blue-700 dark:text-blue-300">Total Assets</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-red-600 dark:text-red-400">
                            {{ number_format($data['total_liabilities'] ?? 0, 2) }}
                        </div>
                        <div class="text-sm text-red-700 dark:text-red-300">Total Liabilities</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                            {{ number_format($data['total_equity'] ?? 0, 2) }}
                        </div>
                        <div class="text-sm text-green-700 dark:text-green-300">Total Equity</div>
                    </div>
                </div>
            </div>
        </div>
    @else
        <div class="text-center py-8">
            <div class="text-gray-500 text-lg">No balance sheet data available for the selected period</div>
        </div>
    @endif
</x-filament::section>
