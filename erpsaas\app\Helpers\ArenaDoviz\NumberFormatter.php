<?php

namespace App\Helpers\ArenaDoviz;

class NumberFormatter
{
    /**
     * Currency symbols mapping
     */
    private static array $currencySymbols = [
        'USD' => '$',
        'EUR' => '€',
        'TRY' => '₺',
        'IRR' => '﷼',
        'AED' => 'د.إ',
        'CNY' => '¥',
        'GBP' => '£',
        'JPY' => '¥',
        'CHF' => 'CHF',
        'CAD' => 'C$',
        'AUD' => 'A$',
    ];

    /**
     * Format number with thousand separators
     *
     * @param float|int|string|null $value
     * @param int $decimals
     * @param string $thousandSep
     * @param string $decimalSep
     * @return string
     */
    public static function formatNumber($value, int $decimals = 2, string $thousandSep = ',', string $decimalSep = '.'): string
    {
        if ($value === null || $value === '' || !is_numeric($value)) {
            return '';
        }

        return number_format((float) $value, $decimals, $decimalSep, $thousandSep);
    }

    /**
     * Format currency amount with symbol
     *
     * @param float|int|string|null $value
     * @param string $currencyCode
     * @param int $decimals
     * @param bool $symbolFirst
     * @return string
     */
    public static function formatCurrency($value, string $currencyCode = 'TRY', int $decimals = 2, bool $symbolFirst = false): string
    {
        $formatted = self::formatNumber($value, $decimals);
        
        if (!$formatted) {
            return '';
        }

        // Extract base currency from location-based code (e.g., USD_IST -> USD)
        $baseCurrency = self::extractBaseCurrency($currencyCode);
        $symbol = self::getCurrencySymbol($baseCurrency);

        return $symbolFirst 
            ? "{$symbol} {$formatted}"
            : "{$formatted} {$symbol}";
    }

    /**
     * Format amount for display in tables and lists
     *
     * @param float|int|string|null $value
     * @param string $currencyCode
     * @param int $decimals
     * @return string
     */
    public static function formatDisplayAmount($value, string $currencyCode = 'TRY', int $decimals = 2): string
    {
        return self::formatCurrency($value, $currencyCode, $decimals, false);
    }

    /**
     * Format large amounts with K, M, B suffixes
     *
     * @param float|int|string|null $value
     * @param int $decimals
     * @return string
     */
    public static function formatCompactNumber($value, int $decimals = 1): string
    {
        if ($value === null || $value === '' || !is_numeric($value)) {
            return '';
        }

        $value = (float) $value;
        $absValue = abs($value);

        if ($absValue >= 1000000000) {
            return self::formatNumber($value / 1000000000, $decimals) . 'B';
        } elseif ($absValue >= 1000000) {
            return self::formatNumber($value / 1000000, $decimals) . 'M';
        } elseif ($absValue >= 1000) {
            return self::formatNumber($value / 1000, $decimals) . 'K';
        }

        return self::formatNumber($value, $decimals);
    }

    /**
     * Parse formatted number string back to float
     *
     * @param string $formattedValue
     * @return float
     */
    public static function parseNumber(string $formattedValue): float
    {
        if (empty($formattedValue)) {
            return 0.0;
        }

        // Remove thousand separators and currency symbols
        $cleaned = preg_replace('/[^\d.-]/', '', $formattedValue);
        
        return (float) $cleaned;
    }

    /**
     * Get currency symbol for a currency code
     *
     * @param string $currencyCode
     * @return string
     */
    public static function getCurrencySymbol(string $currencyCode): string
    {
        $baseCurrency = self::extractBaseCurrency($currencyCode);
        return self::$currencySymbols[$baseCurrency] ?? $baseCurrency;
    }

    /**
     * Extract base currency from location-based currency code
     *
     * @param string $currencyCode
     * @return string
     */
    public static function extractBaseCurrency(string $currencyCode): string
    {
        // Handle location-based codes like USD_IST, EUR_TBZ
        return explode('_', $currencyCode)[0];
    }

    /**
     * Format exchange rate with proper precision
     *
     * @param float|int|string|null $rate
     * @param int $decimals
     * @return string
     */
    public static function formatExchangeRate($rate, int $decimals = 6): string
    {
        return self::formatNumber($rate, $decimals);
    }

    /**
     * Format percentage with % symbol
     *
     * @param float|int|string|null $value
     * @param int $decimals
     * @return string
     */
    public static function formatPercentage($value, int $decimals = 2): string
    {
        $formatted = self::formatNumber($value, $decimals);
        return $formatted ? "{$formatted}%" : '';
    }

    /**
     * Format amount for Iranian Rial (no decimals)
     *
     * @param float|int|string|null $value
     * @return string
     */
    public static function formatIranianRial($value): string
    {
        return self::formatCurrency($value, 'IRR', 0, false);
    }

    /**
     * Format amount based on currency type
     *
     * @param float|int|string|null $value
     * @param string $currencyCode
     * @return string
     */
    public static function formatByCurrency($value, string $currencyCode): string
    {
        $baseCurrency = self::extractBaseCurrency($currencyCode);
        
        // Iranian Rial doesn't use decimals
        $decimals = $baseCurrency === 'IRR' ? 0 : 2;
        
        return self::formatCurrency($value, $currencyCode, $decimals);
    }

    /**
     * Get appropriate decimal places for currency
     *
     * @param string $currencyCode
     * @return int
     */
    public static function getDecimalPlaces(string $currencyCode): int
    {
        $baseCurrency = self::extractBaseCurrency($currencyCode);
        
        // Currencies that don't use decimal places
        $noDecimalCurrencies = ['IRR', 'JPY', 'KRW'];
        
        return in_array($baseCurrency, $noDecimalCurrencies) ? 0 : 2;
    }

    /**
     * Format amount for input fields (with data attributes)
     *
     * @param float|int|string|null $value
     * @param string $currencyCode
     * @return array
     */
    public static function formatForInput($value, string $currencyCode = 'TRY'): array
    {
        $decimals = self::getDecimalPlaces($currencyCode);
        $formatted = self::formatNumber($value, $decimals);
        
        return [
            'value' => $formatted,
            'currency' => $currencyCode,
            'decimals' => $decimals,
            'symbol' => self::getCurrencySymbol($currencyCode),
        ];
    }

    /**
     * Validate if a string is a properly formatted number
     *
     * @param string $value
     * @return bool
     */
    public static function isValidFormattedNumber(string $value): bool
    {
        // Allow numbers with commas as thousand separators and dots as decimal separators
        return preg_match('/^\d{1,3}(,\d{3})*(\.\d+)?$/', $value) === 1;
    }
}
