class r{constructor(){this.isConnected=!1,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectDelay=1e3,this.rateElements=new Map,this.lastRates=new Map,this.init()}init(){this.setupWebSocket(),this.setupRateElements(),this.setupNotifications(),this.startHeartbeat()}setupWebSocket(){this.startPolling()}startPolling(){setInterval(()=>{this.fetchLatestRates()},3e4),this.fetchLatestRates()}async fetchLatestRates(){try{const t=await fetch("/api/arena-doviz/rates/latest",{headers:{Accept:"application/json","X-Requested-With":"XMLHttpRequest"}});if(t.ok){const e=await t.json();this.handleRateUpdates(e.rates),this.isConnected=!0,this.reconnectAttempts=0}}catch(t){console.error("Failed to fetch latest rates:",t),this.handleConnectionError()}}handleRateUpdates(t){t.forEach(e=>{this.updateRateDisplay(e),this.checkForSignificantChange(e)})}updateRateDisplay(t){(this.rateElements.get(t.currency_code)||[]).forEach(n=>{const a=parseFloat(n.textContent.replace(/[^\d.-]/g,"")),i=parseFloat(t.buy_rate);n.textContent=this.formatRate(i,t.currency_code),a!==i&&this.animateRateChange(n,a<i?"up":"down")}),this.lastRates.set(t.currency_code,t)}animateRateChange(t,e){t.classList.remove("rate-up","rate-down","rate-pulse"),t.classList.add(`rate-${e}`,"rate-pulse"),setTimeout(()=>{t.classList.remove(`rate-${e}`,"rate-pulse")},2e3)}checkForSignificantChange(t){const e=this.lastRates.get(t.currency_code);if(e){const n=Math.abs((t.buy_rate-e.buy_rate)/e.buy_rate)*100;n>=1&&this.showRateChangeNotification(t,e,n)}}showRateChangeNotification(t,e,n){const a=t.buy_rate>e.buy_rate?"increased":"decreased",s=`${a==="increased"?"📈":"📉"} ${t.currency_code} rate ${a} by ${n.toFixed(2)}%`;this.showNotification(s,"rate-change")}showNotification(t,e="info"){const n=document.createElement("div");n.className=`arena-doviz-notification notification-${e}`,n.innerHTML=`
            <div class="notification-content">
                <span class="notification-message">${t}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `,this.getNotificationContainer().appendChild(n),setTimeout(()=>{n.parentElement&&n.remove()},5e3),setTimeout(()=>{n.classList.add("show")},100)}getNotificationContainer(){let t=document.getElementById("arena-doviz-notifications");return t||(t=document.createElement("div"),t.id="arena-doviz-notifications",t.className="arena-doviz-notifications-container",document.body.appendChild(t)),t}setupRateElements(){document.querySelectorAll("[data-currency-rate]").forEach(e=>{const n=e.getAttribute("data-currency-rate");this.rateElements.has(n)||this.rateElements.set(n,[]),this.rateElements.get(n).push(e)})}setupNotifications(){"Notification"in window&&Notification.permission==="default"&&Notification.requestPermission()}startHeartbeat(){setInterval(()=>{this.isConnected&&this.sendHeartbeat()},6e4)}sendHeartbeat(){console.log("Heartbeat sent")}handleConnectionError(){this.isConnected=!1,this.reconnectAttempts++,this.reconnectAttempts<=this.maxReconnectAttempts?(console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`),setTimeout(()=>{this.fetchLatestRates()},this.reconnectDelay*this.reconnectAttempts)):(console.error("Max reconnection attempts reached"),this.showNotification("Connection lost. Please refresh the page.","error"))}formatRate(t,e){if(window.ArenaDovizFormatter)return window.ArenaDovizFormatter.formatExchangeRate(t);const n=e.startsWith("IRR")?0:6;return new Intl.NumberFormat("en-US",{minimumFractionDigits:n,maximumFractionDigits:n}).format(t)}refreshRates(){this.fetchLatestRates()}disconnect(){this.isConnected=!1}reconnect(){this.reconnectAttempts=0,this.fetchLatestRates()}}document.addEventListener("DOMContentLoaded",function(){window.arenaDovizRealtime=new r});const o=document.createElement("style");o.textContent=`
    .arena-doviz-notifications-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        max-width: 400px;
    }

    .arena-doviz-notification {
        background: #fff;
        border-left: 4px solid #3b82f6;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        margin-bottom: 10px;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
    }

    .arena-doviz-notification.show {
        opacity: 1;
        transform: translateX(0);
    }

    .arena-doviz-notification.notification-rate-change {
        border-left-color: #f59e0b;
    }

    .arena-doviz-notification.notification-error {
        border-left-color: #ef4444;
    }

    .notification-content {
        padding: 12px 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .notification-message {
        font-size: 14px;
        color: #374151;
    }

    .notification-close {
        background: none;
        border: none;
        font-size: 18px;
        color: #9ca3af;
        cursor: pointer;
        padding: 0;
        margin-left: 12px;
    }

    .notification-close:hover {
        color: #374151;
    }

    .rate-pulse {
        animation: ratePulse 0.6s ease-in-out;
    }

    .rate-up {
        color: #10b981 !important;
        font-weight: bold;
    }

    .rate-down {
        color: #ef4444 !important;
        font-weight: bold;
    }

    @keyframes ratePulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
`;document.head.appendChild(o);
