/**
 * Arena Doviz Number Formatting Utilities
 * Provides consistent number formatting with thousand separators across the application
 */

class ArenaDovizFormatter {
    /**
     * Format number with thousand separators
     * @param {number|string} value - The number to format
     * @param {number} decimals - Number of decimal places (default: 2)
     * @param {string} thousandSep - Thousand separator (default: ',')
     * @param {string} decimalSep - Decimal separator (default: '.')
     * @returns {string} Formatted number
     */
    static formatNumber(value, decimals = 2, thousandSep = ',', decimalSep = '.') {
        if (value === null || value === undefined || value === '') {
            return '';
        }

        // Convert to number if string
        const num = typeof value === 'string' ? parseFloat(value.replace(/,/g, '')) : value;
        
        if (isNaN(num)) {
            return '';
        }

        // Format with specified decimal places
        const formatted = num.toFixed(decimals);
        const parts = formatted.split('.');
        
        // Add thousand separators to integer part
        parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, thousandSep);
        
        // Join with decimal separator
        return parts.join(decimalSep);
    }

    /**
     * Format currency amount with symbol
     * @param {number|string} value - The amount to format
     * @param {string} currency - Currency code (e.g., 'USD_IST', 'TRY_IST')
     * @param {number} decimals - Number of decimal places
     * @returns {string} Formatted currency amount
     */
    static formatCurrency(value, currency = 'TRY', decimals = 2) {
        const formatted = this.formatNumber(value, decimals);
        if (!formatted) return '';

        // Currency symbols mapping
        const symbols = {
            'USD': '$',
            'EUR': '€',
            'TRY': '₺',
            'IRR': '﷼',
            'AED': 'د.إ',
            'CNY': '¥',
        };

        // Extract base currency from location-based code (e.g., USD_IST -> USD)
        const baseCurrency = currency.split('_')[0];
        const symbol = symbols[baseCurrency] || baseCurrency;

        return `${formatted} ${symbol}`;
    }

    /**
     * Parse formatted number back to float
     * @param {string} formattedValue - The formatted number string
     * @returns {number} Parsed number
     */
    static parseNumber(formattedValue) {
        if (!formattedValue || typeof formattedValue !== 'string') {
            return 0;
        }

        // Remove thousand separators and convert to number
        return parseFloat(formattedValue.replace(/,/g, '')) || 0;
    }

    /**
     * Initialize number formatting for input fields
     * @param {string} selector - CSS selector for input fields
     */
    static initializeInputFormatting(selector = '.amount-input') {
        document.addEventListener('DOMContentLoaded', function() {
            const inputs = document.querySelectorAll(selector);
            
            inputs.forEach(input => {
                // Format on blur
                input.addEventListener('blur', function() {
                    const value = ArenaDovizFormatter.parseNumber(this.value);
                    if (value > 0) {
                        this.value = ArenaDovizFormatter.formatNumber(value);
                    }
                });

                // Allow only numbers, commas, and decimal points during input
                input.addEventListener('input', function(e) {
                    let value = e.target.value;
                    
                    // Remove any characters that aren't digits, commas, or decimal points
                    value = value.replace(/[^\d,.-]/g, '');
                    
                    // Ensure only one decimal point
                    const decimalCount = (value.match(/\./g) || []).length;
                    if (decimalCount > 1) {
                        value = value.substring(0, value.lastIndexOf('.'));
                    }
                    
                    e.target.value = value;
                });

                // Format existing values on page load
                if (input.value) {
                    const value = ArenaDovizFormatter.parseNumber(input.value);
                    if (value > 0) {
                        input.value = ArenaDovizFormatter.formatNumber(value);
                    }
                }
            });
        });
    }

    /**
     * Format all amount displays on the page
     * @param {string} selector - CSS selector for amount display elements
     */
    static formatDisplayAmounts(selector = '.amount-display') {
        document.addEventListener('DOMContentLoaded', function() {
            const displays = document.querySelectorAll(selector);
            
            displays.forEach(display => {
                const value = display.textContent || display.innerText;
                const currency = display.dataset.currency || 'TRY';
                const decimals = parseInt(display.dataset.decimals) || 2;
                
                if (value && !isNaN(parseFloat(value))) {
                    display.textContent = ArenaDovizFormatter.formatCurrency(value, currency, decimals);
                }
            });
        });
    }

    /**
     * Real-time formatting for input fields (formats as user types)
     * @param {string} selector - CSS selector for input fields
     */
    static initializeRealTimeFormatting(selector = '.amount-input-realtime') {
        document.addEventListener('DOMContentLoaded', function() {
            const inputs = document.querySelectorAll(selector);
            
            inputs.forEach(input => {
                input.addEventListener('input', function(e) {
                    let value = e.target.value;
                    
                    // Remove existing formatting
                    value = value.replace(/[^\d.]/g, '');
                    
                    // Parse and reformat
                    const numValue = parseFloat(value);
                    if (!isNaN(numValue) && numValue > 0) {
                        // Get cursor position before formatting
                        const cursorPos = e.target.selectionStart;
                        const oldLength = e.target.value.length;
                        
                        // Format the number
                        const formatted = ArenaDovizFormatter.formatNumber(numValue, 2);
                        e.target.value = formatted;
                        
                        // Adjust cursor position after formatting
                        const newLength = formatted.length;
                        const newCursorPos = cursorPos + (newLength - oldLength);
                        e.target.setSelectionRange(newCursorPos, newCursorPos);
                    }
                });
            });
        });
    }

    /**
     * Initialize all formatting features
     */
    static initialize() {
        this.initializeInputFormatting();
        this.formatDisplayAmounts();
        this.initializeRealTimeFormatting();
    }
}

// Auto-initialize when script loads
ArenaDovizFormatter.initialize();

// Export for use in other scripts
window.ArenaDovizFormatter = ArenaDovizFormatter;
