<?php

namespace App\Filament\Company\Resources\ArenaDoviz\CommissionRuleResource\Pages;

use App\Filament\Company\Resources\ArenaDoviz\CommissionRuleResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCommissionRules extends ListRecords
{
    protected static string $resource = CommissionRuleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
