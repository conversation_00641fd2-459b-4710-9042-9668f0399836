<?php

namespace Tests\Unit\ArenaDoviz;

use App\Models\ArenaDoviz\CommissionRule;
use App\Services\ArenaDoviz\CommissionCalculationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CommissionCalculationServiceTest extends TestCase
{
    use RefreshDatabase;

    private CommissionCalculationService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new CommissionCalculationService();
    }

    public function test_calculate_commission_with_percentage_rule()
    {
        // Create a percentage commission rule
        CommissionRule::factory()->create([
            'rule_name' => 'Test Percentage Rule',
            'commission_type' => 'percentage',
            'commission_rate' => 2.5,
            'calculation_method' => 'post_conversion',
            'is_active' => true,
            'priority' => 1,
        ]);

        $exchangeData = [
            'source_amount' => 1000.00,
            'destination_amount' => 32000.00,
            'source_currency' => 'USD_IST',
            'destination_currency' => 'TRY_IST',
        ];

        $result = $this->service->calculateCommission($exchangeData);

        $this->assertEquals(800.00, $result['commission_amount']); // 32000 * 2.5%
        $this->assertEquals('TRY_IST', $result['commission_currency']);
        $this->assertEquals(2.5, $result['commission_rate_used']);
        $this->assertEquals('post_conversion', $result['calculation_method']);
    }

    public function test_calculate_commission_with_fixed_rule()
    {
        CommissionRule::factory()->create([
            'rule_name' => 'Test Fixed Rule',
            'commission_type' => 'fixed',
            'fixed_amount' => 50.00,
            'commission_currency' => 'TRY_IST',
            'is_active' => true,
            'priority' => 1,
        ]);

        $exchangeData = [
            'source_amount' => 1000.00,
            'destination_amount' => 32000.00,
            'source_currency' => 'USD_IST',
            'destination_currency' => 'TRY_IST',
        ];

        $result = $this->service->calculateCommission($exchangeData);

        $this->assertEquals(50.00, $result['commission_amount']);
        $this->assertEquals('TRY_IST', $result['commission_currency']);
    }

    public function test_calculate_commission_with_hybrid_rule()
    {
        CommissionRule::factory()->create([
            'rule_name' => 'Test Hybrid Rule',
            'commission_type' => 'hybrid',
            'commission_rate' => 1.0,
            'fixed_amount' => 25.00,
            'calculation_method' => 'post_conversion',
            'is_active' => true,
            'priority' => 1,
        ]);

        $exchangeData = [
            'source_amount' => 1000.00,
            'destination_amount' => 32000.00,
            'source_currency' => 'USD_IST',
            'destination_currency' => 'TRY_IST',
        ];

        $result = $this->service->calculateCommission($exchangeData);

        $this->assertEquals(345.00, $result['commission_amount']); // (32000 * 1%) + 25
        $this->assertEquals(1.0, $result['commission_rate_used']);
    }

    public function test_calculate_commission_with_min_commission_limit()
    {
        CommissionRule::factory()->create([
            'rule_name' => 'Test Min Commission Rule',
            'commission_type' => 'percentage',
            'commission_rate' => 0.1,
            'min_commission' => 100.00,
            'calculation_method' => 'post_conversion',
            'is_active' => true,
            'priority' => 1,
        ]);

        $exchangeData = [
            'source_amount' => 100.00,
            'destination_amount' => 3200.00,
            'source_currency' => 'USD_IST',
            'destination_currency' => 'TRY_IST',
        ];

        $result = $this->service->calculateCommission($exchangeData);

        $this->assertEquals(100.00, $result['commission_amount']); // Min commission applied
    }

    public function test_calculate_commission_with_max_commission_limit()
    {
        CommissionRule::factory()->create([
            'rule_name' => 'Test Max Commission Rule',
            'commission_type' => 'percentage',
            'commission_rate' => 5.0,
            'max_commission' => 500.00,
            'calculation_method' => 'post_conversion',
            'is_active' => true,
            'priority' => 1,
        ]);

        $exchangeData = [
            'source_amount' => 1000.00,
            'destination_amount' => 32000.00,
            'source_currency' => 'USD_IST',
            'destination_currency' => 'TRY_IST',
        ];

        $result = $this->service->calculateCommission($exchangeData);

        $this->assertEquals(500.00, $result['commission_amount']); // Max commission applied
    }

    public function test_calculate_commission_with_pre_conversion_method()
    {
        CommissionRule::factory()->create([
            'rule_name' => 'Test Pre-conversion Rule',
            'commission_type' => 'percentage',
            'commission_rate' => 2.0,
            'calculation_method' => 'pre_conversion',
            'is_active' => true,
            'priority' => 1,
        ]);

        $exchangeData = [
            'source_amount' => 1000.00,
            'destination_amount' => 32000.00,
            'source_currency' => 'USD_IST',
            'destination_currency' => 'TRY_IST',
        ];

        $result = $this->service->calculateCommission($exchangeData);

        $this->assertEquals(20.00, $result['commission_amount']); // 1000 * 2%
        $this->assertEquals(1000.00, $result['base_amount']);
        $this->assertEquals('pre_conversion', $result['calculation_method']);
    }

    public function test_calculate_commission_with_default_rule_when_no_matches()
    {
        $exchangeData = [
            'source_amount' => 1000.00,
            'destination_amount' => 32000.00,
            'source_currency' => 'USD_IST',
            'destination_currency' => 'TRY_IST',
        ];

        $result = $this->service->calculateCommission($exchangeData);

        $this->assertEquals(160.00, $result['commission_amount']); // 32000 * 0.5%
        $this->assertEquals('TRY_IST', $result['commission_currency']);
        $this->assertEquals(0.5, $result['commission_rate_used']);
        $this->assertEquals('Default Rule', $result['rule_applied']);
    }

    public function test_calculate_commission_with_priority_rules()
    {
        // Create two rules with different priorities
        CommissionRule::factory()->create([
            'rule_name' => 'Low Priority Rule',
            'commission_type' => 'percentage',
            'commission_rate' => 1.0,
            'priority' => 1,
            'is_active' => true,
        ]);

        CommissionRule::factory()->create([
            'rule_name' => 'High Priority Rule',
            'commission_type' => 'percentage',
            'commission_rate' => 2.0,
            'priority' => 5,
            'is_active' => true,
        ]);

        $exchangeData = [
            'source_amount' => 1000.00,
            'destination_amount' => 32000.00,
            'source_currency' => 'USD_IST',
            'destination_currency' => 'TRY_IST',
        ];

        $result = $this->service->calculateCommission($exchangeData);

        $this->assertEquals(640.00, $result['commission_amount']); // Uses high priority rule (2%)
        $this->assertEquals('High Priority Rule', $result['rule_applied']);
    }

    public function test_calculate_commission_ignores_inactive_rules()
    {
        CommissionRule::factory()->create([
            'rule_name' => 'Inactive Rule',
            'commission_type' => 'percentage',
            'commission_rate' => 10.0,
            'is_active' => false,
            'priority' => 10,
        ]);

        $exchangeData = [
            'source_amount' => 1000.00,
            'destination_amount' => 32000.00,
            'source_currency' => 'USD_IST',
            'destination_currency' => 'TRY_IST',
        ];

        $result = $this->service->calculateCommission($exchangeData);

        // Should use default rule since inactive rule is ignored
        $this->assertEquals(160.00, $result['commission_amount']); // 32000 * 0.5%
        $this->assertEquals('Default Rule', $result['rule_applied']);
    }

    public function test_validate_commission_with_valid_commission()
    {
        $commission = [
            'commission_amount' => 100.00,
            'commission_rate_used' => 2.0,
            'base_amount' => 5000.00,
        ];

        $result = $this->service->validateCommission($commission);

        $this->assertTrue($result['is_valid']);
        $this->assertEmpty($result['errors']);
    }

    public function test_validate_commission_with_high_rate()
    {
        $commission = [
            'commission_amount' => 100.00,
            'commission_rate_used' => 10.0, // Above default max of 5%
            'base_amount' => 1000.00,
        ];

        $result = $this->service->validateCommission($commission);

        $this->assertFalse($result['is_valid']);
        $this->assertContains('Commission rate (10%) exceeds maximum allowed rate (5%)', $result['errors']);
    }

    public function test_validate_commission_with_negative_amount()
    {
        $commission = [
            'commission_amount' => -50.00,
            'commission_rate_used' => 2.0,
            'base_amount' => 1000.00,
        ];

        $result = $this->service->validateCommission($commission);

        $this->assertFalse($result['is_valid']);
        $this->assertContains('Commission amount cannot be negative', $result['errors']);
    }
}
