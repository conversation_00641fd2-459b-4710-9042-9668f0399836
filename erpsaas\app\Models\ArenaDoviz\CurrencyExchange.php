<?php

namespace App\Models\ArenaDoviz;

use App\Concerns\Blamable;
use App\Concerns\CompanyOwned;
use App\Enums\ArenaDoviz\ExchangeStatus;
use App\Enums\ArenaDoviz\ExchangeType;
use App\Models\Common\Client;
use App\Models\Setting\Currency;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class CurrencyExchange extends Model
{
    use Blamable;
    use CompanyOwned;
    use HasFactory;
    use SoftDeletes;

    protected $table = 'currency_exchanges';

    protected $fillable = [
        'company_id',
        'client_id',
        'location_id', // Added for location-based operations
        'iranian_bank_id', // Added for Iranian banking integration
        'exchange_number',
        'exchange_type',
        'status',
        'from_currency_code', // Now supports location codes like USD_IST
        'to_currency_code',   // Now supports location codes like EUR_TBZ
        'from_amount',
        'to_amount',
        'exchange_rate',
        'commission_rate',
        'commission_amount',
        'net_amount',
        'notes',
        'exchange_date',
        'settlement_date',
        'delivery_method',
        'delivery_address',
        'reference_number',
        // Iranian banking fields
        'iranian_account_number',
        'iranian_card_number',
        'iranian_tracking_code',
        'iranian_transfer_date',
        'iranian_transfer_fee',
        'iranian_transfer_notes',
        // Batch processing fields
        'batch_id',
        'batch_sequence',
        'batch_processed_at',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'exchange_type' => ExchangeType::class,
        'status' => ExchangeStatus::class,
        'from_amount' => 'decimal:4',
        'to_amount' => 'decimal:4',
        'exchange_rate' => 'decimal:6',
        'commission_rate' => 'decimal:4',
        'commission_amount' => 'decimal:4',
        'net_amount' => 'decimal:4',
        'iranian_transfer_fee' => 'decimal:2',
        'exchange_date' => 'datetime',
        'settlement_date' => 'datetime',
        'iranian_transfer_date' => 'datetime',
        'batch_processed_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($exchange) {
            if (empty($exchange->exchange_number)) {
                $exchange->exchange_number = static::generateExchangeNumber();
            }
        });
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    public function fromCurrency(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'from_currency_code', 'code');
    }

    public function toCurrency(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'to_currency_code', 'code');
    }

    public function fromLocationCurrency(): BelongsTo
    {
        return $this->belongsTo(LocationCurrency::class, 'from_currency_code', 'location_currency_code');
    }

    public function toLocationCurrency(): BelongsTo
    {
        return $this->belongsTo(LocationCurrency::class, 'to_currency_code', 'location_currency_code');
    }

    public function iranianBank(): BelongsTo
    {
        return $this->belongsTo(IranianBank::class);
    }

    /**
     * Get the combined exchange this belongs to (if any)
     */
    public function combinedExchange(): BelongsTo
    {
        return $this->belongsTo(CombinedExchange::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public static function generateExchangeNumber(): string
    {
        $prefix = 'EX';
        $year = date('Y');
        $month = date('m');
        
        $lastExchange = static::whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastExchange ? 
            (int) substr($lastExchange->exchange_number, -4) + 1 : 1;

        return sprintf('%s%s%s%04d', $prefix, $year, $month, $sequence);
    }

    public function calculateCommission(): void
    {
        if ($this->commission_rate && $this->from_amount) {
            $this->commission_amount = $this->from_amount * ($this->commission_rate / 100);
            $this->net_amount = $this->to_amount - $this->commission_amount;
        }
    }

    public function getFormattedFromAmountAttribute(): string
    {
        return \App\Helpers\ArenaDoviz\NumberFormatter::formatByCurrency($this->from_amount, $this->from_currency_code);
    }

    public function getFormattedToAmountAttribute(): string
    {
        return \App\Helpers\ArenaDoviz\NumberFormatter::formatByCurrency($this->to_amount, $this->to_currency_code);
    }

    public function getFormattedExchangeRateAttribute(): string
    {
        return '1 ' . $this->from_currency_code . ' = ' .
               \App\Helpers\ArenaDoviz\NumberFormatter::formatExchangeRate($this->exchange_rate) . ' ' . $this->to_currency_code;
    }

    public function getFormattedCommissionAmountAttribute(): string
    {
        return \App\Helpers\ArenaDoviz\NumberFormatter::formatByCurrency($this->commission_amount, $this->to_currency_code);
    }

    public function getFormattedNetAmountAttribute(): string
    {
        return \App\Helpers\ArenaDoviz\NumberFormatter::formatByCurrency($this->net_amount, $this->to_currency_code);
    }

    /**
     * Calculate commission using the commission service
     */
    public function calculateCommissionByRules(): array
    {
        $service = app(\App\Services\ArenaDoviz\CommissionCalculationService::class);

        $exchangeData = [
            'client_id' => $this->client_id,
            'location_id' => $this->location_id,
            'exchange_type' => $this->exchange_type->value,
            'source_currency' => $this->from_currency_code,
            'destination_currency' => $this->to_currency_code,
            'source_amount' => $this->from_amount,
            'destination_amount' => $this->to_amount,
        ];

        return $service->calculateCommission($exchangeData);
    }

    /**
     * Get commission breakdown for display
     */
    public function getCommissionBreakdown(): array
    {
        $service = app(\App\Services\ArenaDoviz\CommissionCalculationService::class);
        return $service->getCommissionBreakdown($this);
    }

    /**
     * Update commission based on rules
     */
    public function updateCommissionFromRules(): void
    {
        $commission = $this->calculateCommissionByRules();

        $this->update([
            'commission_rate' => $commission['commission_rate_used'],
            'commission_amount' => $commission['commission_amount'],
            'net_amount' => $this->to_amount - $commission['commission_amount'],
        ]);
    }

    public function isPending(): bool
    {
        return $this->status === ExchangeStatus::PENDING;
    }

    public function isCompleted(): bool
    {
        return $this->status === ExchangeStatus::COMPLETED;
    }

    public function isCancelled(): bool
    {
        return $this->status === ExchangeStatus::CANCELLED;
    }

    public function canBeModified(): bool
    {
        return in_array($this->status, [ExchangeStatus::PENDING, ExchangeStatus::PROCESSING]);
    }

    public function canBeCancelled(): bool
    {
        return in_array($this->status, [ExchangeStatus::PENDING, ExchangeStatus::PROCESSING]);
    }
}
