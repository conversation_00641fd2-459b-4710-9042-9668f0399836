<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('combined_exchanges', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->constrained()->cascadeOnDelete();
            $table->foreignId('location_id')->nullable()->constrained('locations')->nullOnDelete();
            $table->foreignId('buy_client_id')->constrained('clients')->cascadeOnDelete();
            $table->foreignId('sell_client_id')->constrained('clients')->cascadeOnDelete();
            
            // Exchange identification
            $table->string('combined_exchange_number', 20)->unique();
            $table->enum('status', ['pending', 'processing', 'completed', 'cancelled'])
                ->default('pending');
            
            // Currency and amounts
            $table->string('currency_code', 10);
            $table->decimal('buy_amount', 15, 2);
            $table->decimal('sell_amount', 15, 2);
            $table->decimal('buy_rate', 15, 6);
            $table->decimal('sell_rate', 15, 6);
            
            // Profit calculations
            $table->decimal('profit_amount', 15, 2)->default(0);
            $table->decimal('profit_margin', 8, 4)->default(0); // Percentage
            $table->decimal('total_commission', 15, 2)->default(0);
            $table->decimal('net_profit', 15, 2)->default(0);
            
            // Dates
            $table->timestamp('exchange_date')->default(now());
            $table->timestamp('settlement_date')->nullable();
            
            // Notes
            $table->text('notes')->nullable();
            
            // Audit fields
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes
            $table->index('combined_exchange_number');
            $table->index(['company_id', 'status']);
            $table->index(['buy_client_id', 'sell_client_id']);
            $table->index('exchange_date');
            $table->index(['currency_code', 'exchange_date']);
            $table->index(['status', 'exchange_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('combined_exchanges');
    }
};
