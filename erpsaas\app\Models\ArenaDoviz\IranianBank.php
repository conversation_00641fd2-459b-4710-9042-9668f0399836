<?php

namespace App\Models\ArenaDoviz;

use App\Concerns\Blamable;
use App\Concerns\CompanyOwned;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class IranianBank extends Model
{
    use Blamable;
    use CompanyOwned;
    use HasFactory;
    use SoftDeletes;

    protected $table = 'iranian_banks';

    protected $fillable = [
        'company_id',
        'name',
        'persian_name',
        'bank_code',
        'swift_code',
        'central_bank_code',
        'bank_type',
        'is_active',
        'supports_international',
        'supports_rial_exchange',
        'contact_phone',
        'contact_email',
        'website',
        'address',
        'city',
        'province',
        'postal_code',
        'established_year',
        'logo_url',
        'notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'supports_international' => 'boolean',
        'supports_rial_exchange' => 'boolean',
        'established_year' => 'integer',
    ];

    /**
     * Bank types in Iran
     */
    public const BANK_TYPES = [
        'commercial' => 'Commercial Bank',
        'specialized' => 'Specialized Bank',
        'development' => 'Development Bank',
        'cooperative' => 'Cooperative Bank',
        'islamic' => 'Islamic Bank',
        'private' => 'Private Bank',
        'government' => 'Government Bank',
    ];

    /**
     * Get currency exchanges using this bank
     */
    public function currencyExchanges(): HasMany
    {
        return $this->hasMany(CurrencyExchange::class, 'iranian_bank_id');
    }

    /**
     * Get bank transactions
     */
    public function bankTransactions(): HasMany
    {
        return $this->hasMany(IranianBankTransaction::class, 'iranian_bank_id');
    }

    /**
     * Get formatted display name (English and Persian)
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->persian_name ? "{$this->name} ({$this->persian_name})" : $this->name;
    }

    /**
     * Get formatted bank code with name
     */
    public function getFormattedBankCodeAttribute(): string
    {
        return $this->bank_code ? "{$this->bank_code} - {$this->name}" : $this->name;
    }

    /**
     * Check if bank supports international transactions
     */
    public function supportsInternational(): bool
    {
        return $this->supports_international;
    }

    /**
     * Check if bank supports rial exchange
     */
    public function supportsRialExchange(): bool
    {
        return $this->supports_rial_exchange;
    }

    /**
     * Get active banks
     */
    public static function active()
    {
        return static::where('is_active', true);
    }

    /**
     * Get banks that support international transactions
     */
    public static function international()
    {
        return static::where('supports_international', true)->active();
    }

    /**
     * Get banks that support rial exchange
     */
    public static function rialExchange()
    {
        return static::where('supports_rial_exchange', true)->active();
    }

    /**
     * Get banks by type
     */
    public static function byType(string $type)
    {
        return static::where('bank_type', $type)->active();
    }

    /**
     * Get banks for dropdown selection
     */
    public static function getForDropdown(): array
    {
        return static::active()
            ->orderBy('name')
            ->pluck('display_name', 'id')
            ->toArray();
    }

    /**
     * Get banks for international transactions dropdown
     */
    public static function getInternationalForDropdown(): array
    {
        return static::international()
            ->orderBy('name')
            ->pluck('display_name', 'id')
            ->toArray();
    }

    /**
     * Get banks for rial exchange dropdown
     */
    public static function getRialExchangeForDropdown(): array
    {
        return static::rialExchange()
            ->orderBy('name')
            ->pluck('display_name', 'id')
            ->toArray();
    }

    /**
     * Search banks by name or Persian name
     */
    public static function search(string $query)
    {
        return static::active()
            ->where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('persian_name', 'like', "%{$query}%")
                  ->orWhere('bank_code', 'like', "%{$query}%");
            });
    }

    /**
     * Get bank statistics
     */
    public function getStatistics(): array
    {
        return [
            'total_exchanges' => $this->currencyExchanges()->count(),
            'total_volume' => $this->currencyExchanges()->sum('from_amount'),
            'active_exchanges' => $this->currencyExchanges()
                ->whereIn('status', ['pending', 'processing'])
                ->count(),
            'completed_exchanges' => $this->currencyExchanges()
                ->where('status', 'completed')
                ->count(),
        ];
    }
}
