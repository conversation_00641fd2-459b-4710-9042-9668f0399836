<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('debit_notes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->constrained()->cascadeOnDelete();
            $table->foreignId('client_id')->constrained()->cascadeOnDelete();
            $table->foreignId('delivery_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('location_id')->nullable()->constrained('locations')->nullOnDelete();
            
            // Debit note identification
            $table->string('debit_note_number', 20)->unique();
            $table->enum('status', ['pending', 'scheduled', 'in_progress', 'completed', 'cancelled'])
                ->default('pending');
            $table->enum('debit_type', [
                'delivery', 'cash_collection', 'document_delivery', 
                'service_charge', 'penalty', 'adjustment'
            ])->default('delivery');
            
            // Financial details
            $table->string('currency_code', 10);
            $table->decimal('amount', 15, 2);
            $table->text('description');
            
            // Recipient information
            $table->string('recipient_name', 100);
            $table->string('recipient_phone', 20)->nullable();
            $table->string('recipient_email', 100)->nullable();
            $table->text('delivery_address')->nullable();
            
            // Scheduling and completion
            $table->timestamp('scheduled_date')->nullable();
            $table->timestamp('completed_date')->nullable();
            
            // Document paths
            $table->string('signature_path', 255)->nullable(); // Digital signature file
            $table->string('photo_path', 255)->nullable(); // Delivery proof photo
            $table->string('receipt_path', 255)->nullable(); // Receipt/invoice scan
            
            // Verification and security
            $table->string('verification_code', 10)->nullable(); // For recipient verification
            
            // Notes and instructions
            $table->text('special_instructions')->nullable();
            $table->text('courier_notes')->nullable();
            $table->text('client_notes')->nullable();
            $table->text('internal_notes')->nullable();
            
            // Audit fields
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes
            $table->index('debit_note_number');
            $table->index(['company_id', 'status']);
            $table->index(['client_id', 'status']);
            $table->index('scheduled_date');
            $table->index('verification_code');
            $table->index(['status', 'scheduled_date']);
            $table->index(['debit_type', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('debit_notes');
    }
};
