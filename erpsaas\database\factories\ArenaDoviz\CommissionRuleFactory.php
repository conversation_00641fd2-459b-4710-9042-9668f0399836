<?php

namespace Database\Factories\ArenaDoviz;

use App\Models\ArenaDoviz\CommissionRule;
use App\Models\Common\Company;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ArenaDoviz\CommissionRule>
 */
class CommissionRuleFactory extends Factory
{
    protected $model = CommissionRule::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'company_id' => Company::factory(),
            'rule_name' => $this->faker->words(3, true) . ' Commission Rule',
            'exchange_type' => $this->faker->randomElement(['buy', 'sell', 'transfer', 'cash_deposit']),
            'commission_type' => $this->faker->randomElement(['percentage', 'fixed', 'tiered', 'hybrid']),
            'commission_rate' => $this->faker->randomFloat(2, 0.1, 5.0),
            'fixed_amount' => $this->faker->randomFloat(2, 10, 500),
            'calculation_method' => $this->faker->randomElement(['pre_conversion', 'post_conversion', 'higher_amount', 'lower_amount']),
            'commission_currency' => $this->faker->randomElement(['TRY_IST', 'USD_IST', 'EUR_IST', 'IRR_TBZ']),
            'is_active' => true,
            'priority' => $this->faker->numberBetween(1, 10),
            'created_by' => User::factory(),
        ];
    }

    /**
     * Indicate that the commission rule is for percentage type.
     */
    public function percentage(): static
    {
        return $this->state(fn (array $attributes) => [
            'commission_type' => 'percentage',
            'commission_rate' => $this->faker->randomFloat(2, 0.1, 3.0),
            'fixed_amount' => null,
        ]);
    }

    /**
     * Indicate that the commission rule is for fixed type.
     */
    public function fixed(): static
    {
        return $this->state(fn (array $attributes) => [
            'commission_type' => 'fixed',
            'commission_rate' => null,
            'fixed_amount' => $this->faker->randomFloat(2, 25, 200),
        ]);
    }

    /**
     * Indicate that the commission rule is for hybrid type.
     */
    public function hybrid(): static
    {
        return $this->state(fn (array $attributes) => [
            'commission_type' => 'hybrid',
            'commission_rate' => $this->faker->randomFloat(2, 0.1, 2.0),
            'fixed_amount' => $this->faker->randomFloat(2, 10, 100),
        ]);
    }

    /**
     * Indicate that the commission rule is for tiered type.
     */
    public function tiered(): static
    {
        return $this->state(fn (array $attributes) => [
            'commission_type' => 'tiered',
            'commission_rate' => null,
            'fixed_amount' => null,
            'tier_rules' => [
                ['min_amount' => 0, 'max_amount' => 10000, 'rate' => 0.5],
                ['min_amount' => 10000, 'max_amount' => 50000, 'rate' => 0.3],
                ['min_amount' => 50000, 'max_amount' => PHP_FLOAT_MAX, 'rate' => 0.2],
            ],
        ]);
    }

    /**
     * Indicate that the commission rule is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the commission rule has high priority.
     */
    public function highPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => $this->faker->numberBetween(8, 10),
        ]);
    }

    /**
     * Indicate that the commission rule has low priority.
     */
    public function lowPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => $this->faker->numberBetween(1, 3),
        ]);
    }

    /**
     * Indicate that the commission rule is for buy transactions.
     */
    public function forBuy(): static
    {
        return $this->state(fn (array $attributes) => [
            'exchange_type' => 'buy',
        ]);
    }

    /**
     * Indicate that the commission rule is for sell transactions.
     */
    public function forSell(): static
    {
        return $this->state(fn (array $attributes) => [
            'exchange_type' => 'sell',
        ]);
    }

    /**
     * Indicate that the commission rule is for transfer transactions.
     */
    public function forTransfer(): static
    {
        return $this->state(fn (array $attributes) => [
            'exchange_type' => 'transfer',
        ]);
    }

    /**
     * Indicate that the commission rule has minimum commission.
     */
    public function withMinCommission(float $amount = 50.0): static
    {
        return $this->state(fn (array $attributes) => [
            'min_commission' => $amount,
        ]);
    }

    /**
     * Indicate that the commission rule has maximum commission.
     */
    public function withMaxCommission(float $amount = 1000.0): static
    {
        return $this->state(fn (array $attributes) => [
            'max_commission' => $amount,
        ]);
    }

    /**
     * Indicate that the commission rule is for specific currency pair.
     */
    public function forCurrencyPair(string $from, string $to): static
    {
        return $this->state(fn (array $attributes) => [
            'from_currency_code' => $from,
            'to_currency_code' => $to,
        ]);
    }

    /**
     * Indicate that the commission rule uses pre-conversion calculation.
     */
    public function preConversion(): static
    {
        return $this->state(fn (array $attributes) => [
            'calculation_method' => 'pre_conversion',
        ]);
    }

    /**
     * Indicate that the commission rule uses post-conversion calculation.
     */
    public function postConversion(): static
    {
        return $this->state(fn (array $attributes) => [
            'calculation_method' => 'post_conversion',
        ]);
    }

    /**
     * Indicate that the commission rule has validity dates.
     */
    public function withValidityPeriod(\DateTime $from = null, \DateTime $until = null): static
    {
        return $this->state(fn (array $attributes) => [
            'valid_from' => $from ? $from->format('Y-m-d') : now()->subDays(30)->format('Y-m-d'),
            'valid_until' => $until ? $until->format('Y-m-d') : now()->addDays(30)->format('Y-m-d'),
        ]);
    }

    /**
     * Indicate that the commission rule is expired.
     */
    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'valid_from' => now()->subDays(60)->format('Y-m-d'),
            'valid_until' => now()->subDays(30)->format('Y-m-d'),
        ]);
    }

    /**
     * Indicate that the commission rule is not yet valid.
     */
    public function notYetValid(): static
    {
        return $this->state(fn (array $attributes) => [
            'valid_from' => now()->addDays(30)->format('Y-m-d'),
            'valid_until' => now()->addDays(60)->format('Y-m-d'),
        ]);
    }
}
