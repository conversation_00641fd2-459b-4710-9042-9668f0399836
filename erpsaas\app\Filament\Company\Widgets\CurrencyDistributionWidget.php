<?php

namespace App\Filament\Company\Widgets;

use App\Services\ArenaDoviz\DashboardAnalyticsService;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Number;

class CurrencyDistributionWidget extends ChartWidget
{
    protected static ?string $heading = 'Currency Distribution';
    protected static ?string $description = 'Transaction volume by currency';
    protected static ?int $sort = 3;
    protected int | string | array $columnSpan = 'full';
    protected static ?string $pollingInterval = '60s';

    private function getAnalyticsService(): DashboardAnalyticsService
    {
        return app(DashboardAnalyticsService::class);
    }

    protected function getData(): array
    {
        $company = auth()->user()->currentCompany;
        $startDate = now()->startOfMonth();
        $endDate = now();

        $metrics = $this->getAnalyticsService()->getDashboardMetrics($company, $startDate, $endDate);
        $currencyData = $metrics['transactions']['by_currency'];

        $labels = $currencyData->pluck('currency')->toArray();
        $volumes = $currencyData->pluck('volume')->toArray();
        $counts = $currencyData->pluck('count')->toArray();

        // Generate colors for each currency
        $colors = [
            'rgba(59, 130, 246, 0.8)',   // Blue
            'rgba(16, 185, 129, 0.8)',   // Green
            'rgba(245, 158, 11, 0.8)',   // Yellow
            'rgba(239, 68, 68, 0.8)',    // Red
            'rgba(139, 92, 246, 0.8)',   // Purple
            'rgba(236, 72, 153, 0.8)',   // Pink
            'rgba(14, 165, 233, 0.8)',   // Sky
            'rgba(34, 197, 94, 0.8)',    // Emerald
            'rgba(251, 146, 60, 0.8)',   // Orange
            'rgba(168, 85, 247, 0.8)',   // Violet
        ];

        return [
            'datasets' => [
                [
                    'label' => 'Volume (TRY)',
                    'data' => $volumes,
                    'backgroundColor' => array_slice($colors, 0, count($volumes)),
                    'borderColor' => array_map(function($color) {
                        return str_replace('0.8', '1', $color);
                    }, array_slice($colors, 0, count($volumes))),
                    'borderWidth' => 2,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }

    protected function getOptions(): array
    {
        return [
            'responsive' => true,
            'maintainAspectRatio' => false,
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'right',
                ],
                'tooltip' => [
                    'callbacks' => [
                        'label' => new \Filament\Support\RawJs('function(context) {
                            const label = context.label || "";
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return label + ": " + new Intl.NumberFormat("tr-TR", {
                                style: "currency",
                                currency: "TRY"
                            }).format(value) + " (" + percentage + "%)";
                        }'),
                    ],
                ],
            ],
            'cutout' => '50%',
        ];
    }
}
