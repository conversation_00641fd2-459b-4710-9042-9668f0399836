<?php

namespace Database\Factories\ArenaDoviz;

use App\Enums\ArenaDoviz\ExchangeType;
use App\Enums\ArenaDoviz\ExchangeStatus;
use App\Models\ArenaDoviz\CurrencyExchange;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ArenaDoviz\CurrencyExchange>
 */
class CurrencyExchangeFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CurrencyExchange::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $exchangeType = $this->faker->randomElement(ExchangeType::cases());
        $status = $this->faker->randomElement(ExchangeStatus::cases());
        
        $currencyPair = $this->getRandomCurrencyPair();
        $fromCurrency = $currencyPair['from'];
        $toCurrency = $currencyPair['to'];
        $exchangeRate = $currencyPair['rate'];
        
        $fromAmount = $this->generateAmount($fromCurrency);
        $toAmount = $fromAmount * $exchangeRate;
        
        $commissionRate = $this->faker->randomFloat(4, 0.0015, 0.0030);
        $commissionAmount = $toAmount * $commissionRate;
        $netAmount = $toAmount - $commissionAmount;
        
        $exchangeDate = $this->faker->dateTimeBetween('-6 months', 'now');
        $settlementDate = $status === ExchangeStatus::COMPLETED ? 
            Carbon::instance($exchangeDate)->addHours($this->faker->numberBetween(1, 48)) : null;
        
        return [
            'company_id' => 1,
            'client_id' => null, // Will be set when creating with client
            'exchange_type' => $exchangeType,
            'status' => $status,
            'from_currency_code' => $fromCurrency,
            'to_currency_code' => $toCurrency,
            'from_amount' => round($fromAmount, 4),
            'to_amount' => round($toAmount, 4),
            'exchange_rate' => round($exchangeRate, 6),
            'commission_rate' => $commissionRate,
            'commission_amount' => round($commissionAmount, 4),
            'net_amount' => round($netAmount, 4),
            'notes' => $this->faker->optional(0.6)->sentence(),
            'exchange_date' => $exchangeDate,
            'settlement_date' => $settlementDate,
            'delivery_method' => $this->faker->optional(0.4)->randomElement(['pickup', 'courier', 'bank_transfer']),
            'delivery_address' => $this->faker->optional(0.3)->address(),
            'reference_number' => $this->generateReferenceNumber(),
            'created_by' => $this->faker->numberBetween(1, 5),
        ];
    }

    /**
     * Generate buy transaction (customer buying foreign currency)
     */
    public function buy(): static
    {
        return $this->state(fn (array $attributes) => [
            'exchange_type' => ExchangeType::BUY,
            'notes' => 'Customer purchase of foreign currency',
        ]);
    }

    /**
     * Generate sell transaction (customer selling foreign currency)
     */
    public function sell(): static
    {
        return $this->state(fn (array $attributes) => [
            'exchange_type' => ExchangeType::SELL,
            'notes' => 'Customer sale of foreign currency',
        ]);
    }

    /**
     * Generate transfer transaction
     */
    public function transfer(): static
    {
        return $this->state(fn (array $attributes) => [
            'exchange_type' => ExchangeType::TRANSFER,
            'notes' => 'International transfer transaction',
            'delivery_method' => 'bank_transfer',
        ]);
    }

    /**
     * Generate completed transaction
     */
    public function completed(): static
    {
        return $this->state(function (array $attributes) {
            $exchangeDate = $attributes['exchange_date'] ?? $this->faker->dateTimeBetween('-6 months', '-1 day');
            
            return [
                'status' => ExchangeStatus::COMPLETED,
                'settlement_date' => Carbon::instance($exchangeDate)->addHours($this->faker->numberBetween(1, 24)),
            ];
        });
    }

    /**
     * Generate pending transaction
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => ExchangeStatus::PENDING,
            'settlement_date' => null,
            'exchange_date' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ]);
    }

    /**
     * Generate processing transaction
     */
    public function processing(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => ExchangeStatus::PROCESSING,
            'settlement_date' => null,
            'exchange_date' => $this->faker->dateTimeBetween('-3 days', 'now'),
        ]);
    }

    /**
     * Generate high-value transaction
     */
    public function highValue(): static
    {
        return $this->state(function (array $attributes) {
            $fromCurrency = $attributes['from_currency_code'] ?? 'USD';
            $highAmount = $this->generateHighAmount($fromCurrency);
            $exchangeRate = $attributes['exchange_rate'] ?? 32.5;
            $toAmount = $highAmount * $exchangeRate;
            
            $commissionRate = 0.0015; // Lower rate for high-value transactions
            $commissionAmount = $toAmount * $commissionRate;
            $netAmount = $toAmount - $commissionAmount;
            
            return [
                'from_amount' => $highAmount,
                'to_amount' => round($toAmount, 4),
                'commission_rate' => $commissionRate,
                'commission_amount' => round($commissionAmount, 4),
                'net_amount' => round($netAmount, 4),
                'notes' => 'High-value transaction - special handling required',
            ];
        });
    }

    /**
     * Generate USD to TRY transaction
     */
    public function usdToTry(): static
    {
        return $this->state(function (array $attributes) {
            $fromAmount = $this->faker->numberBetween(100, 10000);
            $exchangeRate = $this->faker->randomFloat(4, 32.0, 33.0);
            $toAmount = $fromAmount * $exchangeRate;
            
            return [
                'from_currency_code' => 'USD',
                'to_currency_code' => 'TRY',
                'from_amount' => $fromAmount,
                'to_amount' => round($toAmount, 4),
                'exchange_rate' => $exchangeRate,
            ];
        });
    }

    /**
     * Generate EUR to TRY transaction
     */
    public function eurToTry(): static
    {
        return $this->state(function (array $attributes) {
            $fromAmount = $this->faker->numberBetween(100, 8000);
            $exchangeRate = $this->faker->randomFloat(4, 34.5, 36.0);
            $toAmount = $fromAmount * $exchangeRate;
            
            return [
                'from_currency_code' => 'EUR',
                'to_currency_code' => 'TRY',
                'from_amount' => $fromAmount,
                'to_amount' => round($toAmount, 4),
                'exchange_rate' => $exchangeRate,
            ];
        });
    }

    /**
     * Generate TRY to IRR transaction
     */
    public function tryToIrr(): static
    {
        return $this->state(function (array $attributes) {
            $fromAmount = $this->faker->numberBetween(5000, 100000);
            $exchangeRate = $this->faker->randomFloat(2, 1200, 1300); // TRY to IRR rate
            $toAmount = $fromAmount * $exchangeRate;
            
            return [
                'from_currency_code' => 'TRY',
                'to_currency_code' => 'IRR',
                'from_amount' => $fromAmount,
                'to_amount' => round($toAmount, 0), // IRR doesn't use decimals
                'exchange_rate' => $exchangeRate,
            ];
        });
    }

    /**
     * Generate with delivery
     */
    public function withDelivery(): static
    {
        return $this->state(fn (array $attributes) => [
            'delivery_method' => $this->faker->randomElement(['courier', 'pickup']),
            'delivery_address' => $this->faker->address(),
        ]);
    }

    private function getRandomCurrencyPair(): array
    {
        $pairs = [
            ['from' => 'TRY', 'to' => 'USD', 'rate' => $this->faker->randomFloat(6, 0.030, 0.032)],
            ['from' => 'USD', 'to' => 'TRY', 'rate' => $this->faker->randomFloat(4, 32.0, 33.0)],
            ['from' => 'TRY', 'to' => 'EUR', 'rate' => $this->faker->randomFloat(6, 0.028, 0.030)],
            ['from' => 'EUR', 'to' => 'TRY', 'rate' => $this->faker->randomFloat(4, 34.5, 36.0)],
            ['from' => 'TRY', 'to' => 'AED', 'rate' => $this->faker->randomFloat(6, 0.110, 0.115)],
            ['from' => 'AED', 'to' => 'TRY', 'rate' => $this->faker->randomFloat(4, 8.7, 9.0)],
            ['from' => 'TRY', 'to' => 'IRR', 'rate' => $this->faker->randomFloat(2, 1200, 1300)],
            ['from' => 'IRR', 'to' => 'TRY', 'rate' => $this->faker->randomFloat(6, 0.0007, 0.0009)],
            ['from' => 'USD', 'to' => 'EUR', 'rate' => $this->faker->randomFloat(4, 1.05, 1.10)],
            ['from' => 'EUR', 'to' => 'USD', 'rate' => $this->faker->randomFloat(4, 0.90, 0.95)],
        ];
        
        return $this->faker->randomElement($pairs);
    }

    private function generateAmount(string $currency): float
    {
        return match ($currency) {
            'TRY' => $this->faker->numberBetween(1000, 100000),
            'USD' => $this->faker->numberBetween(100, 10000),
            'EUR' => $this->faker->numberBetween(100, 8000),
            'GBP' => $this->faker->numberBetween(100, 7000),
            'AED' => $this->faker->numberBetween(500, 30000),
            'IRR' => $this->faker->numberBetween(10000000, 1000000000),
            'CHF' => $this->faker->numberBetween(100, 8000),
            'JPY' => $this->faker->numberBetween(10000, 1000000),
            default => $this->faker->numberBetween(100, 10000),
        };
    }

    private function generateHighAmount(string $currency): float
    {
        return match ($currency) {
            'TRY' => $this->faker->numberBetween(500000, 5000000),
            'USD' => $this->faker->numberBetween(50000, 500000),
            'EUR' => $this->faker->numberBetween(40000, 400000),
            'GBP' => $this->faker->numberBetween(35000, 350000),
            'AED' => $this->faker->numberBetween(150000, 1500000),
            'IRR' => $this->faker->numberBetween(5000000000, 50000000000),
            'CHF' => $this->faker->numberBetween(40000, 400000),
            'JPY' => $this->faker->numberBetween(5000000, 50000000),
            default => $this->faker->numberBetween(50000, 500000),
        };
    }

    private function generateReferenceNumber(): string
    {
        $prefix = $this->faker->randomElement(['REF', 'TXN', 'EXC', 'ARN']);
        return $prefix . date('Ymd') . $this->faker->numberBetween(1000, 9999);
    }
}
