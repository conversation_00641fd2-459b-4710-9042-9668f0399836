<?php

namespace App\Events\ArenaDoviz;

use App\Models\ArenaDoviz\LocationCurrency;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CurrencyRateUpdated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public LocationCurrency $locationCurrency;
    public ?float $oldBuyRate;
    public ?float $oldSellRate;
    public float $changePercent;
    public string $changeDirection;

    /**
     * Create a new event instance.
     */
    public function __construct(LocationCurrency $locationCurrency, ?float $oldBuyRate = null, ?float $oldSellRate = null)
    {
        $this->locationCurrency = $locationCurrency;
        $this->oldBuyRate = $oldBuyRate;
        $this->oldSellRate = $oldSellRate;
        
        // Calculate change percentage and direction
        if ($oldBuyRate && $oldBuyRate > 0) {
            $this->changePercent = abs(($locationCurrency->buy_rate - $oldBuyRate) / $oldBuyRate) * 100;
            $this->changeDirection = $locationCurrency->buy_rate > $oldBuyRate ? 'up' : 'down';
        } else {
            $this->changePercent = 0;
            $this->changeDirection = 'neutral';
        }
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new Channel('currency-rates'),
            new PrivateChannel('company.' . $this->locationCurrency->company_id . '.currency-rates'),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'currency_code' => $this->locationCurrency->location_currency_code,
            'location_name' => $this->locationCurrency->location->name ?? 'Unknown',
            'buy_rate' => $this->locationCurrency->buy_rate,
            'sell_rate' => $this->locationCurrency->sell_rate,
            'mid_rate' => $this->locationCurrency->mid_rate,
            'old_buy_rate' => $this->oldBuyRate,
            'old_sell_rate' => $this->oldSellRate,
            'change_percent' => round($this->changePercent, 2),
            'change_direction' => $this->changeDirection,
            'updated_at' => $this->locationCurrency->updated_at->toISOString(),
            'formatted_buy_rate' => \App\Helpers\ArenaDoviz\NumberFormatter::formatExchangeRate($this->locationCurrency->buy_rate),
            'formatted_sell_rate' => \App\Helpers\ArenaDoviz\NumberFormatter::formatExchangeRate($this->locationCurrency->sell_rate),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'currency.rate.updated';
    }
}
