class s{static formatNumber(e,o=2,a=",",t="."){if(e==null||e==="")return"";const n=typeof e=="string"?parseFloat(e.replace(/,/g,"")):e;if(isNaN(n))return"";const r=n.toFixed(o).split(".");return r[0]=r[0].replace(/\B(?=(\d{3})+(?!\d))/g,a),r.join(t)}static formatCurrency(e,o="TRY",a=2){const t=this.formatNumber(e,a);if(!t)return"";const n={USD:"$",EUR:"€",TRY:"₺",IRR:"﷼",AED:"د.إ",CNY:"¥"},i=o.split("_")[0],r=n[i]||i;return`${t} ${r}`}static parseNumber(e){return!e||typeof e!="string"?0:parseFloat(e.replace(/,/g,""))||0}static initializeInputFormatting(e=".amount-input"){document.addEventListener("DOMContentLoaded",function(){document.querySelectorAll(e).forEach(a=>{if(a.addEventListener("blur",function(){const t=s.parseNumber(this.value);t>0&&(this.value=s.formatNumber(t))}),a.addEventListener("input",function(t){let n=t.target.value;n=n.replace(/[^\d,.-]/g,""),(n.match(/\./g)||[]).length>1&&(n=n.substring(0,n.lastIndexOf("."))),t.target.value=n}),a.value){const t=s.parseNumber(a.value);t>0&&(a.value=s.formatNumber(t))}})})}static formatDisplayAmounts(e=".amount-display"){document.addEventListener("DOMContentLoaded",function(){document.querySelectorAll(e).forEach(a=>{const t=a.textContent||a.innerText,n=a.dataset.currency||"TRY",i=parseInt(a.dataset.decimals)||2;t&&!isNaN(parseFloat(t))&&(a.textContent=s.formatCurrency(t,n,i))})})}static initializeRealTimeFormatting(e=".amount-input-realtime"){document.addEventListener("DOMContentLoaded",function(){document.querySelectorAll(e).forEach(a=>{a.addEventListener("input",function(t){let n=t.target.value;n=n.replace(/[^\d.]/g,"");const i=parseFloat(n);if(!isNaN(i)&&i>0){const r=t.target.selectionStart,l=t.target.value.length,c=s.formatNumber(i,2);t.target.value=c;const m=c.length,u=r+(m-l);t.target.setSelectionRange(u,u)}})})})}static initialize(){this.initializeInputFormatting(),this.formatDisplayAmounts(),this.initializeRealTimeFormatting()}}s.initialize();window.ArenaDovizFormatter=s;
