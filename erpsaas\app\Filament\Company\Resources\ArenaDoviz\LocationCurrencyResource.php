<?php

namespace App\Filament\Company\Resources\ArenaDoviz;

use App\Filament\Company\Resources\ArenaDoviz\LocationCurrencyResource\Pages;
use App\Models\ArenaDoviz\LocationCurrency;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class LocationCurrencyResource extends Resource
{
    protected static ?string $model = LocationCurrency::class;

    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';

    protected static ?string $navigationLabel = 'Location Currencies';

    protected static ?string $modelLabel = 'Location Currency';

    protected static ?string $pluralModelLabel = 'Location Currencies';

    protected static ?string $navigationGroup = 'Arena Doviz';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Currency Details')
                    ->schema([
                        Forms\Components\Select::make('location_id')
                            ->label('Location')
                            ->relationship('location', 'name')
                            ->required()
                            ->searchable()
                            ->preload(),

                        Forms\Components\TextInput::make('base_currency_code')
                            ->label('Base Currency Code')
                            ->required()
                            ->maxLength(10)
                            ->placeholder('USD, EUR, TRY, IRR, AED'),

                        Forms\Components\TextInput::make('location_currency_code')
                            ->label('Location Currency Code')
                            ->required()
                            ->maxLength(10)
                            ->unique(ignoreRecord: true)
                            ->placeholder('USD_IST, EUR_TBZ, IRR_THR'),

                        Forms\Components\TextInput::make('name')
                            ->label('Currency Name')
                            ->required()
                            ->maxLength(100)
                            ->placeholder('US Dollar Istanbul'),

                        Forms\Components\TextInput::make('symbol')
                            ->label('Currency Symbol')
                            ->maxLength(10)
                            ->placeholder('$, €, ₺, ﷼'),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true),
                    ])->columns(2),

                Forms\Components\Section::make('Exchange Rates')
                    ->schema([
                        Forms\Components\TextInput::make('buy_rate')
                            ->label('Buy Rate')
                            ->numeric()
                            ->step(0.000001)
                            ->required()
                            ->helperText('Rate for buying this currency'),

                        Forms\Components\TextInput::make('sell_rate')
                            ->label('Sell Rate')
                            ->numeric()
                            ->step(0.000001)
                            ->required()
                            ->helperText('Rate for selling this currency'),

                        Forms\Components\TextInput::make('mid_rate')
                            ->label('Mid Rate')
                            ->numeric()
                            ->step(0.000001)
                            ->helperText('Middle rate (calculated automatically)'),

                        Forms\Components\DateTimePicker::make('rate_date')
                            ->label('Rate Date')
                            ->default(now()),

                        Forms\Components\TextInput::make('rate_source')
                            ->label('Rate Source')
                            ->maxLength(50)
                            ->placeholder('manual, api, bank_feed'),

                        Forms\Components\TextInput::make('decimal_places')
                            ->label('Decimal Places')
                            ->numeric()
                            ->default(2)
                            ->minValue(0)
                            ->maxValue(6),
                    ])->columns(2),

                Forms\Components\Section::make('Transaction Limits')
                    ->schema([
                        Forms\Components\TextInput::make('daily_limit')
                            ->label('Daily Transaction Limit')
                            ->numeric()
                            ->step(0.01),

                        Forms\Components\TextInput::make('min_transaction')
                            ->label('Minimum Transaction')
                            ->numeric()
                            ->step(0.01),

                        Forms\Components\TextInput::make('max_transaction')
                            ->label('Maximum Transaction')
                            ->numeric()
                            ->step(0.01),

                        Forms\Components\TextInput::make('commission_rate')
                            ->label('Default Commission Rate (%)')
                            ->numeric()
                            ->step(0.01)
                            ->suffix('%'),
                    ])->columns(2),

                Forms\Components\Section::make('Additional Information')
                    ->schema([
                        Forms\Components\Textarea::make('notes')
                            ->label('Notes')
                            ->rows(3)
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('location.name')
                    ->label('Location')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('location_currency_code')
                    ->label('Currency Code')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color('primary'),

                Tables\Columns\TextColumn::make('name')
                    ->label('Currency Name')
                    ->searchable()
                    ->limit(30),

                Tables\Columns\TextColumn::make('symbol')
                    ->label('Symbol')
                    ->badge()
                    ->color('gray'),

                Tables\Columns\TextColumn::make('buy_rate')
                    ->label('Buy Rate')
                    ->numeric(decimalPlaces: 6)
                    ->sortable(),

                Tables\Columns\TextColumn::make('sell_rate')
                    ->label('Sell Rate')
                    ->numeric(decimalPlaces: 6)
                    ->sortable(),

                Tables\Columns\TextColumn::make('rate_date')
                    ->label('Rate Date')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean(),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Last Updated')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('location_id')
                    ->label('Location')
                    ->relationship('location', 'name'),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active'),

                Tables\Filters\SelectFilter::make('base_currency_code')
                    ->label('Base Currency')
                    ->options([
                        'USD' => 'USD',
                        'EUR' => 'EUR',
                        'TRY' => 'TRY',
                        'IRR' => 'IRR',
                        'AED' => 'AED',
                        'CNY' => 'CNY',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

                Tables\Actions\Action::make('update_rates')
                    ->label('Update Rates')
                    ->icon('heroicon-o-arrow-path')
                    ->color('warning')
                    ->action(function (LocationCurrency $record) {
                        // This could integrate with an external API to update rates
                        $record->update([
                            'rate_date' => now(),
                            'rate_source' => 'manual_update',
                        ]);
                    })
                    ->requiresConfirmation(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('location_currency_code');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLocationCurrencies::route('/'),
            'create' => Pages\CreateLocationCurrency::route('/create'),
            'view' => Pages\ViewLocationCurrency::route('/{record}'),
            'edit' => Pages\EditLocationCurrency::route('/{record}/edit'),
        ];
    }
}
