<?php

namespace App\Filament\Company\Resources\ArenaDoviz;

use App\Enums\ArenaDoviz\DebitNoteStatus;
use App\Filament\Company\Resources\ArenaDoviz\DebitNoteResource\Pages;
use App\Models\ArenaDoviz\DebitNote;
use App\Models\Common\Client;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class DebitNoteResource extends Resource
{
    protected static ?string $model = DebitNote::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationLabel = 'Debit Notes';

    protected static ?string $modelLabel = 'Debit Note';

    protected static ?string $pluralModelLabel = 'Debit Notes';

    protected static ?string $navigationGroup = 'Arena Doviz';

    protected static ?int $navigationSort = 4;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Debit Note Details')
                    ->schema([
                        Forms\Components\Select::make('client_id')
                            ->label('Client')
                            ->options(Client::pluck('name', 'id'))
                            ->searchable()
                            ->required(),

                        Forms\Components\Select::make('location_id')
                            ->label('Location')
                            ->relationship('location', 'name')
                            ->searchable()
                            ->preload(),

                        Forms\Components\Select::make('debit_type')
                            ->label('Debit Type')
                            ->options([
                                'delivery' => 'Delivery',
                                'cash_collection' => 'Cash Collection',
                                'document_delivery' => 'Document Delivery',
                                'service_charge' => 'Service Charge',
                                'penalty' => 'Penalty',
                                'adjustment' => 'Adjustment',
                            ])
                            ->required()
                            ->default('delivery'),

                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options(DebitNoteStatus::class)
                            ->default(DebitNoteStatus::PENDING)
                            ->required(),
                    ])->columns(2),

                Forms\Components\Section::make('Financial Information')
                    ->schema([
                        Forms\Components\TextInput::make('currency_code')
                            ->label('Currency')
                            ->default('TRY')
                            ->required()
                            ->maxLength(10),

                        Forms\Components\TextInput::make('amount')
                            ->label('Amount')
                            ->numeric()
                            ->step(0.01)
                            ->required(),

                        Forms\Components\Textarea::make('description')
                            ->label('Description')
                            ->required()
                            ->rows(3)
                            ->columnSpanFull(),
                    ])->columns(2),

                Forms\Components\Section::make('Recipient Information')
                    ->schema([
                        Forms\Components\TextInput::make('recipient_name')
                            ->label('Recipient Name')
                            ->required()
                            ->maxLength(100),

                        Forms\Components\TextInput::make('recipient_phone')
                            ->label('Recipient Phone')
                            ->tel()
                            ->maxLength(20),

                        Forms\Components\TextInput::make('recipient_email')
                            ->label('Recipient Email')
                            ->email()
                            ->maxLength(100),

                        Forms\Components\Textarea::make('delivery_address')
                            ->label('Delivery Address')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])->columns(2),

                Forms\Components\Section::make('Scheduling')
                    ->schema([
                        Forms\Components\DateTimePicker::make('scheduled_date')
                            ->label('Scheduled Date')
                            ->default(now()->addDay()),

                        Forms\Components\DateTimePicker::make('completed_date')
                            ->label('Completed Date')
                            ->disabled()
                            ->dehydrated(false),
                    ])->columns(2),

                Forms\Components\Section::make('Documents & Verification')
                    ->schema([
                        Forms\Components\FileUpload::make('signature_path')
                            ->label('Digital Signature')
                            ->image()
                            ->directory('debit-notes/signatures')
                            ->visibility('private'),

                        Forms\Components\FileUpload::make('photo_path')
                            ->label('Delivery Proof Photo')
                            ->image()
                            ->directory('debit-notes/photos')
                            ->visibility('private'),

                        Forms\Components\FileUpload::make('receipt_path')
                            ->label('Receipt/Invoice')
                            ->acceptedFileTypes(['application/pdf', 'image/*'])
                            ->directory('debit-notes/receipts')
                            ->visibility('private'),

                        Forms\Components\TextInput::make('verification_code')
                            ->label('Verification Code')
                            ->disabled()
                            ->dehydrated(false)
                            ->helperText('Auto-generated for recipient verification'),
                    ])->columns(2),

                Forms\Components\Section::make('Notes & Instructions')
                    ->schema([
                        Forms\Components\Textarea::make('special_instructions')
                            ->label('Special Instructions')
                            ->rows(2),

                        Forms\Components\Textarea::make('courier_notes')
                            ->label('Courier Notes')
                            ->rows(2),

                        Forms\Components\Textarea::make('client_notes')
                            ->label('Client Notes')
                            ->rows(2),

                        Forms\Components\Textarea::make('internal_notes')
                            ->label('Internal Notes')
                            ->rows(2),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('debit_note_number')
                    ->label('Debit Note #')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('client.name')
                    ->label('Client')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('debit_type')
                    ->label('Type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'delivery' => 'info',
                        'cash_collection' => 'warning',
                        'document_delivery' => 'primary',
                        'service_charge' => 'success',
                        'penalty' => 'danger',
                        'adjustment' => 'gray',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('formatted_amount')
                    ->label('Amount')
                    ->sortable('amount'),

                Tables\Columns\TextColumn::make('recipient_name')
                    ->label('Recipient')
                    ->searchable()
                    ->limit(30),

                Tables\Columns\TextColumn::make('scheduled_date')
                    ->label('Scheduled')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->sortable(),

                Tables\Columns\IconColumn::make('has_signature')
                    ->label('Signed')
                    ->boolean()
                    ->getStateUsing(fn (DebitNote $record): bool => !empty($record->signature_path)),

                Tables\Columns\IconColumn::make('has_photo')
                    ->label('Photo')
                    ->boolean()
                    ->getStateUsing(fn (DebitNote $record): bool => !empty($record->photo_path)),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options(DebitNoteStatus::class),

                Tables\Filters\SelectFilter::make('debit_type')
                    ->options([
                        'delivery' => 'Delivery',
                        'cash_collection' => 'Cash Collection',
                        'document_delivery' => 'Document Delivery',
                        'service_charge' => 'Service Charge',
                        'penalty' => 'Penalty',
                        'adjustment' => 'Adjustment',
                    ]),

                Tables\Filters\Filter::make('overdue')
                    ->query(fn (Builder $query): Builder => $query->overdue())
                    ->label('Overdue'),

                Tables\Filters\Filter::make('today')
                    ->query(fn (Builder $query): Builder => $query->today())
                    ->label('Today'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                
                Tables\Actions\Action::make('complete')
                    ->label('Mark Complete')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->visible(fn (DebitNote $record): bool => $record->status->canTransitionTo(DebitNoteStatus::COMPLETED))
                    ->action(function (DebitNote $record) {
                        $record->update([
                            'status' => DebitNoteStatus::COMPLETED,
                            'completed_date' => now(),
                        ]);
                    })
                    ->requiresConfirmation(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDebitNotes::route('/'),
            'create' => Pages\CreateDebitNote::route('/create'),
            'view' => Pages\ViewDebitNote::route('/{record}'),
            'edit' => Pages\EditDebitNote::route('/{record}/edit'),
        ];
    }
}
