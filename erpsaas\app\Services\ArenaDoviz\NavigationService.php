<?php

namespace App\Services\ArenaDoviz;

use App\Models\ArenaDoviz\CurrencyExchange;
use App\Models\Common\Client;
use Illuminate\Support\Facades\Auth;

class NavigationService
{
    /**
     * Get navigation badges for Arena Doviz resources
     */
    public function getNavigationBadges(): array
    {
        $user = Auth::user();
        if (!$user || !$user->currentCompany) {
            return [];
        }

        $companyId = $user->currentCompany->id;

        return [
            'pending_transactions' => $this->getPendingTransactionsCount($companyId),
            'pending_approvals' => $this->getPendingApprovalsCount($companyId),
            'active_clients' => $this->getActiveClientsCount($companyId),
            'low_balances' => $this->getLowBalancesCount($companyId),
        ];
    }

    /**
     * Get count of pending transactions
     */
    private function getPendingTransactionsCount(int $companyId): int
    {
        return CurrencyExchange::where('company_id', $companyId)
            ->where('status', 'pending')
            ->count();
    }

    /**
     * Get count of transactions pending approval
     */
    private function getPendingApprovalsCount(int $companyId): int
    {
        return CurrencyExchange::where('company_id', $companyId)
            ->where('status', 'pending_approval')
            ->count();
    }

    /**
     * Get count of active clients (clients with transactions in last 30 days)
     */
    private function getActiveClientsCount(int $companyId): int
    {
        return Client::where('company_id', $companyId)
            ->whereHas('currencyExchanges', function ($query) {
                $query->where('exchange_date', '>=', now()->subDays(30));
            })
            ->count();
    }

    /**
     * Get count of clients with low balances
     */
    private function getLowBalancesCount(int $companyId): int
    {
        return Client::where('company_id', $companyId)
            ->whereHas('arenaProfile', function ($query) {
                $query->where('credit_limit', '>', 0)
                    ->whereRaw('credit_limit < expected_monthly_volume * 0.1');
            })
            ->count();
    }

    /**
     * Check if user has permission for Arena Doviz operations
     */
    public function hasArenaDovizAccess(): bool
    {
        $user = Auth::user();
        if (!$user) {
            return false;
        }

        return $user->hasAnyRole(['admin', 'exchange_manager', 'exchange_operator', 'cashier']);
    }

    /**
     * Get quick action items for dashboard
     */
    public function getQuickActions(): array
    {
        $user = Auth::user();
        if (!$user) {
            return [];
        }

        $actions = [];

        if ($user->can('exchange:create')) {
            $actions[] = [
                'label' => 'New Buy Transaction',
                'icon' => 'heroicon-o-arrow-down-circle',
                'color' => 'success',
                'url' => \App\Filament\Company\Resources\CurrencyExchangeResource::getUrl('create', ['type' => 'buy']),
            ];

            $actions[] = [
                'label' => 'New Sell Transaction',
                'icon' => 'heroicon-o-arrow-up-circle',
                'color' => 'danger',
                'url' => \App\Filament\Company\Resources\CurrencyExchangeResource::getUrl('create', ['type' => 'sell']),
            ];

            $actions[] = [
                'label' => 'Transfer',
                'icon' => 'heroicon-o-arrow-right-circle',
                'color' => 'info',
                'url' => \App\Filament\Company\Resources\CurrencyExchangeResource::getUrl('create', ['type' => 'transfer']),
            ];

            $actions[] = [
                'label' => 'Cash Deposit',
                'icon' => 'heroicon-o-banknotes',
                'color' => 'warning',
                'url' => \App\Filament\Company\Resources\ArenaDoviz\ClientBalanceResource::getUrl('create'),
            ];
        }

        return $actions;
    }

    /**
     * Get navigation items with proper badges
     */
    public function getNavigationItemsWithBadges(): array
    {
        $badges = $this->getNavigationBadges();

        return [
            'currency_exchanges' => [
                'badge' => $badges['pending_transactions'] > 0 ? $badges['pending_transactions'] : null,
                'badge_color' => 'warning',
            ],
            'client_profiles' => [
                'badge' => $badges['active_clients'] > 0 ? $badges['active_clients'] : null,
                'badge_color' => 'success',
            ],
            'client_balances' => [
                'badge' => $badges['low_balances'] > 0 ? $badges['low_balances'] : null,
                'badge_color' => 'danger',
            ],
        ];
    }
}
