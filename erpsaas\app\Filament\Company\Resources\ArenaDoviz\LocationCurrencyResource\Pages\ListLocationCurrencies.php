<?php

namespace App\Filament\Company\Resources\ArenaDoviz\LocationCurrencyResource\Pages;

use App\Filament\Company\Resources\ArenaDoviz\LocationCurrencyResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListLocationCurrencies extends ListRecords
{
    protected static string $resource = LocationCurrencyResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),

            Actions\Action::make('export')
                ->label('Export Rates')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('success')
                ->action(function () {
                    $exportService = app(\App\Services\ArenaDoviz\ExportService::class);
                    $path = $exportService->exportLocationCurrencies('xlsx');
                    $url = $exportService->getExportUrl($path);

                    $this->js("window.open('{$url}', '_blank')");
                }),

            Actions\Action::make('import')
                ->label('Import Rates')
                ->icon('heroicon-o-arrow-up-tray')
                ->color('warning')
                ->form([
                    \Filament\Forms\Components\FileUpload::make('file')
                        ->label('Import File')
                        ->acceptedFileTypes(['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel', 'text/csv'])
                        ->required()
                        ->helperText('Upload Excel (.xlsx, .xls) or CSV file with currency rates'),
                ])
                ->action(function (array $data) {
                    $importService = app(\App\Services\ArenaDoviz\ImportService::class);
                    $companyId = auth()->user()->currentCompany->id;

                    $file = $data['file'];
                    $results = $importService->importCurrencyRates($file, $companyId);

                    if ($results['errors'] > 0) {
                        \Filament\Notifications\Notification::make()
                            ->title('Import completed with errors')
                            ->body("Success: {$results['success']}, Errors: {$results['errors']}, Skipped: {$results['skipped']}")
                            ->warning()
                            ->send();
                    } else {
                        \Filament\Notifications\Notification::make()
                            ->title('Import successful')
                            ->body("Successfully imported {$results['success']} rates")
                            ->success()
                            ->send();
                    }
                }),
        ];
    }
}
