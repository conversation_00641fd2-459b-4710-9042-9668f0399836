<?php

namespace App\Filament\Company\Resources\ArenaDoviz\CombinedExchangeResource\Pages;

use App\Filament\Company\Resources\ArenaDoviz\CombinedExchangeResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewCombinedExchange extends ViewRecord
{
    protected static string $resource = CombinedExchangeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
