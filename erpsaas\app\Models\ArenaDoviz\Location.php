<?php

namespace App\Models\ArenaDoviz;

use App\Concerns\Blamable;
use App\Concerns\CompanyOwned;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Location extends Model
{
    use Blamable;
    use CompanyOwned;
    use HasFactory;
    use SoftDeletes;

    protected $table = 'locations';

    protected $fillable = [
        'company_id',
        'name',
        'code',
        'city',
        'country',
        'address',
        'timezone',
        'phone',
        'email',
        'manager_name',
        'working_hours',
        'services',
        'max_transaction_amount',
        'is_active',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'working_hours' => 'array',
        'services' => 'array',
        'max_transaction_amount' => 'decimal:2',
    ];

    /**
     * Get all currency exchanges for this location
     */
    public function currencyExchanges(): HasMany
    {
        return $this->hasMany(CurrencyExchange::class, 'location_id');
    }

    /**
     * Get location-specific currencies (e.g., USD_IST, EUR_IST)
     */
    public function locationCurrencies(): HasMany
    {
        return $this->hasMany(LocationCurrency::class, 'location_id');
    }

    /**
     * Get formatted location display name
     */
    public function getDisplayNameAttribute(): string
    {
        return "{$this->name} ({$this->code})";
    }

    /**
     * Check if location is currently open based on working hours
     */
    public function isCurrentlyOpen(): bool
    {
        if (!$this->is_active || !$this->working_hours) {
            return false;
        }

        $currentDay = strtolower(now($this->timezone)->format('l'));
        $currentTime = now($this->timezone)->format('H:i');

        $todayHours = $this->working_hours[$currentDay] ?? null;

        if (!$todayHours || $todayHours === 'closed') {
            return false;
        }

        [$openTime, $closeTime] = $todayHours;

        return $currentTime >= $openTime && $currentTime <= $closeTime;
    }

    /**
     * Get active locations
     */
    public static function active()
    {
        return static::where('is_active', true);
    }

    /**
     * Get main office location
     */
    public static function mainOffice()
    {
        return static::where('is_main_office', true)->first();
    }
}
