<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Report Form -->
        <x-filament::section>
            <x-slot name="heading">
                Report Configuration
            </x-slot>
            
            {{ $this->form }}
        </x-filament::section>

        <!-- Report Content -->
        @if($reportData)
            @if(isset($reportData['error']))
                <x-filament::section>
                    <div class="text-center py-8">
                        <div class="text-red-500 text-lg font-semibold">Error generating report</div>
                        <div class="text-gray-600 mt-2">{{ $reportData['error'] }}</div>
                    </div>
                </x-filament::section>
            @else
                @switch($reportType)
                    @case('dashboard')
                        @include('filament.company.reports.dashboard', ['data' => $reportData])
                        @break
                    
                    @case('profit_loss')
                        @include('filament.company.reports.profit-loss', ['data' => $reportData])
                        @break
                    
                    @case('balance_sheet')
                        @include('filament.company.reports.balance-sheet', ['data' => $reportData])
                        @break
                    
                    @case('cash_flow')
                        @include('filament.company.reports.cash-flow', ['data' => $reportData])
                        @break
                    
                    @case('transaction_summary')
                        @include('filament.company.reports.transaction-summary', ['data' => $reportData])
                        @break
                    
                    @case('commission_analysis')
                        @include('filament.company.reports.commission-analysis', ['data' => $reportData])
                        @break
                    
                    @case('client_profitability')
                        @include('filament.company.reports.client-profitability', ['data' => $reportData])
                        @break
                    
                    @default
                        <x-filament::section>
                            <div class="text-center py-8">
                                <div class="text-gray-500 text-lg">Select a report type to view data</div>
                            </div>
                        </x-filament::section>
                @endswitch
            @endif
        @else
            <x-filament::section>
                <div class="text-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
                    <div class="text-gray-500 mt-4">Generating report...</div>
                </div>
            </x-filament::section>
        @endif
    </div>
</x-filament-panels::page>
