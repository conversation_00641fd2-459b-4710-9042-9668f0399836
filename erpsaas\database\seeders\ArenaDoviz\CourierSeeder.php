<?php

namespace Database\Seeders\ArenaDoviz;

use App\Enums\ArenaDoviz\CourierStatus;
use App\Models\ArenaDoviz\Courier;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Auth;

class CourierSeeder extends Seeder
{
    /**
     * Seed courier profiles for Arena Doviz delivery operations.
     * 
     * Creates realistic courier data including:
     * - Active couriers with vehicle information
     * - Service areas and working hours
     * - Emergency contacts and delivery limits
     * - Different courier statuses
     */
    public function run(): void
    {
        $this->command->info('🚚 Seeding Arena Doviz couriers...');

        // Set a temporary authenticated user for the CompanyOwned trait
        $adminUser = \App\Models\User::where('email', '<EMAIL>')->first();
        if ($adminUser) {
            Auth::login($adminUser);
        }

        $couriersData = [
            [
                'user_id' => $this->getCourierUserId('<EMAIL>'),
                'name' => '<PERSON><PERSON>',
                'phone' => '+90 ************',
                'email' => '<EMAIL>',
                'status' => CourierStatus::AVAILABLE,
                'vehicle_type' => 'Motorcycle',
                'vehicle_plate' => '34 ABC 123',
                'license_number' => 'B123456789',
                'emergency_contact' => 'Ayşe Şahin',
                'emergency_phone' => '+90 ************',
                'max_delivery_amount' => 50000.00,
                'service_areas' => ['Şişli', 'Beşiktaş', 'Kadıköy', 'Üsküdar'],
                'working_hours' => [
                    'monday' => ['09:00', '18:00'],
                    'tuesday' => ['09:00', '18:00'],
                    'wednesday' => ['09:00', '18:00'],
                    'thursday' => ['09:00', '18:00'],
                    'friday' => ['09:00', '18:00'],
                    'saturday' => ['09:00', '17:00'],
                    'sunday' => 'off'
                ],
                'notes' => 'Experienced courier with excellent delivery record. Specializes in high-value deliveries.',
            ],
            
            [
                'user_id' => $this->getCourierUserId('<EMAIL>'),
                'name' => 'Kemal Özdemir',
                'phone' => '+90 ************',
                'email' => '<EMAIL>',
                'status' => CourierStatus::ACTIVE,
                'vehicle_type' => 'Car',
                'vehicle_plate' => '34 DEF 456',
                'license_number' => 'B987654321',
                'emergency_contact' => 'Fatma Özdemir',
                'emergency_phone' => '+90 ************',
                'max_delivery_amount' => 75000.00,
                'service_areas' => ['Levent', 'Etiler', 'Nişantaşı', 'Beyoğlu', 'Galata'],
                'working_hours' => [
                    'monday' => ['08:00', '17:00'],
                    'tuesday' => ['08:00', '17:00'],
                    'wednesday' => ['08:00', '17:00'],
                    'thursday' => ['08:00', '17:00'],
                    'friday' => ['08:00', '17:00'],
                    'saturday' => ['09:00', '15:00'],
                    'sunday' => 'off'
                ],
                'notes' => 'Reliable courier with car for larger deliveries. Covers premium areas.',
            ],
            
            [
                'user_id' => null,
                'name' => 'Ahmet Yıldız',
                'phone' => '+90 ************',
                'email' => '<EMAIL>',
                'status' => CourierStatus::BUSY,
                'vehicle_type' => 'Motorcycle',
                'vehicle_plate' => '34 GHI 789',
                'license_number' => 'B456789123',
                'emergency_contact' => 'Zeynep Yıldız',
                'emergency_phone' => '+90 ************',
                'max_delivery_amount' => 40000.00,
                'service_areas' => ['Bakırköy', 'Yeşilköy', 'Ataköy', 'Florya'],
                'working_hours' => [
                    'monday' => ['10:00', '19:00'],
                    'tuesday' => ['10:00', '19:00'],
                    'wednesday' => ['10:00', '19:00'],
                    'thursday' => ['10:00', '19:00'],
                    'friday' => ['10:00', '19:00'],
                    'saturday' => ['10:00', '18:00'],
                    'sunday' => 'off'
                ],
                'notes' => 'Currently on delivery. Covers western Istanbul areas.',
            ],
            
            [
                'user_id' => null,
                'name' => 'Mehmet Kaya',
                'phone' => '+90 ************',
                'email' => '<EMAIL>',
                'status' => CourierStatus::AVAILABLE,
                'vehicle_type' => 'Scooter',
                'vehicle_plate' => '34 JKL 012',
                'license_number' => 'A123456789',
                'emergency_contact' => 'Elif Kaya',
                'emergency_phone' => '+90 ************',
                'max_delivery_amount' => 25000.00,
                'service_areas' => ['Fatih', 'Eminönü', 'Beyazıt', 'Sultanahmet'],
                'working_hours' => [
                    'monday' => ['09:00', '18:00'],
                    'tuesday' => ['09:00', '18:00'],
                    'wednesday' => ['09:00', '18:00'],
                    'thursday' => ['09:00', '18:00'],
                    'friday' => ['09:00', '18:00'],
                    'saturday' => ['09:00', '16:00'],
                    'sunday' => 'off'
                ],
                'notes' => 'Covers historic peninsula and central areas. Good for smaller deliveries.',
            ],
            
            [
                'user_id' => null,
                'name' => 'Osman Demir',
                'phone' => '+90 ************',
                'email' => '<EMAIL>',
                'status' => CourierStatus::OFF_DUTY,
                'vehicle_type' => 'Van',
                'vehicle_plate' => '34 MNO 345',
                'license_number' => 'B789123456',
                'emergency_contact' => 'Hatice Demir',
                'emergency_phone' => '+90 ************',
                'max_delivery_amount' => 100000.00,
                'service_areas' => ['Maslak', 'Sarıyer', 'Tarabya', 'Büyükdere'],
                'working_hours' => [
                    'monday' => ['08:00', '16:00'],
                    'tuesday' => ['08:00', '16:00'],
                    'wednesday' => ['08:00', '16:00'],
                    'thursday' => ['08:00', '16:00'],
                    'friday' => ['08:00', '16:00'],
                    'saturday' => 'off',
                    'sunday' => 'off'
                ],
                'notes' => 'Van driver for large deliveries. Currently off duty. Covers northern areas.',
            ],
            
            [
                'user_id' => null,
                'name' => 'Hasan Özkan',
                'phone' => '+90 ************',
                'email' => '<EMAIL>',
                'status' => CourierStatus::INACTIVE,
                'vehicle_type' => 'Motorcycle',
                'vehicle_plate' => '34 PQR 678',
                'license_number' => 'B321654987',
                'emergency_contact' => 'Ayşe Özkan',
                'emergency_phone' => '+90 ************',
                'max_delivery_amount' => 35000.00,
                'service_areas' => ['Pendik', 'Kartal', 'Maltepe', 'Bostancı'],
                'working_hours' => [
                    'monday' => ['09:00', '18:00'],
                    'tuesday' => ['09:00', '18:00'],
                    'wednesday' => ['09:00', '18:00'],
                    'thursday' => ['09:00', '18:00'],
                    'friday' => ['09:00', '18:00'],
                    'saturday' => ['09:00', '17:00'],
                    'sunday' => 'off'
                ],
                'notes' => 'Currently inactive due to vehicle maintenance. Covers Asian side areas.',
            ],
            
            // Part-time couriers
            [
                'user_id' => null,
                'name' => 'Ali Yılmaz',
                'phone' => '+90 ************',
                'email' => '<EMAIL>',
                'status' => CourierStatus::AVAILABLE,
                'vehicle_type' => 'Bicycle',
                'vehicle_plate' => null,
                'license_number' => null,
                'emergency_contact' => 'Fatma Yılmaz',
                'emergency_phone' => '+90 ************',
                'max_delivery_amount' => 10000.00,
                'service_areas' => ['Taksim', 'Galata', 'Karaköy', 'Cihangir'],
                'working_hours' => [
                    'monday' => 'off',
                    'tuesday' => 'off',
                    'wednesday' => 'off',
                    'thursday' => 'off',
                    'friday' => 'off',
                    'saturday' => ['10:00', '18:00'],
                    'sunday' => ['10:00', '18:00']
                ],
                'notes' => 'Weekend part-time courier. Bicycle delivery for small amounts in central areas.',
            ],
            
            [
                'user_id' => null,
                'name' => 'Emre Kılıç',
                'phone' => '+90 ************',
                'email' => '<EMAIL>',
                'status' => CourierStatus::AVAILABLE,
                'vehicle_type' => 'Motorcycle',
                'vehicle_plate' => '34 STU 901',
                'license_number' => 'B654321987',
                'emergency_contact' => 'Seda Kılıç',
                'emergency_phone' => '+90 ************',
                'max_delivery_amount' => 45000.00,
                'service_areas' => ['Bağcılar', 'Güngören', 'Bahçelievler', 'Yenibosna'],
                'working_hours' => [
                    'monday' => ['14:00', '22:00'],
                    'tuesday' => ['14:00', '22:00'],
                    'wednesday' => ['14:00', '22:00'],
                    'thursday' => ['14:00', '22:00'],
                    'friday' => ['14:00', '22:00'],
                    'saturday' => 'off',
                    'sunday' => 'off'
                ],
                'notes' => 'Evening shift courier. Covers western residential areas.',
            ],
        ];
        
        foreach ($couriersData as $courierData) {
            Courier::create(array_merge($courierData, [
                'company_id' => 1,
                'created_by' => 1, // Admin user
            ]));
        }

        // Logout the temporary user
        Auth::logout();

        $this->command->info('✅ Created ' . count($couriersData) . ' courier profiles');
    }
    
    private function getCourierUserId(string $email): ?int
    {
        $user = User::where('email', $email)->first();
        return $user?->id;
    }
}
