<div class="space-y-6">
    <!-- Summary Overview -->
    <x-filament::section>
        <x-slot name="heading">
            <div class="flex items-center gap-2">
                <x-heroicon-o-document-chart-bar class="w-5 h-5" />
                Transaction Summary Report
            </div>
        </x-slot>
        
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg text-center">
                <div class="text-blue-600 dark:text-blue-400 text-sm font-medium">Total Transactions</div>
                <div class="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {{ number_format($data['total_transactions']) }}
                </div>
            </div>
            
            <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg text-center">
                <div class="text-green-600 dark:text-green-400 text-sm font-medium">Total Volume</div>
                <div class="text-2xl font-bold text-green-900 dark:text-green-100">
                    {{ number_format($data['total_volume'], 2) }} TRY
                </div>
            </div>
            
            <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg text-center">
                <div class="text-yellow-600 dark:text-yellow-400 text-sm font-medium">Total Commission</div>
                <div class="text-2xl font-bold text-yellow-900 dark:text-yellow-100">
                    {{ number_format($data['total_commission'], 2) }} TRY
                </div>
            </div>
            
            <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg text-center">
                <div class="text-purple-600 dark:text-purple-400 text-sm font-medium">Avg Transaction</div>
                <div class="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {{ $data['total_transactions'] > 0 ? number_format($data['total_volume'] / $data['total_transactions'], 2) : '0.00' }} TRY
                </div>
            </div>
        </div>
    </x-filament::section>

    <!-- Breakdown by Type -->
    <x-filament::section>
        <x-slot name="heading">Transactions by Type</x-slot>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-800">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Transaction Type
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Count
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Volume (TRY)
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Commission (TRY)
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Avg Amount (TRY)
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            % of Total
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($data['by_type'] as $type => $stats)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100 capitalize">
                                {{ str_replace('_', ' ', $type) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {{ number_format($stats['count']) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {{ number_format($stats['volume'], 2) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600 dark:text-green-400">
                                {{ number_format($stats['commission'], 2) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {{ number_format($stats['avg_amount'], 2) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {{ $data['total_volume'] > 0 ? number_format(($stats['volume'] / $data['total_volume']) * 100, 1) : '0.0' }}%
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </x-filament::section>

    <!-- Breakdown by Currency -->
    <x-filament::section>
        <x-slot name="heading">Transactions by Currency</x-slot>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-800">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Currency
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Count
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Volume
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Commission (TRY)
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            % of Total
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($data['by_currency'] as $currency => $stats)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                                {{ $currency }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {{ number_format($stats['count']) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {{ number_format($stats['volume'], 2) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600 dark:text-green-400">
                                {{ number_format($stats['commission'], 2) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {{ $data['total_volume'] > 0 ? number_format(($stats['volume'] / $data['total_volume']) * 100, 1) : '0.0' }}%
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </x-filament::section>

    <!-- Status Distribution -->
    <x-filament::section>
        <x-slot name="heading">Transaction Status Distribution</x-slot>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            @foreach($data['by_status'] as $status => $stats)
                @php
                    $statusColors = [
                        'pending' => 'yellow',
                        'processing' => 'blue',
                        'completed' => 'green',
                        'cancelled' => 'gray',
                        'failed' => 'red',
                    ];
                    $color = $statusColors[$status] ?? 'gray';
                @endphp
                
                <div class="bg-{{ $color }}-50 dark:bg-{{ $color }}-900/20 p-4 rounded-lg text-center">
                    <div class="text-{{ $color }}-600 dark:text-{{ $color }}-400 text-sm font-medium capitalize">
                        {{ str_replace('_', ' ', $status) }}
                    </div>
                    <div class="text-xl font-bold text-{{ $color }}-900 dark:text-{{ $color }}-100">
                        {{ number_format($stats['count']) }}
                    </div>
                    <div class="text-xs text-{{ $color }}-600 dark:text-{{ $color }}-400 mt-1">
                        {{ number_format($stats['volume'], 2) }} TRY
                    </div>
                    <div class="text-xs text-{{ $color }}-600 dark:text-{{ $color }}-400">
                        {{ $data['total_transactions'] > 0 ? number_format(($stats['count'] / $data['total_transactions']) * 100, 1) : '0.0' }}%
                    </div>
                </div>
            @endforeach
        </div>
    </x-filament::section>

    <!-- Key Insights -->
    <x-filament::section>
        <x-slot name="heading">Key Insights</x-slot>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
                <h4 class="font-semibold text-gray-900 dark:text-gray-100">Performance Metrics</h4>
                
                @php
                    $completedTransactions = $data['by_status']['completed']['count'] ?? 0;
                    $completionRate = $data['total_transactions'] > 0 ? ($completedTransactions / $data['total_transactions']) * 100 : 0;
                    $avgCommissionRate = $data['total_volume'] > 0 ? ($data['total_commission'] / $data['total_volume']) * 100 : 0;
                @endphp
                
                <div class="space-y-3">
                    <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <span>Completion Rate</span>
                        <span class="font-semibold {{ $completionRate >= 95 ? 'text-green-600' : ($completionRate >= 85 ? 'text-yellow-600' : 'text-red-600') }}">
                            {{ number_format($completionRate, 1) }}%
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <span>Avg Commission Rate</span>
                        <span class="font-semibold text-green-600">
                            {{ number_format($avgCommissionRate, 2) }}%
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <span>Revenue per Transaction</span>
                        <span class="font-semibold text-blue-600">
                            {{ $data['total_transactions'] > 0 ? number_format($data['total_commission'] / $data['total_transactions'], 2) : '0.00' }} TRY
                        </span>
                    </div>
                </div>
            </div>
            
            <div class="space-y-4">
                <h4 class="font-semibold text-gray-900 dark:text-gray-100">Top Performing Categories</h4>
                
                @php
                    $sortedByCommission = collect($data['by_type'])->sortByDesc('commission')->take(3);
                @endphp
                
                <div class="space-y-3">
                    @foreach($sortedByCommission as $type => $stats)
                        <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <div>
                                <div class="font-medium capitalize">{{ str_replace('_', ' ', $type) }}</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">
                                    {{ number_format($stats['count']) }} transactions
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="font-semibold text-green-600">
                                    {{ number_format($stats['commission'], 2) }} TRY
                                </div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">
                                    {{ $data['total_commission'] > 0 ? number_format(($stats['commission'] / $data['total_commission']) * 100, 1) : '0.0' }}%
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </x-filament::section>
</div>
