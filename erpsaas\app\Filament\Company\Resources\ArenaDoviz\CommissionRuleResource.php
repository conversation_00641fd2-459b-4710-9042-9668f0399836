<?php

namespace App\Filament\Company\Resources\ArenaDoviz;

use App\Enums\ArenaDoviz\ExchangeType;
use App\Filament\Company\Resources\ArenaDoviz\CommissionRuleResource\Pages;
use App\Models\ArenaDoviz\CommissionRule;
use App\Models\Common\Client;
use App\Services\ArenaDoviz\LocationCurrencyService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class CommissionRuleResource extends Resource
{
    protected static ?string $model = CommissionRule::class;

    protected static ?string $navigationIcon = 'heroicon-o-calculator';

    protected static ?string $navigationLabel = 'Commission Rules';

    protected static ?string $modelLabel = 'Commission Rule';

    protected static ?string $pluralModelLabel = 'Commission Rules';

    protected static ?string $navigationGroup = 'Arena Doviz';

    protected static ?int $navigationSort = 5;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Rule Details')
                    ->schema([
                        Forms\Components\TextInput::make('rule_name')
                            ->label('Rule Name')
                            ->required()
                            ->maxLength(100),

                        Forms\Components\Select::make('client_id')
                            ->label('Specific Client (Optional)')
                            ->options(Client::pluck('name', 'id'))
                            ->searchable()
                            ->nullable()
                            ->helperText('Leave empty to apply to all clients'),

                        Forms\Components\Select::make('location_id')
                            ->label('Specific Location (Optional)')
                            ->relationship('location', 'name')
                            ->searchable()
                            ->nullable()
                            ->helperText('Leave empty to apply to all locations'),

                        Forms\Components\Select::make('exchange_type')
                            ->label('Exchange Type (Optional)')
                            ->options(ExchangeType::class)
                            ->nullable()
                            ->helperText('Leave empty to apply to all exchange types'),
                    ])->columns(2),

                Forms\Components\Section::make('Currency Filters')
                    ->schema([
                        Forms\Components\Select::make('from_currency_code')
                            ->label('From Currency (Optional)')
                            ->options(function () {
                                return app(LocationCurrencyService::class)
                                    ->getAvailableLocationCurrencies();
                            })
                            ->searchable()
                            ->nullable(),

                        Forms\Components\Select::make('to_currency_code')
                            ->label('To Currency (Optional)')
                            ->options(function () {
                                return app(LocationCurrencyService::class)
                                    ->getAvailableLocationCurrencies();
                            })
                            ->searchable()
                            ->nullable(),
                    ])->columns(2),

                Forms\Components\Section::make('Commission Configuration')
                    ->schema([
                        Forms\Components\Select::make('commission_type')
                            ->label('Commission Type')
                            ->options(CommissionRule::COMMISSION_TYPES)
                            ->required()
                            ->live(),

                        Forms\Components\TextInput::make('commission_rate')
                            ->label('Commission Rate (%)')
                            ->numeric()
                            ->step(0.01)
                            ->visible(fn (Forms\Get $get) => in_array($get('commission_type'), ['percentage', 'hybrid'])),

                        Forms\Components\TextInput::make('fixed_amount')
                            ->label('Fixed Amount')
                            ->numeric()
                            ->step(0.01)
                            ->visible(fn (Forms\Get $get) => in_array($get('commission_type'), ['fixed', 'hybrid'])),

                        Forms\Components\Select::make('calculation_method')
                            ->label('Calculation Method')
                            ->options(CommissionRule::CALCULATION_METHODS)
                            ->required()
                            ->default('post_conversion'),

                        Forms\Components\Select::make('commission_currency')
                            ->label('Commission Currency')
                            ->options(function () {
                                return app(LocationCurrencyService::class)
                                    ->getAvailableLocationCurrencies();
                            })
                            ->searchable()
                            ->nullable()
                            ->helperText('Leave empty to use destination currency'),
                    ])->columns(2),

                Forms\Components\Section::make('Commission Limits')
                    ->schema([
                        Forms\Components\TextInput::make('min_commission')
                            ->label('Minimum Commission')
                            ->numeric()
                            ->step(0.01)
                            ->nullable(),

                        Forms\Components\TextInput::make('max_commission')
                            ->label('Maximum Commission')
                            ->numeric()
                            ->step(0.01)
                            ->nullable(),
                    ])->columns(2),

                Forms\Components\Section::make('Tiered Rules')
                    ->schema([
                        Forms\Components\Repeater::make('tier_rules')
                            ->label('Tier Configuration')
                            ->schema([
                                Forms\Components\TextInput::make('min_amount')
                                    ->label('Minimum Amount')
                                    ->numeric()
                                    ->required(),

                                Forms\Components\TextInput::make('max_amount')
                                    ->label('Maximum Amount')
                                    ->numeric()
                                    ->required(),

                                Forms\Components\TextInput::make('rate')
                                    ->label('Rate (%)')
                                    ->numeric()
                                    ->step(0.01)
                                    ->required(),
                            ])
                            ->columns(3)
                            ->addActionLabel('Add Tier')
                            ->collapsible(),
                    ])
                    ->visible(fn (Forms\Get $get) => $get('commission_type') === 'tiered'),

                Forms\Components\Section::make('Rule Validity')
                    ->schema([
                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true),

                        Forms\Components\TextInput::make('priority')
                            ->label('Priority')
                            ->numeric()
                            ->default(1)
                            ->helperText('Higher numbers have higher priority'),

                        Forms\Components\DatePicker::make('valid_from')
                            ->label('Valid From')
                            ->nullable(),

                        Forms\Components\DatePicker::make('valid_until')
                            ->label('Valid Until')
                            ->nullable(),
                    ])->columns(2),

                Forms\Components\Section::make('Notes')
                    ->schema([
                        Forms\Components\Textarea::make('notes')
                            ->label('Notes')
                            ->rows(3)
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('rule_name')
                    ->label('Rule Name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('client.name')
                    ->label('Client')
                    ->searchable()
                    ->placeholder('All Clients'),

                Tables\Columns\TextColumn::make('exchange_type')
                    ->label('Exchange Type')
                    ->badge()
                    ->placeholder('All Types'),

                Tables\Columns\TextColumn::make('commission_type')
                    ->label('Type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'percentage' => 'success',
                        'fixed' => 'info',
                        'tiered' => 'warning',
                        'hybrid' => 'primary',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('formatted_rate')
                    ->label('Rate/Amount'),

                Tables\Columns\TextColumn::make('calculation_method')
                    ->label('Calculation')
                    ->badge()
                    ->color('gray'),

                Tables\Columns\TextColumn::make('priority')
                    ->label('Priority')
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean(),

                Tables\Columns\TextColumn::make('valid_from')
                    ->label('Valid From')
                    ->date()
                    ->placeholder('Always'),

                Tables\Columns\TextColumn::make('valid_until')
                    ->label('Valid Until')
                    ->date()
                    ->placeholder('Always'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('commission_type')
                    ->options(CommissionRule::COMMISSION_TYPES),

                Tables\Filters\SelectFilter::make('exchange_type')
                    ->options(ExchangeType::class),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active'),

                Tables\Filters\Filter::make('valid_now')
                    ->query(fn ($query) => $query->valid())
                    ->label('Currently Valid'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('priority', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCommissionRules::route('/'),
            'create' => Pages\CreateCommissionRule::route('/create'),
            'view' => Pages\ViewCommissionRule::route('/{record}'),
            'edit' => Pages\EditCommissionRule::route('/{record}/edit'),
        ];
    }
}
