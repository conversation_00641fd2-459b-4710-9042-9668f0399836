<?php

namespace Tests\Feature\ArenaDoviz;

use App\Models\ArenaDoviz\Location;
use App\Models\Common\Company;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class LocationManagementTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Company $company;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->company = Company::factory()->create();
        $this->user->companies()->attach($this->company);
        
        $this->actingAs($this->user);
    }

    public function test_can_view_locations_index_page()
    {
        $response = $this->get('/company/' . $this->company->id . '/arena-doviz/locations');
        
        $response->assertStatus(200);
        $response->assertSee('Locations');
    }

    public function test_can_create_new_location()
    {
        $locationData = [
            'name' => 'Test Location',
            'code' => 'TST',
            'city' => 'Test City',
            'country' => 'Test Country',
            'address' => '123 Test Street',
            'timezone' => 'UTC',
            'phone' => '+1234567890',
            'email' => '<EMAIL>',
            'manager_name' => 'Test Manager',
            'is_active' => true,
            'services' => ['currency_exchange', 'money_transfer'],
            'max_transaction_amount' => 50000.00,
        ];

        Livewire::test(\App\Filament\Company\Resources\ArenaDoviz\LocationResource\Pages\CreateLocation::class)
            ->fillForm($locationData)
            ->call('create')
            ->assertHasNoFormErrors();

        $this->assertDatabaseHas('locations', [
            'name' => 'Test Location',
            'code' => 'TST',
            'city' => 'Test City',
            'country' => 'Test Country',
            'company_id' => $this->company->id,
        ]);
    }

    public function test_location_code_must_be_unique()
    {
        Location::factory()->create([
            'code' => 'IST',
            'company_id' => $this->company->id,
        ]);

        $locationData = [
            'name' => 'Another Location',
            'code' => 'IST', // Duplicate code
            'city' => 'Test City',
            'country' => 'Test Country',
            'timezone' => 'UTC',
            'is_active' => true,
        ];

        Livewire::test(\App\Filament\Company\Resources\ArenaDoviz\LocationResource\Pages\CreateLocation::class)
            ->fillForm($locationData)
            ->call('create')
            ->assertHasFormErrors(['code']);
    }

    public function test_can_edit_existing_location()
    {
        $location = Location::factory()->create([
            'company_id' => $this->company->id,
            'name' => 'Original Name',
            'code' => 'ORG',
        ]);

        $updatedData = [
            'name' => 'Updated Name',
            'code' => 'UPD',
            'city' => 'Updated City',
            'country' => 'Updated Country',
            'timezone' => 'Europe/Istanbul',
            'is_active' => false,
        ];

        Livewire::test(\App\Filament\Company\Resources\ArenaDoviz\LocationResource\Pages\EditLocation::class, [
            'record' => $location->getRouteKey(),
        ])
            ->fillForm($updatedData)
            ->call('save')
            ->assertHasNoFormErrors();

        $location->refresh();
        $this->assertEquals('Updated Name', $location->name);
        $this->assertEquals('UPD', $location->code);
        $this->assertEquals('Updated City', $location->city);
        $this->assertFalse($location->is_active);
    }

    public function test_can_delete_location()
    {
        $location = Location::factory()->create([
            'company_id' => $this->company->id,
        ]);

        Livewire::test(\App\Filament\Company\Resources\ArenaDoviz\LocationResource\Pages\EditLocation::class, [
            'record' => $location->getRouteKey(),
        ])
            ->callAction('delete');

        $this->assertSoftDeleted('locations', [
            'id' => $location->id,
        ]);
    }

    public function test_can_view_location_details()
    {
        $location = Location::factory()->create([
            'company_id' => $this->company->id,
            'name' => 'Test Location',
            'code' => 'TST',
            'city' => 'Test City',
            'country' => 'Test Country',
            'manager_name' => 'Test Manager',
        ]);

        Livewire::test(\App\Filament\Company\Resources\ArenaDoviz\LocationResource\Pages\ViewLocation::class, [
            'record' => $location->getRouteKey(),
        ])
            ->assertSee('Test Location')
            ->assertSee('TST')
            ->assertSee('Test City')
            ->assertSee('Test Manager');
    }

    public function test_location_services_are_stored_as_array()
    {
        $location = Location::factory()->create([
            'company_id' => $this->company->id,
            'services' => ['currency_exchange', 'money_transfer', 'delivery'],
        ]);

        $this->assertIsArray($location->services);
        $this->assertContains('currency_exchange', $location->services);
        $this->assertContains('money_transfer', $location->services);
        $this->assertContains('delivery', $location->services);
    }

    public function test_location_working_hours_are_stored_as_array()
    {
        $workingHours = [
            'monday' => ['open' => '09:00', 'close' => '18:00'],
            'tuesday' => ['open' => '09:00', 'close' => '18:00'],
            'wednesday' => ['open' => '09:00', 'close' => '18:00'],
            'thursday' => ['open' => '09:00', 'close' => '18:00'],
            'friday' => ['open' => '09:00', 'close' => '18:00'],
            'saturday' => ['closed' => true],
            'sunday' => ['closed' => true],
        ];

        $location = Location::factory()->create([
            'company_id' => $this->company->id,
            'working_hours' => $workingHours,
        ]);

        $this->assertIsArray($location->working_hours);
        $this->assertEquals('09:00', $location->working_hours['monday']['open']);
        $this->assertEquals('18:00', $location->working_hours['monday']['close']);
        $this->assertTrue($location->working_hours['saturday']['closed']);
    }

    public function test_can_filter_locations_by_country()
    {
        Location::factory()->create([
            'company_id' => $this->company->id,
            'name' => 'Turkey Location',
            'country' => 'Turkey',
        ]);

        Location::factory()->create([
            'company_id' => $this->company->id,
            'name' => 'Iran Location',
            'country' => 'Iran',
        ]);

        Livewire::test(\App\Filament\Company\Resources\ArenaDoviz\LocationResource\Pages\ListLocations::class)
            ->filterTable('country', 'Turkey')
            ->assertCanSeeTableRecords([
                Location::where('country', 'Turkey')->first(),
            ])
            ->assertCanNotSeeTableRecords([
                Location::where('country', 'Iran')->first(),
            ]);
    }

    public function test_can_filter_locations_by_active_status()
    {
        $activeLocation = Location::factory()->create([
            'company_id' => $this->company->id,
            'name' => 'Active Location',
            'is_active' => true,
        ]);

        $inactiveLocation = Location::factory()->create([
            'company_id' => $this->company->id,
            'name' => 'Inactive Location',
            'is_active' => false,
        ]);

        Livewire::test(\App\Filament\Company\Resources\ArenaDoviz\LocationResource\Pages\ListLocations::class)
            ->filterTable('is_active', true)
            ->assertCanSeeTableRecords([$activeLocation])
            ->assertCanNotSeeTableRecords([$inactiveLocation]);
    }

    public function test_can_search_locations_by_name()
    {
        $location1 = Location::factory()->create([
            'company_id' => $this->company->id,
            'name' => 'Istanbul Office',
        ]);

        $location2 = Location::factory()->create([
            'company_id' => $this->company->id,
            'name' => 'Tehran Branch',
        ]);

        Livewire::test(\App\Filament\Company\Resources\ArenaDoviz\LocationResource\Pages\ListLocations::class)
            ->searchTable('Istanbul')
            ->assertCanSeeTableRecords([$location1])
            ->assertCanNotSeeTableRecords([$location2]);
    }

    public function test_location_max_transaction_amount_is_formatted_correctly()
    {
        $location = Location::factory()->create([
            'company_id' => $this->company->id,
            'max_transaction_amount' => 100000.50,
        ]);

        $this->assertEquals(100000.50, $location->max_transaction_amount);
        $this->assertIsFloat($location->max_transaction_amount);
    }
}
