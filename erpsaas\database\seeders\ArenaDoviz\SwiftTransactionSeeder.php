<?php

namespace Database\Seeders\ArenaDoviz;

use App\Enums\ArenaDoviz\SwiftStatus;
use App\Models\ArenaDoviz\CurrencyExchange;
use App\Models\ArenaDoviz\SwiftTransaction;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class SwiftTransactionSeeder extends Seeder
{
    /**
     * Seed SWIFT transactions for Arena Doviz international transfers.
     * 
     * Creates realistic SWIFT transaction data including:
     * - Incoming and outgoing international transfers
     * - Various bank details and routing information
     * - Different transaction statuses and processing times
     * - Realistic fees and exchange rates
     */
    public function run(): void
    {
        $this->command->info('🏦 Seeding Arena Doviz SWIFT transactions...');

        // Set a temporary authenticated user for the CompanyOwned trait
        $adminUser = \App\Models\User::where('email', '<EMAIL>')->first();
        if ($adminUser) {
            Auth::login($adminUser);
        }

        // Temporarily disable event dispatcher to prevent WhatsApp notifications
        CurrencyExchange::unsetEventDispatcher();
        SwiftTransaction::unsetEventDispatcher();

        // Get currency exchanges that could have SWIFT transactions
        $exchanges = CurrencyExchange::where('company_id', 1)
            ->whereIn('exchange_type', ['buy', 'sell'])
            ->whereIn('from_currency_code', ['USD', 'EUR', 'GBP', 'AED'])
            ->orWhereIn('to_currency_code', ['USD', 'EUR', 'GBP', 'AED'])
            ->limit(20)
            ->get();
        
        if ($exchanges->isEmpty()) {
            $this->command->warn('No suitable currency exchanges found. Please run CurrencyExchangeSeeder first.');
            return;
        }
        
        $swiftCount = 0;
        
        foreach ($exchanges as $exchange) {
            // Not all exchanges have SWIFT transactions (about 30% do)
            if (rand(1, 100) > 30) {
                continue;
            }
            
            $this->createSwiftTransaction($exchange);
            $swiftCount++;
        }
        
        // Create some additional standalone SWIFT transactions
        $this->createStandaloneSwiftTransactions();
        $swiftCount += 5;

        // Re-enable event dispatchers
        CurrencyExchange::setEventDispatcher(app('events'));
        SwiftTransaction::setEventDispatcher(app('events'));

        // Logout the temporary user
        Auth::logout();

        $this->command->info("✅ Created {$swiftCount} SWIFT transactions");
    }
    
    private function createSwiftTransaction(CurrencyExchange $exchange): void
    {
        $isIncoming = rand(1, 2) === 1; // 50% incoming, 50% outgoing
        $currency = $isIncoming ? $exchange->to_currency_code : $exchange->from_currency_code;
        $amount = $isIncoming ? $exchange->to_amount : $exchange->from_amount;
        
        $bankData = $this->getBankData($currency, $isIncoming);
        $status = $this->getSwiftStatus($exchange->created_at);
        
        SwiftTransaction::create([
            'company_id' => 1,
            'currency_exchange_id' => $exchange->id,
            'swift_reference' => $this->generateSwiftReference($exchange->created_at),
            'status' => $status,
            'sender_bank_name' => $bankData['sender_bank_name'],
            'sender_bank_swift' => $bankData['sender_bank_swift'],
            'sender_account_number' => $bankData['sender_account_number'],
            'sender_name' => $bankData['sender_name'],
            'sender_address' => $bankData['sender_address'],
            'receiver_bank_name' => $bankData['receiver_bank_name'],
            'receiver_bank_swift' => $bankData['receiver_bank_swift'],
            'receiver_account_number' => $bankData['receiver_account_number'],
            'receiver_name' => $bankData['receiver_name'],
            'receiver_address' => $bankData['receiver_address'],
            'amount' => $amount,
            'currency_code' => $currency,
            'purpose_of_payment' => $this->getPurposeOfPayment(),
            'charges_bearer' => $this->getChargesBearer(),
            'intermediary_bank_swift' => $bankData['intermediary_bank_swift'],
            'intermediary_bank_name' => $bankData['intermediary_bank_name'],
            'special_instructions' => $this->getSpecialInstructions(),
            'expected_date' => $exchange->created_at->addDays(rand(1, 3)),
            'completed_date' => $status === SwiftStatus::COMPLETED ?
                $exchange->created_at->addDays(rand(1, 5)) : null,
            'fees_amount' => $this->calculateFees($amount, $currency),
            'exchange_rate_applied' => $exchange->exchange_rate,
            'created_by' => $exchange->created_by,
            'created_at' => $exchange->created_at,
            'updated_at' => $exchange->updated_at,
        ]);
    }
    
    private function getBankData(string $currency, bool $isIncoming): array
    {
        $banks = $this->getBanksByCountry($currency);
        $senderBank = $banks[array_rand($banks)];
        $receiverBank = $this->getArenaDovizBank();
        
        if ($isIncoming) {
            // Incoming: Foreign bank → Arena Doviz
            return [
                'sender_bank_name' => $senderBank['name'],
                'sender_bank_swift' => $senderBank['swift'],
                'sender_account_number' => $this->generateAccountNumber(),
                'sender_name' => $this->generatePersonName(),
                'sender_address' => $senderBank['address'],
                'receiver_bank_name' => $receiverBank['name'],
                'receiver_bank_swift' => $receiverBank['swift'],
                'receiver_account_number' => $receiverBank['account'],
                'receiver_name' => 'Arena Doviz Ltd.',
                'receiver_address' => $receiverBank['address'],
                'intermediary_bank_swift' => $senderBank['intermediary_swift'] ?? null,
                'intermediary_bank_name' => $senderBank['intermediary_name'] ?? null,
            ];
        } else {
            // Outgoing: Arena Doviz → Foreign bank
            return [
                'sender_bank_name' => $receiverBank['name'],
                'sender_bank_swift' => $receiverBank['swift'],
                'sender_account_number' => $receiverBank['account'],
                'sender_name' => 'Arena Doviz Ltd.',
                'sender_address' => $receiverBank['address'],
                'receiver_bank_name' => $senderBank['name'],
                'receiver_bank_swift' => $senderBank['swift'],
                'receiver_account_number' => $this->generateAccountNumber(),
                'receiver_name' => $this->generatePersonName(),
                'receiver_address' => $senderBank['address'],
                'intermediary_bank_swift' => $senderBank['intermediary_swift'] ?? null,
                'intermediary_bank_name' => $senderBank['intermediary_name'] ?? null,
            ];
        }
    }
    
    private function getBanksByCountry(string $currency): array
    {
        return match ($currency) {
            'USD' => [
                [
                    'name' => 'JPMorgan Chase Bank N.A.',
                    'swift' => 'CHASUS33',
                    'address' => '270 Park Avenue, New York, NY 10017, USA',
                    'intermediary_swift' => 'CHASUS33XXX',
                    'intermediary_name' => 'JPMorgan Chase Bank N.A.',
                ],
                [
                    'name' => 'Bank of America N.A.',
                    'swift' => 'BOFAUS3N',
                    'address' => '100 North Tryon Street, Charlotte, NC 28255, USA',
                ],
                [
                    'name' => 'Wells Fargo Bank N.A.',
                    'swift' => 'WFBIUS6S',
                    'address' => '420 Montgomery Street, San Francisco, CA 94104, USA',
                ],
            ],
            'EUR' => [
                [
                    'name' => 'Deutsche Bank AG',
                    'swift' => 'DEUTDEFF',
                    'address' => 'Taunusanlage 12, 60325 Frankfurt am Main, Germany',
                ],
                [
                    'name' => 'BNP Paribas',
                    'swift' => 'BNPAFRPP',
                    'address' => '16 Boulevard des Italiens, 75009 Paris, France',
                ],
                [
                    'name' => 'UniCredit Bank AG',
                    'swift' => 'HYVEDEMMXXX',
                    'address' => 'Arabellastraße 12, 81925 Munich, Germany',
                ],
            ],
            'GBP' => [
                [
                    'name' => 'Barclays Bank PLC',
                    'swift' => 'BARCGB22',
                    'address' => '1 Churchill Place, London E14 5HP, United Kingdom',
                ],
                [
                    'name' => 'HSBC Bank PLC',
                    'swift' => 'HBUKGB4B',
                    'address' => '8 Canada Square, London E14 5HQ, United Kingdom',
                ],
            ],
            'AED' => [
                [
                    'name' => 'Emirates NBD Bank PJSC',
                    'swift' => 'EBILAEAD',
                    'address' => 'Baniyas Road, Deira, Dubai, UAE',
                ],
                [
                    'name' => 'First Abu Dhabi Bank PJSC',
                    'swift' => 'NBADAEAA',
                    'address' => 'Khalifa Business Park, Al Qurm District, Abu Dhabi, UAE',
                ],
            ],
            default => [
                [
                    'name' => 'Standard Chartered Bank',
                    'swift' => 'SCBLUS33',
                    'address' => '1 Basinghall Avenue, London EC2V 5DD, United Kingdom',
                ],
            ],
        };
    }
    
    private function getArenaDovizBank(): array
    {
        return [
            'name' => 'Türkiye İş Bankası A.Ş.',
            'swift' => 'ISBKTRIS',
            'account' => '**************************',
            'address' => 'İş Kuleleri Kule 1, 34330 Levent/İstanbul, Turkey',
        ];
    }
    
    private function generateAccountNumber(): string
    {
        return 'ACC' . rand(*********, *********);
    }
    
    private function generatePersonName(): string
    {
        $firstNames = ['John', 'Sarah', 'Michael', 'Emma', 'David', 'Lisa', 'Robert', 'Maria'];
        $lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis'];
        
        return $firstNames[array_rand($firstNames)] . ' ' . $lastNames[array_rand($lastNames)];
    }
    
    private function getSwiftStatus(Carbon $createdAt): SwiftStatus
    {
        $daysAgo = $createdAt->diffInDays(Carbon::now());
        
        if ($daysAgo > 7) {
            // Old transactions: mostly completed
            return rand(1, 100) <= 90 ? SwiftStatus::COMPLETED : SwiftStatus::FAILED;
        } elseif ($daysAgo > 2) {
            // Recent transactions
            $statuses = [SwiftStatus::COMPLETED, SwiftStatus::PROCESSING, SwiftStatus::PENDING];
            return $statuses[array_rand($statuses)];
        } else {
            // Very recent: mostly pending or processing
            return rand(1, 2) === 1 ? SwiftStatus::PENDING : SwiftStatus::PROCESSING;
        }
    }
    
    private function getPurposeOfPayment(): string
    {
        $purposes = [
            'Currency exchange transaction',
            'International trade payment',
            'Personal remittance',
            'Business payment',
            'Investment transfer',
            'Import/export settlement',
        ];
        
        return $purposes[array_rand($purposes)];
    }
    
    private function getChargesBearer(): string
    {
        $bearers = ['OUR', 'BEN', 'SHA'];
        return $bearers[array_rand($bearers)];
    }
    
    private function getSpecialInstructions(): ?string
    {
        if (rand(1, 100) > 40) {
            return null; // 60% have no special instructions
        }
        
        $instructions = [
            'Urgent transfer - same day processing required',
            'Please confirm receipt via email',
            'Regular monthly transfer',
            'Trade finance related payment',
            'Contact beneficiary before crediting',
        ];
        
        return $instructions[array_rand($instructions)];
    }
    
    private function calculateFees(float $amount, string $currency): float
    {
        // Base fee + percentage
        $baseFee = match ($currency) {
            'USD' => 25.00,
            'EUR' => 22.00,
            'GBP' => 20.00,
            'AED' => 90.00,
            default => 25.00,
        };
        
        $percentageFee = $amount * 0.001; // 0.1%
        return min($baseFee + $percentageFee, $amount * 0.005); // Max 0.5% of amount
    }
    
    private function createStandaloneSwiftTransactions(): void
    {
        // Create some SWIFT transactions without associated currency exchanges
        // These represent direct international transfers
        
        for ($i = 0; $i < 5; $i++) {
            $currency = ['USD', 'EUR', 'GBP'][array_rand(['USD', 'EUR', 'GBP'])];
            $amount = rand(1000, 50000);
            $createdAt = Carbon::now()->subDays(rand(1, 30));
            
            // Create a dummy currency exchange first
            $exchange = CurrencyExchange::create([
                'company_id' => 1,
                'client_id' => 1, // Use first client
                'exchange_number' => $this->generateExchangeNumber($createdAt),
                'exchange_type' => 'transfer',
                'status' => 'completed',
                'from_currency_code' => $currency,
                'to_currency_code' => 'TRY',
                'from_amount' => $amount,
                'to_amount' => $amount * 32.5,
                'exchange_rate' => 32.5,
                'commission_rate' => 0.002,
                'commission_amount' => $amount * 32.5 * 0.002,
                'net_amount' => $amount * 32.5 * 0.998,
                'notes' => 'SWIFT transfer transaction',
                'exchange_date' => $createdAt,
                'settlement_date' => $createdAt->copy()->addDays(2),
                'created_by' => 1,
                'created_at' => $createdAt,
                'updated_at' => $createdAt,
            ]);
            
            $this->createSwiftTransaction($exchange);
        }
    }

    private function generateSwiftReference(Carbon $date): string
    {
        $prefix = 'SW';
        $year = $date->format('Y');
        $month = $date->format('m');

        // Use a more robust counter for seeding
        static $counters = [];
        $key = $year . $month;

        if (!isset($counters[$key])) {
            $counters[$key] = 1;
        } else {
            $counters[$key]++;
        }

        return sprintf('%s%s%s%06d', $prefix, $year, $month, $counters[$key]);
    }

    private function generateExchangeNumber(Carbon $date): string
    {
        $prefix = 'EX';
        $year = $date->format('Y');
        $month = $date->format('m');

        // Use a more robust counter for seeding
        static $counters = [];
        $key = $year . $month;

        if (!isset($counters[$key])) {
            $counters[$key] = 10000; // Start from a high number to avoid conflicts
        } else {
            $counters[$key]++;
        }

        return sprintf('%s%s%s%04d', $prefix, $year, $month, $counters[$key]);
    }
}
