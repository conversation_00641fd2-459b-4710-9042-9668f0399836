<?php

namespace App\Filament\Company\Resources\ArenaDoviz\DeliveryResource\Pages;

use App\Filament\Company\Resources\ArenaDoviz\DeliveryResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewDelivery extends ViewRecord
{
    protected static string $resource = DeliveryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
