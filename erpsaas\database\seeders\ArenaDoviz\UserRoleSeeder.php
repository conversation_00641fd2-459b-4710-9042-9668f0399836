<?php

namespace Database\Seeders\ArenaDoviz;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserRoleSeeder extends Seeder
{
    /**
     * Seed users with Arena Doviz specific roles and permissions.
     * 
     * Based on documentation requirements:
     * - Admin: Full system access, user management, system configuration
     * - Exchange Manager: Transaction processing, customer management, approvals
     * - Exchange Operator: Transaction processing, customer management
     * - Cashier: Cash transactions, basic customer operations
     * - Viewer: Read-only access to reports and data
     * - Courier: Delivery management and receipt handling
     */
    public function run(): void
    {
        $this->command->info('👥 Seeding Arena Doviz users and roles...');
        
        $users = [
            // Admin User (already exists from UserCompanySeeder, we'll update it)
            [
                'name' => 'Arena Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'permissions' => [
                    'manage_users', 'manage_system', 'manage_companies', 'view_all_reports',
                    'approve_transactions', 'manage_currencies', 'manage_locations',
                    'manage_clients', 'process_transactions', 'manage_deliveries'
                ],
            ],
            
            // Exchange Manager - Istanbul
            [
                'name' => 'Mehmet Özkan',
                'email' => '<EMAIL>',
                'password' => Hash::make('manager123'),
                'permissions' => [
                    'approve_transactions', 'manage_clients', 'process_transactions',
                    'view_reports', 'manage_rates', 'manage_deliveries', 'manage_couriers'
                ],
            ],

            // Exchange Manager - Tabriz
            [
                'name' => 'Ali Rezaei',
                'email' => '<EMAIL>',
                'password' => Hash::make('manager123'),
                'permissions' => [
                    'approve_transactions', 'manage_clients', 'process_transactions',
                    'view_reports', 'manage_rates', 'manage_deliveries', 'manage_couriers'
                ],
            ],
            
            // Exchange Operators
            [
                'name' => 'Ayşe Demir',
                'email' => '<EMAIL>',
                'password' => Hash::make('operator123'),
                'permissions' => [
                    'process_transactions', 'manage_clients', 'view_reports',
                    'create_deliveries', 'update_client_profiles'
                ],
            ],

            [
                'name' => 'Hassan Ahmadi',
                'email' => '<EMAIL>',
                'password' => Hash::make('operator123'),
                'permissions' => [
                    'process_transactions', 'manage_clients', 'view_reports',
                    'create_deliveries', 'update_client_profiles'
                ],
            ],

            [
                'name' => 'Ahmed Al-Mansouri',
                'email' => '<EMAIL>',
                'password' => Hash::make('operator123'),
                'permissions' => [
                    'process_transactions', 'manage_clients', 'view_reports',
                    'create_deliveries', 'update_client_profiles'
                ],
            ],
            
            // Cashiers
            [
                'name' => 'Fatma Yılmaz',
                'email' => '<EMAIL>',
                'password' => Hash::make('cashier123'),
                'permissions' => [
                    'process_cash_transactions', 'view_client_balances',
                    'create_cash_deposits', 'verify_cash_deposits'
                ],
            ],

            [
                'name' => 'Reza Hosseini',
                'email' => '<EMAIL>',
                'password' => Hash::make('cashier123'),
                'permissions' => [
                    'process_cash_transactions', 'view_client_balances',
                    'create_cash_deposits', 'verify_cash_deposits'
                ],
            ],
            
            // Viewers (Accountants/Auditors)
            [
                'name' => 'Elif Kaya',
                'email' => '<EMAIL>',
                'password' => Hash::make('viewer123'),
                'permissions' => [
                    'view_reports', 'view_transactions', 'view_client_balances',
                    'export_reports', 'view_audit_logs'
                ],
            ],

            // Couriers
            [
                'name' => 'Murat Şahin',
                'email' => '<EMAIL>',
                'password' => Hash::make('courier123'),
                'permissions' => [
                    'view_deliveries', 'update_delivery_status', 'upload_delivery_proof',
                    'view_delivery_routes', 'update_courier_status'
                ],
            ],

            [
                'name' => 'Kemal Özdemir',
                'email' => '<EMAIL>',
                'password' => Hash::make('courier123'),
                'permissions' => [
                    'view_deliveries', 'update_delivery_status', 'upload_delivery_proof',
                    'view_delivery_routes', 'update_courier_status'
                ],
            ],
        ];
        
        foreach ($users as $userData) {
            $permissions = $userData['permissions'];
            unset($userData['permissions']);
            
            $user = User::updateOrCreate(
                ['email' => $userData['email']],
                $userData
            );
            
            // Note: In a real implementation, you would assign permissions using Spatie Permission
            // For now, we'll store them as a note in the user record
            $user->update([
                'current_company_id' => 1, // Arena Doviz company
            ]);
        }
        
        $this->command->info('✅ Created ' . count($users) . ' users with Arena Doviz roles and permissions');
    }
}
