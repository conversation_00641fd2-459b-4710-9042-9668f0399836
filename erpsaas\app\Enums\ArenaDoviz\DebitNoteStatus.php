<?php

namespace App\Enums\ArenaDoviz;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum DebitNoteStatus: string implements HasColor, HasIcon, HasLabel
{
    case PENDING = 'pending';
    case SCHEDULED = 'scheduled';
    case IN_PROGRESS = 'in_progress';
    case COMPLETED = 'completed';
    case CANCELLED = 'cancelled';

    public function getLabel(): string
    {
        return match ($this) {
            self::PENDING => 'Pending',
            self::SCHEDULED => 'Scheduled',
            self::IN_PROGRESS => 'In Progress',
            self::COMPLETED => 'Completed',
            self::CANCELLED => 'Cancelled',
        };
    }

    public function getColor(): string
    {
        return match ($this) {
            self::PENDING => 'warning',
            self::SCHEDULED => 'info',
            self::IN_PROGRESS => 'primary',
            self::COMPLETED => 'success',
            self::CANCELLED => 'danger',
        };
    }

    public function getIcon(): string
    {
        return match ($this) {
            self::PENDING => 'heroicon-o-clock',
            self::SCHEDULED => 'heroicon-o-calendar',
            self::IN_PROGRESS => 'heroicon-o-truck',
            self::COMPLETED => 'heroicon-o-check-circle',
            self::CANCELLED => 'heroicon-o-x-circle',
        };
    }

    public function getDescription(): string
    {
        return match ($this) {
            self::PENDING => 'Debit note is pending assignment',
            self::SCHEDULED => 'Debit note is scheduled for delivery',
            self::IN_PROGRESS => 'Debit note delivery is in progress',
            self::COMPLETED => 'Debit note has been completed with signature and proof',
            self::CANCELLED => 'Debit note has been cancelled',
        };
    }

    public function canTransitionTo(self $status): bool
    {
        return match ($this) {
            self::PENDING => in_array($status, [self::SCHEDULED, self::CANCELLED]),
            self::SCHEDULED => in_array($status, [self::IN_PROGRESS, self::CANCELLED]),
            self::IN_PROGRESS => in_array($status, [self::COMPLETED, self::CANCELLED]),
            self::COMPLETED => false, // Completed notes cannot be changed
            self::CANCELLED => false, // Cancelled notes cannot be changed
        };
    }

    public function isActive(): bool
    {
        return !in_array($this, [self::COMPLETED, self::CANCELLED]);
    }

    public function isComplete(): bool
    {
        return $this === self::COMPLETED;
    }

    public function requiresAction(): bool
    {
        return in_array($this, [self::PENDING, self::SCHEDULED, self::IN_PROGRESS]);
    }
}
