<?php

namespace Database\Seeders\ArenaDoviz;

use App\Models\Common\Client;
use App\Models\ArenaDoviz\ClientBalance;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class ClientBalanceSeeder extends Seeder
{
    /**
     * Seed client balances for Arena Doviz operations.
     * 
     * Creates realistic balance data for all clients across multiple currencies:
     * - VIP clients with higher balances
     * - Corporate clients with business-level balances
     * - Individual clients with personal-level balances
     * - Multi-currency balance tracking
     */
    public function run(): void
    {
        $this->command->info('💰 Seeding Arena Doviz client balances...');

        // Set a temporary authenticated user for the CompanyOwned trait
        $adminUser = \App\Models\User::where('email', '<EMAIL>')->first();
        if ($adminUser) {
            Auth::login($adminUser);
        }

        $clients = Client::where('company_id', 1)->get();
        $currencies = ['TRY', 'USD', 'EUR', 'AED', 'IRR', 'GBP'];
        
        $balanceData = [
            // VIP Client Balances (Higher amounts)
            'VIP001' => [
                'USD' => ['balance' => 25000.00, 'reserved' => 2000.00],
                'EUR' => ['balance' => 18000.00, 'reserved' => 1500.00],
                'TRY' => ['balance' => 150000.00, 'reserved' => 10000.00],
                'AED' => ['balance' => 45000.00, 'reserved' => 3000.00],
            ],
            'VIP002' => [
                'EUR' => ['balance' => 35000.00, 'reserved' => 5000.00],
                'USD' => ['balance' => 22000.00, 'reserved' => 1800.00],
                'GBP' => ['balance' => 15000.00, 'reserved' => 1200.00],
                'TRY' => ['balance' => 200000.00, 'reserved' => 15000.00],
            ],
            
            // Corporate Client Balances (Business-level amounts)
            'CORP001' => [
                'USD' => ['balance' => 12000.00, 'reserved' => 1000.00],
                'EUR' => ['balance' => 8000.00, 'reserved' => 800.00],
                'TRY' => ['balance' => 75000.00, 'reserved' => 5000.00],
            ],
            'CORP002' => [
                'EUR' => ['balance' => 15000.00, 'reserved' => 1200.00],
                'USD' => ['balance' => 8500.00, 'reserved' => 700.00],
                'TRY' => ['balance' => 95000.00, 'reserved' => 7000.00],
            ],
            
            // Individual Client Balances (Personal-level amounts)
            'IND001' => [
                'USD' => ['balance' => 2500.00, 'reserved' => 200.00],
                'TRY' => ['balance' => 25000.00, 'reserved' => 2000.00],
            ],
        ];
        
        foreach ($clients as $client) {
            $accountNumber = $client->account_number;
            
            if (isset($balanceData[$accountNumber])) {
                // Use predefined balance data
                foreach ($balanceData[$accountNumber] as $currencyCode => $amounts) {
                    $this->createClientBalance($client, $currencyCode, $amounts);
                }
            } else {
                // Generate random balances for other clients
                $this->generateRandomBalances($client);
            }
        }
        
        // Create some clients with zero or negative balances for testing
        $this->createSpecialBalanceScenarios();

        // Logout the temporary user
        Auth::logout();

        $this->command->info('✅ Created comprehensive client balance data');
    }
    
    private function createClientBalance(Client $client, string $currencyCode, array $amounts): void
    {
        $balance = $amounts['balance'];
        $reserved = $amounts['reserved'] ?? 0;
        $available = $balance - $reserved;

        ClientBalance::updateOrCreate(
            [
                'company_id' => $client->company_id,
                'client_id' => $client->id,
                'currency_code' => $currencyCode,
            ],
            [
                'balance' => $balance,
                'reserved_balance' => $reserved,
                'available_balance' => $available,
                'last_transaction_date' => Carbon::now()->subDays(rand(1, 30)),
                'created_by' => 1, // Admin user
            ]
        );
    }
    
    private function generateRandomBalances(Client $client): void
    {
        $currencies = ['TRY', 'USD', 'EUR'];
        $numCurrencies = rand(1, 3);
        $selectedCurrencies = array_slice($currencies, 0, $numCurrencies);
        
        foreach ($selectedCurrencies as $currencyCode) {
            $baseAmount = $this->getBaseAmountByCurrency($currencyCode);
            $balance = $baseAmount * rand(1, 10);
            $reserved = $balance * (rand(0, 20) / 100); // 0-20% reserved
            
            $this->createClientBalance($client, $currencyCode, [
                'balance' => $balance,
                'reserved' => $reserved,
            ]);
        }
    }
    
    private function getBaseAmountByCurrency(string $currencyCode): float
    {
        return match ($currencyCode) {
            'TRY' => 5000.00,
            'USD' => 500.00,
            'EUR' => 450.00,
            'GBP' => 400.00,
            'AED' => 1800.00,
            'IRR' => ********.00, // Iranian Rial has much higher nominal values
            default => 1000.00,
        };
    }
    
    private function createSpecialBalanceScenarios(): void
    {
        // Create additional test clients for special scenarios
        $specialClients = [
            [
                'name' => 'Zero Balance Test Client',
                'account_number' => 'TEST001',
                'currency_code' => 'USD',
                'balances' => [
                    'USD' => ['balance' => 0.00, 'reserved' => 0.00],
                    'TRY' => ['balance' => 0.00, 'reserved' => 0.00],
                ],
            ],
            [
                'name' => 'High Volume Test Client',
                'account_number' => 'TEST002',
                'currency_code' => 'USD',
                'balances' => [
                    'USD' => ['balance' => 100000.00, 'reserved' => 5000.00],
                    'EUR' => ['balance' => 85000.00, 'reserved' => 4000.00],
                    'TRY' => ['balance' => 2500000.00, 'reserved' => 100000.00],
                    'IRR' => ['balance' => **********.00, 'reserved' => *********.00],
                ],
            ],
            [
                'name' => 'Multi-Currency Test Client',
                'account_number' => 'TEST003',
                'currency_code' => 'EUR',
                'balances' => [
                    'USD' => ['balance' => 5000.00, 'reserved' => 500.00],
                    'EUR' => ['balance' => 4500.00, 'reserved' => 450.00],
                    'GBP' => ['balance' => 3500.00, 'reserved' => 350.00],
                    'AED' => ['balance' => 18000.00, 'reserved' => 1800.00],
                    'TRY' => ['balance' => 125000.00, 'reserved' => 12500.00],
                ],
            ],
        ];
        
        foreach ($specialClients as $clientData) {
            $client = Client::create([
                'company_id' => 1,
                'name' => $clientData['name'],
                'currency_code' => $clientData['currency_code'],
                'account_number' => $clientData['account_number'],
                'notes' => 'Test client for special balance scenarios',
            ]);
            
            // Create primary contact
            $client->primaryContact()->create([
                'is_primary' => true,
                'first_name' => 'Test',
                'last_name' => 'Client',
                'email' => strtolower(str_replace(' ', '.', $clientData['name'])) . '@test.com',
                'phones' => ['+90 ************'],
            ]);
            
            foreach ($clientData['balances'] as $currencyCode => $amounts) {
                $this->createClientBalance($client, $currencyCode, $amounts);
            }
        }
    }
}
