<?php

namespace App\Http\Controllers\Api\ArenaDoviz;

use App\Http\Controllers\Controller;
use App\Models\ArenaDoviz\LocationCurrency;
use App\Services\ArenaDoviz\RealTimeRateService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CurrencyRateController extends Controller
{
    public function __construct(
        private RealTimeRateService $rateService
    ) {}

    /**
     * Get latest currency rates
     */
    public function latest(Request $request): JsonResponse
    {
        $companyId = $request->user()->currentCompany->id ?? null;
        
        if (!$companyId) {
            return response()->json(['error' => 'No company selected'], 400);
        }

        $rates = LocationCurrency::where('company_id', $companyId)
            ->where('is_active', true)
            ->with('location')
            ->get()
            ->map(function ($rate) {
                return [
                    'currency_code' => $rate->location_currency_code,
                    'location_name' => $rate->location->name ?? 'Unknown',
                    'buy_rate' => $rate->buy_rate,
                    'sell_rate' => $rate->sell_rate,
                    'mid_rate' => $rate->mid_rate,
                    'updated_at' => $rate->updated_at->toISOString(),
                    'formatted_buy_rate' => \App\Helpers\ArenaDoviz\NumberFormatter::formatExchangeRate($rate->buy_rate),
                    'formatted_sell_rate' => \App\Helpers\ArenaDoviz\NumberFormatter::formatExchangeRate($rate->sell_rate),
                ];
            });

        return response()->json([
            'rates' => $rates,
            'last_updated' => now()->toISOString(),
        ]);
    }

    /**
     * Get specific currency rate
     */
    public function show(Request $request, string $currencyCode): JsonResponse
    {
        $companyId = $request->user()->currentCompany->id ?? null;
        
        if (!$companyId) {
            return response()->json(['error' => 'No company selected'], 400);
        }

        $rate = LocationCurrency::where('company_id', $companyId)
            ->where('location_currency_code', $currencyCode)
            ->where('is_active', true)
            ->with('location')
            ->first();

        if (!$rate) {
            return response()->json(['error' => 'Currency rate not found'], 404);
        }

        return response()->json([
            'currency_code' => $rate->location_currency_code,
            'location_name' => $rate->location->name ?? 'Unknown',
            'buy_rate' => $rate->buy_rate,
            'sell_rate' => $rate->sell_rate,
            'mid_rate' => $rate->mid_rate,
            'updated_at' => $rate->updated_at->toISOString(),
            'formatted_buy_rate' => \App\Helpers\ArenaDoviz\NumberFormatter::formatExchangeRate($rate->buy_rate),
            'formatted_sell_rate' => \App\Helpers\ArenaDoviz\NumberFormatter::formatExchangeRate($rate->sell_rate),
        ]);
    }

    /**
     * Update currency rates manually
     */
    public function update(Request $request): JsonResponse
    {
        $companyId = $request->user()->currentCompany->id ?? null;
        
        if (!$companyId) {
            return response()->json(['error' => 'No company selected'], 400);
        }

        try {
            $results = $this->rateService->updateAllRates();
            
            $successCount = collect($results)->where('success', true)->count();
            $totalCount = count($results);
            
            return response()->json([
                'message' => 'Currency rates updated successfully',
                'success_count' => $successCount,
                'total_count' => $totalCount,
                'results' => $results,
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to update currency rates',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get rate history
     */
    public function history(Request $request, string $currencyCode): JsonResponse
    {
        $companyId = $request->user()->currentCompany->id ?? null;
        
        if (!$companyId) {
            return response()->json(['error' => 'No company selected'], 400);
        }

        $days = $request->get('days', 7);
        $history = $this->rateService->getRateHistory($currencyCode, $days);

        return response()->json([
            'currency_code' => $currencyCode,
            'days' => $days,
            'history' => $history,
        ]);
    }

    /**
     * Get rate change alerts
     */
    public function alerts(Request $request): JsonResponse
    {
        $threshold = $request->get('threshold', 2.0);
        $alerts = $this->rateService->getRateChangeAlerts($threshold);

        return response()->json([
            'threshold_percent' => $threshold,
            'alerts' => $alerts,
            'count' => count($alerts),
        ]);
    }

    /**
     * Check if rates need updating
     */
    public function status(Request $request): JsonResponse
    {
        $needsUpdate = $this->rateService->ratesNeedUpdate();
        
        return response()->json([
            'needs_update' => $needsUpdate,
            'last_check' => now()->toISOString(),
        ]);
    }

    /**
     * Get cached rate
     */
    public function cached(Request $request, string $currencyCode): JsonResponse
    {
        $cachedRate = $this->rateService->getCachedRate($currencyCode);
        
        if (!$cachedRate) {
            return response()->json(['error' => 'No cached rate found'], 404);
        }

        return response()->json([
            'currency_code' => $currencyCode,
            'cached_rate' => $cachedRate,
        ]);
    }
}
