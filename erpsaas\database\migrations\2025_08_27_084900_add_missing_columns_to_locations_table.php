<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('locations', function (Blueprint $table) {
            // Add missing columns that the seeder expects
            $table->string('city', 100)->nullable()->after('code');
            $table->string('country', 100)->nullable()->after('city');
            $table->json('services')->nullable()->after('working_hours');
            $table->decimal('max_transaction_amount', 15, 2)->nullable()->after('services');
            
            // Remove columns that are no longer needed
            $table->dropColumn(['country_code', 'currency_code', 'is_main_office']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('locations', function (Blueprint $table) {
            // Remove added columns
            $table->dropColumn(['city', 'country', 'services', 'max_transaction_amount']);
            
            // Add back removed columns
            $table->string('country_code', 2)->nullable()->after('timezone');
            $table->string('currency_code', 3)->nullable()->after('country_code');
            $table->boolean('is_main_office')->default(false)->after('currency_code');
        });
    }
};
