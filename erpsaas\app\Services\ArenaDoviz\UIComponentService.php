<?php

namespace App\Services\ArenaDoviz;

use App\Models\ArenaDoviz\CurrencyExchange;
use App\Models\Common\Client;
use Illuminate\Support\Collection;

class UIComponentService
{
    /**
     * Get dashboard stats for Arena Doviz
     */
    public function getDashboardStats(int $companyId): array
    {
        $todayTransactions = CurrencyExchange::where('company_id', $companyId)
            ->whereDate('exchange_date', today())
            ->count();

        $pendingApprovals = CurrencyExchange::where('company_id', $companyId)
            ->where('status', 'pending')
            ->count();

        $activeClients = Client::where('company_id', $companyId)
            ->whereHas('currencyExchanges', function ($query) {
                $query->where('exchange_date', '>=', now()->subDays(30));
            })
            ->count();

        $totalBalance = CurrencyExchange::where('company_id', $companyId)
            ->sum('commission_amount');

        return [
            [
                'label' => 'Total Company Balance',
                'value' => number_format($totalBalance, 2) . ' TRY',
                'description' => 'All currencies combined',
                'icon' => 'heroicon-o-currency-dollar',
                'color' => 'success',
            ],
            [
                'label' => "Today's Transactions",
                'value' => $todayTransactions,
                'description' => 'Transactions processed today',
                'icon' => 'heroicon-o-arrow-trending-up',
                'color' => 'info',
            ],
            [
                'label' => 'Pending Approvals',
                'value' => $pendingApprovals,
                'description' => 'Awaiting approval',
                'icon' => 'heroicon-o-clock',
                'color' => 'warning',
            ],
            [
                'label' => 'Active Customers',
                'value' => $activeClients,
                'description' => 'Active in last 30 days',
                'icon' => 'heroicon-o-users',
                'color' => 'primary',
            ],
        ];
    }

    /**
     * Get recent transactions for dashboard
     */
    public function getRecentTransactions(int $companyId, int $limit = 10): Collection
    {
        return CurrencyExchange::where('company_id', $companyId)
            ->with(['client', 'fromCurrency', 'toCurrency'])
            ->orderBy('exchange_date', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($transaction) {
                return [
                    'id' => $transaction->id,
                    'number' => '#' . str_pad($transaction->id, 3, '0', STR_PAD_LEFT),
                    'type' => ucfirst($transaction->exchange_type->value),
                    'client' => $transaction->client->name,
                    'amount' => number_format($transaction->from_amount, 2) . ' ' . $transaction->fromCurrency->code,
                    'status' => $transaction->status->value,
                    'status_color' => $this->getStatusColor($transaction->status->value),
                    'time' => $transaction->exchange_date->format('H:i'),
                    'url' => \App\Filament\Company\Resources\CurrencyExchangeResource::getUrl('view', ['record' => $transaction]),
                ];
            });
    }

    /**
     * Get currency rates widget data
     */
    public function getCurrencyRates(): array
    {
        // This would typically fetch from a live currency service
        // For now, returning mock data that follows the design
        return [
            [
                'currency' => 'USD/TRY',
                'buy_rate' => '32.15',
                'sell_rate' => '32.25',
                'location' => 'Istanbul',
                'updated_at' => now()->format('H:i'),
            ],
            [
                'currency' => 'EUR/TRY',
                'buy_rate' => '34.80',
                'sell_rate' => '34.95',
                'location' => 'Istanbul',
                'updated_at' => now()->format('H:i'),
            ],
            [
                'currency' => 'IRR/TRY',
                'buy_rate' => '0.00076',
                'sell_rate' => '0.00078',
                'location' => 'Istanbul',
                'updated_at' => now()->format('H:i'),
            ],
        ];
    }

    /**
     * Get balance alerts
     */
    public function getBalanceAlerts(int $companyId): array
    {
        $lowBalanceClients = Client::where('company_id', $companyId)
            ->whereHas('arenaProfile', function ($query) {
                $query->where('credit_limit', '>', 0)
                    ->whereRaw('credit_limit < expected_monthly_volume * 0.1');
            })
            ->with('arenaProfile')
            ->get();

        return $lowBalanceClients->map(function ($client) {
            return [
                'type' => 'low_balance',
                'message' => "Low balance warning for {$client->name}",
                'severity' => 'warning',
                'client_id' => $client->id,
                'url' => \App\Filament\Company\Resources\ClientProfileResource::getUrl('view', ['record' => $client]),
            ];
        })->toArray();
    }

    /**
     * Get status color for transaction status
     */
    private function getStatusColor(string $status): string
    {
        return match ($status) {
            'completed' => 'success',
            'pending' => 'warning',
            'cancelled' => 'danger',
            'processing' => 'info',
            default => 'gray',
        };
    }

    /**
     * Format currency amount with proper formatting
     */
    public function formatCurrency(float $amount, string $currencyCode): string
    {
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'TRY' => '₺',
            'IRR' => '﷼',
        ];

        $symbol = $symbols[$currencyCode] ?? $currencyCode;
        
        return $symbol . number_format($amount, 2);
    }

    /**
     * Get transaction type options for forms
     */
    public function getTransactionTypeOptions(): array
    {
        return [
            'buy' => 'Buy',
            'sell' => 'Sell',
            'buy_sell' => 'Buy+Sell',
            'transfer' => 'Transfer',
            'swift' => 'SWIFT',
        ];
    }

    /**
     * Get commission type options
     */
    public function getCommissionTypeOptions(): array
    {
        return [
            'percentage' => 'Percentage (%)',
            'fixed' => 'Fixed Amount',
        ];
    }

    /**
     * Get delivery method options
     */
    public function getDeliveryMethodOptions(): array
    {
        return [
            'pickup' => 'Pickup from Office',
            'courier' => 'Courier Delivery',
            'bank_transfer' => 'Bank Transfer',
            'cash' => 'Cash Delivery',
        ];
    }
}
