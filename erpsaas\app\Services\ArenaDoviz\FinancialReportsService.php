<?php

namespace App\Services\ArenaDoviz;

use App\Models\ArenaDoviz\ClientBalance;
use App\Models\ArenaDoviz\CurrencyExchange;
use App\Models\Company;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class FinancialReportsService
{
    /**
     * Generate Profit & Loss Report
     */
    public function generateProfitLossReport(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $revenue = $this->calculateRevenue($company, $startDate, $endDate);
        $expenses = $this->calculateExpenses($company, $startDate, $endDate);
        $netIncome = $revenue['total'] - $expenses['total'];

        return [
            'period' => [
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => $endDate->format('Y-m-d'),
                'days' => $startDate->diffInDays($endDate) + 1,
            ],
            'revenue' => $revenue,
            'expenses' => $expenses,
            'net_income' => $netIncome,
            'profit_margin' => $revenue['total'] > 0 ? ($netIncome / $revenue['total']) * 100 : 0,
            'daily_average' => [
                'revenue' => $revenue['total'] / ($startDate->diffInDays($endDate) + 1),
                'expenses' => $expenses['total'] / ($startDate->diffInDays($endDate) + 1),
                'net_income' => $netIncome / ($startDate->diffInDays($endDate) + 1),
            ],
        ];
    }

    /**
     * Generate Balance Sheet Report
     */
    public function generateBalanceSheetReport(Company $company, Carbon $asOfDate): array
    {
        $assets = $this->calculateAssets($company, $asOfDate);
        $liabilities = $this->calculateLiabilities($company, $asOfDate);
        $equity = $assets['total'] - $liabilities['total'];

        return [
            'as_of_date' => $asOfDate->format('Y-m-d'),
            'assets' => $assets,
            'liabilities' => $liabilities,
            'equity' => [
                'total' => $equity,
                'breakdown' => [
                    'retained_earnings' => $equity,
                ],
            ],
            'balance_check' => abs($assets['total'] - ($liabilities['total'] + $equity)) < 0.01,
        ];
    }

    /**
     * Generate Cash Flow Report
     */
    public function generateCashFlowReport(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $operatingCashFlow = $this->calculateOperatingCashFlow($company, $startDate, $endDate);
        $investingCashFlow = $this->calculateInvestingCashFlow($company, $startDate, $endDate);
        $financingCashFlow = $this->calculateFinancingCashFlow($company, $startDate, $endDate);

        $netCashFlow = $operatingCashFlow + $investingCashFlow + $financingCashFlow;

        return [
            'period' => [
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => $endDate->format('Y-m-d'),
            ],
            'operating_cash_flow' => $operatingCashFlow,
            'investing_cash_flow' => $investingCashFlow,
            'financing_cash_flow' => $financingCashFlow,
            'net_cash_flow' => $netCashFlow,
            'cash_flow_breakdown' => $this->getCashFlowBreakdown($company, $startDate, $endDate),
        ];
    }

    /**
     * Generate Transaction Summary Report
     */
    public function generateTransactionSummaryReport(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $transactions = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->get();

        $summary = [
            'total_transactions' => $transactions->count(),
            'total_volume' => $transactions->sum('from_amount'),
            'total_commission' => $transactions->sum('commission_amount'),
            'by_type' => $transactions->groupBy(fn($item) => $item->exchange_type->value)->map(function ($group) {
                return [
                    'count' => $group->count(),
                    'volume' => $group->sum('from_amount'),
                    'commission' => $group->sum('commission_amount'),
                    'avg_amount' => $group->avg('from_amount'),
                ];
            }),
            'by_currency' => $transactions->groupBy('from_currency_code')->map(function ($group) {
                return [
                    'count' => $group->count(),
                    'volume' => $group->sum('from_amount'),
                    'commission' => $group->sum('commission_amount'),
                ];
            }),
            'by_status' => $transactions->groupBy('status')->map(function ($group) {
                return [
                    'count' => $group->count(),
                    'volume' => $group->sum('from_amount'),
                ];
            }),
        ];

        return $summary;
    }

    /**
     * Generate Commission Analysis Report
     */
    public function generateCommissionAnalysisReport(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $transactions = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->where('from_amount', '>', 0)
            ->get();

        $commissionRates = $transactions->map(function ($transaction) {
            return ($transaction->commission_amount / $transaction->from_amount) * 100;
        });

        return [
            'total_commission' => $transactions->sum('commission_amount'),
            'avg_commission_rate' => $commissionRates->avg(),
            'min_commission_rate' => $commissionRates->min(),
            'max_commission_rate' => $commissionRates->max(),
            'commission_by_type' => $transactions->groupBy(fn($item) => $item->exchange_type->value)->map(function ($group) {
                $rates = $group->map(function ($transaction) {
                    return ($transaction->commission_amount / $transaction->from_amount) * 100;
                });

                return [
                    'total_commission' => $group->sum('commission_amount'),
                    'avg_rate' => $rates->avg(),
                    'transaction_count' => $group->count(),
                ];
            }),
            'commission_distribution' => $this->getCommissionDistribution($transactions),
        ];
    }

    /**
     * Generate Client Profitability Report
     */
    public function generateClientProfitabilityReport(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $clientData = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->selectRaw('
                client_id,
                COUNT(*) as transaction_count,
                SUM(from_amount) as total_volume,
                SUM(commission_amount) as total_commission,
                AVG(commission_amount) as avg_commission,
                MIN(exchange_date) as first_transaction,
                MAX(exchange_date) as last_transaction
            ')
            ->groupBy('client_id')
            ->with('client')
            ->get();

        $topClients = $clientData->sortByDesc('total_commission')->take(20);
        $clientSegments = $this->segmentClientsByProfitability($clientData);

        return [
            'summary' => [
                'total_clients' => $clientData->count(),
                'total_commission' => $clientData->sum('total_commission'),
                'avg_commission_per_client' => $clientData->avg('total_commission'),
            ],
            'top_clients' => $topClients,
            'client_segments' => $clientSegments,
            'retention_analysis' => $this->analyzeClientRetention($company, $startDate, $endDate),
        ];
    }

    /**
     * Calculate revenue breakdown
     */
    private function calculateRevenue(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $commissionRevenue = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->sum('commission_amount');

        $revenueByType = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->selectRaw('exchange_type, SUM(commission_amount) as revenue')
            ->groupBy('exchange_type')
            ->get()
            ->pluck('revenue', fn($item) => $item->exchange_type->value);

        return [
            'total' => $commissionRevenue,
            'commission_revenue' => $commissionRevenue,
            'by_type' => $revenueByType,
            'other_revenue' => 0, // Placeholder for other revenue sources
        ];
    }

    /**
     * Calculate expenses (placeholder - would need expense tracking)
     */
    private function calculateExpenses(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        // This is a placeholder - in a real system, you'd track actual expenses
        return [
            'total' => 0,
            'operational_expenses' => 0,
            'delivery_costs' => 0,
            'administrative_expenses' => 0,
            'other_expenses' => 0,
        ];
    }

    /**
     * Calculate assets
     */
    private function calculateAssets(Company $company, Carbon $asOfDate): array
    {
        $cashBalances = ClientBalance::where('company_id', $company->id)
            ->where('updated_at', '<=', $asOfDate)
            ->sum('balance');

        return [
            'total' => $cashBalances,
            'current_assets' => [
                'cash_and_equivalents' => $cashBalances,
                'accounts_receivable' => 0, // Placeholder
            ],
            'non_current_assets' => [
                'equipment' => 0, // Placeholder
                'intangible_assets' => 0, // Placeholder
            ],
        ];
    }

    /**
     * Calculate liabilities
     */
    private function calculateLiabilities(Company $company, Carbon $asOfDate): array
    {
        $clientLiabilities = ClientBalance::where('company_id', $company->id)
            ->where('balance', '<', 0)
            ->where('updated_at', '<=', $asOfDate)
            ->sum('balance');

        return [
            'total' => abs($clientLiabilities),
            'current_liabilities' => [
                'client_balances' => abs($clientLiabilities),
                'accounts_payable' => 0, // Placeholder
            ],
            'non_current_liabilities' => [
                'long_term_debt' => 0, // Placeholder
            ],
        ];
    }

    /**
     * Calculate operating cash flow
     */
    private function calculateOperatingCashFlow(Company $company, Carbon $startDate, Carbon $endDate): float
    {
        return CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->where('status', 'completed')
            ->sum('commission_amount');
    }

    /**
     * Calculate investing cash flow
     */
    private function calculateInvestingCashFlow(Company $company, Carbon $startDate, Carbon $endDate): float
    {
        // Placeholder - would track equipment purchases, etc.
        return 0;
    }

    /**
     * Calculate financing cash flow
     */
    private function calculateFinancingCashFlow(Company $company, Carbon $startDate, Carbon $endDate): float
    {
        // Placeholder - would track loans, equity investments, etc.
        return 0;
    }

    /**
     * Get cash flow breakdown
     */
    private function getCashFlowBreakdown(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $dailyCashFlow = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->where('status', 'completed')
            ->selectRaw('DATE(exchange_date) as date, SUM(commission_amount) as cash_flow')
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('cash_flow', 'date');

        return $dailyCashFlow->toArray();
    }

    /**
     * Get commission distribution
     */
    private function getCommissionDistribution(Collection $transactions): array
    {
        $ranges = [
            '0-0.5%' => [0, 0.5],
            '0.5-1%' => [0.5, 1],
            '1-1.5%' => [1, 1.5],
            '1.5-2%' => [1.5, 2],
            '2%+' => [2, 100],
        ];

        $distribution = [];

        foreach ($ranges as $label => $range) {
            $count = $transactions->filter(function ($transaction) use ($range) {
                $rate = ($transaction->commission_amount / $transaction->from_amount) * 100;
                return $rate >= $range[0] && $rate < $range[1];
            })->count();

            $distribution[$label] = $count;
        }

        return $distribution;
    }

    /**
     * Segment clients by profitability
     */
    private function segmentClientsByProfitability(Collection $clientData): array
    {
        $totalCommission = $clientData->sum('total_commission');
        $avgCommission = $clientData->avg('total_commission');

        return [
            'high_value' => $clientData->filter(fn($client) => $client->total_commission > $avgCommission * 2)->count(),
            'medium_value' => $clientData->filter(fn($client) => $client->total_commission >= $avgCommission && $client->total_commission <= $avgCommission * 2)->count(),
            'low_value' => $clientData->filter(fn($client) => $client->total_commission < $avgCommission)->count(),
        ];
    }

    /**
     * Analyze client retention
     */
    private function analyzeClientRetention(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $previousPeriodStart = $startDate->copy()->subDays($startDate->diffInDays($endDate) + 1);
        $previousPeriodEnd = $startDate->copy()->subDay();

        $previousClients = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$previousPeriodStart, $previousPeriodEnd])
            ->distinct('client_id')
            ->pluck('client_id');

        $currentClients = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->distinct('client_id')
            ->pluck('client_id');

        $retainedClients = $previousClients->intersect($currentClients);
        $newClients = $currentClients->diff($previousClients);
        $lostClients = $previousClients->diff($currentClients);

        return [
            'previous_period_clients' => $previousClients->count(),
            'current_period_clients' => $currentClients->count(),
            'retained_clients' => $retainedClients->count(),
            'new_clients' => $newClients->count(),
            'lost_clients' => $lostClients->count(),
            'retention_rate' => $previousClients->count() > 0 ? ($retainedClients->count() / $previousClients->count()) * 100 : 0,
            'growth_rate' => $previousClients->count() > 0 ? (($currentClients->count() - $previousClients->count()) / $previousClients->count()) * 100 : 0,
        ];
    }
}
