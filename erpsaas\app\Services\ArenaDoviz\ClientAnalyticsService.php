<?php

namespace App\Services\ArenaDoviz;

use App\Models\ArenaDoviz\ClientBalance;
use App\Models\ArenaDoviz\CurrencyExchange;
use App\Models\Common\Client;
use App\Models\Company;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ClientAnalyticsService
{
    /**
     * Get comprehensive client analytics
     */
    public function getClientAnalytics(Company $company, ?Carbon $startDate = null, ?Carbon $endDate = null): array
    {
        $startDate = $startDate ?? now()->startOfMonth();
        $endDate = $endDate ?? now()->endOfDay();

        return [
            'overview' => $this->getClientOverview($company, $startDate, $endDate),
            'profitability' => $this->getClientProfitability($company, $startDate, $endDate),
            'behavior' => $this->getClientBehavior($company, $startDate, $endDate),
            'segmentation' => $this->getClientSegmentation($company, $startDate, $endDate),
            'retention' => $this->getClientRetention($company, $startDate, $endDate),
            'risk_analysis' => $this->getClientRiskAnalysis($company),
            'growth_trends' => $this->getClientGrowthTrends($company, $startDate, $endDate),
        ];
    }

    /**
     * Get client overview metrics
     */
    public function getClientOverview(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $totalClients = Client::where('company_id', $company->id)->count();
        $activeClients = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->distinct('client_id')
            ->count('client_id');

        $newClients = Client::where('company_id', $company->id)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $clientsWithBalances = ClientBalance::where('company_id', $company->id)
            ->where('balance', '>', 0)
            ->distinct('client_id')
            ->count('client_id');

        return [
            'total_clients' => $totalClients,
            'active_clients' => $activeClients,
            'new_clients' => $newClients,
            'clients_with_balances' => $clientsWithBalances,
            'activity_rate' => $totalClients > 0 ? ($activeClients / $totalClients) * 100 : 0,
            'balance_penetration' => $totalClients > 0 ? ($clientsWithBalances / $totalClients) * 100 : 0,
        ];
    }

    /**
     * Get client profitability analysis
     */
    public function getClientProfitability(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $clientProfitability = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->selectRaw('
                client_id,
                COUNT(*) as transaction_count,
                SUM(from_amount) as total_volume,
                SUM(commission_amount) as total_commission,
                AVG(commission_amount) as avg_commission,
                MIN(exchange_date) as first_transaction,
                MAX(exchange_date) as last_transaction
            ')
            ->groupBy('client_id')
            ->with('client.profile')
            ->get();

        $topClients = $clientProfitability->sortByDesc('total_commission')->take(20);
        $totalCommission = $clientProfitability->sum('total_commission');

        // Calculate client lifetime value
        $clientLTV = $clientProfitability->map(function ($client) {
            $daysSinceFirst = now()->diffInDays($client->first_transaction);
            $avgDailyCommission = $daysSinceFirst > 0 ? $client->total_commission / $daysSinceFirst : 0;
            return [
                'client_id' => $client->client_id,
                'client_name' => $client->client->name ?? 'Unknown',
                'ltv' => $avgDailyCommission * 365, // Projected annual value
                'total_commission' => $client->total_commission,
                'transaction_count' => $client->transaction_count,
                'avg_commission' => $client->avg_commission,
                'days_active' => $daysSinceFirst,
            ];
        })->sortByDesc('ltv');

        return [
            'top_clients' => $topClients,
            'client_ltv' => $clientLTV->take(20),
            'profitability_distribution' => $this->calculateProfitabilityDistribution($clientProfitability),
            'commission_concentration' => $this->calculateCommissionConcentration($clientProfitability, $totalCommission),
        ];
    }

    /**
     * Get client behavior analysis
     */
    public function getClientBehavior(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $transactionPatterns = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->selectRaw('
                client_id,
                COUNT(*) as transaction_count,
                AVG(from_amount) as avg_transaction_size,
                STDDEV(from_amount) as transaction_size_variance,
                COUNT(DISTINCT exchange_type) as transaction_types_used,
                COUNT(DISTINCT from_currency_code) as currencies_used,
                AVG(TIMESTAMPDIFF(DAY, exchange_date, settlement_date)) as avg_settlement_days
            ')
            ->groupBy('client_id')
            ->get();

        $frequencyAnalysis = $this->analyzeTransactionFrequency($company, $startDate, $endDate);
        $preferenceAnalysis = $this->analyzeClientPreferences($company, $startDate, $endDate);
        $timingAnalysis = $this->analyzeTransactionTiming($company, $startDate, $endDate);

        return [
            'transaction_patterns' => $transactionPatterns,
            'frequency_analysis' => $frequencyAnalysis,
            'preference_analysis' => $preferenceAnalysis,
            'timing_analysis' => $timingAnalysis,
            'behavior_segments' => $this->segmentClientsByBehavior($transactionPatterns),
        ];
    }

    /**
     * Get client segmentation
     */
    public function getClientSegmentation(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $clients = Client::where('company_id', $company->id)
            ->with(['profile', 'currencyExchanges' => function ($query) use ($startDate, $endDate) {
                $query->whereBetween('exchange_date', [$startDate, $endDate]);
            }])
            ->get();

        $segments = [
            'by_category' => $clients->whereNotNull('profile')->groupBy('profile.category')->map->count(),
            'by_risk_level' => $clients->whereNotNull('profile')->groupBy('profile.risk_level')->map->count(),
            'by_kyc_status' => $clients->whereNotNull('profile')->groupBy('profile.kyc_status')->map->count(),
            'by_activity_level' => $this->segmentByActivityLevel($clients, $startDate, $endDate),
            'by_value_tier' => $this->segmentByValueTier($clients),
        ];

        return $segments;
    }

    /**
     * Get client retention analysis
     */
    public function getClientRetention(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $cohortAnalysis = $this->performCohortAnalysis($company, $startDate, $endDate);
        $churnAnalysis = $this->analyzeClientChurn($company, $startDate, $endDate);
        $loyaltyMetrics = $this->calculateLoyaltyMetrics($company, $startDate, $endDate);

        return [
            'cohort_analysis' => $cohortAnalysis,
            'churn_analysis' => $churnAnalysis,
            'loyalty_metrics' => $loyaltyMetrics,
            'retention_rate' => $this->calculateRetentionRate($company, $startDate, $endDate),
        ];
    }

    /**
     * Get client risk analysis
     */
    public function getClientRiskAnalysis(Company $company): array
    {
        $riskMetrics = Client::where('clients.company_id', $company->id)
            ->whereHas('profile')
            ->join('client_profiles', 'clients.id', '=', 'client_profiles.client_id')
            ->where('client_profiles.company_id', $company->id)
            ->selectRaw('
                client_profiles.risk_level,
                COUNT(*) as client_count,
                AVG(client_profiles.risk_score) as avg_risk_score
            ')
            ->groupBy('client_profiles.risk_level')
            ->get();

        $highRiskClients = Client::where('company_id', $company->id)
            ->whereHas('profile', function ($query) {
                $query->where('risk_level', 'high')
                    ->orWhere('risk_score', '>', 70);
            })
            ->with('profile')
            ->get();

        return [
            'risk_distribution' => $riskMetrics,
            'high_risk_clients' => $highRiskClients,
            'risk_trends' => $this->analyzeRiskTrends($company),
        ];
    }

    /**
     * Get client growth trends
     */
    public function getClientGrowthTrends(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $dailyNewClients = Client::where('company_id', $company->id)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('DATE(created_at) as date, COUNT(*) as new_clients')
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('new_clients', 'date');

        $monthlyGrowth = Client::where('company_id', $company->id)
            ->selectRaw('YEAR(created_at) as year, MONTH(created_at) as month, COUNT(*) as new_clients')
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();

        return [
            'daily_new_clients' => $dailyNewClients,
            'monthly_growth' => $monthlyGrowth,
            'growth_rate' => $this->calculateGrowthRate($company, $startDate, $endDate),
            'acquisition_channels' => $this->analyzeAcquisitionChannels($company, $startDate, $endDate),
        ];
    }

    /**
     * Calculate profitability distribution
     */
    private function calculateProfitabilityDistribution(Collection $clientProfitability): array
    {
        $totalCommission = $clientProfitability->sum('total_commission');
        $clientCount = $clientProfitability->count();

        if ($clientCount === 0) {
            return [];
        }

        $avgCommission = $totalCommission / $clientCount;

        return [
            'high_value' => $clientProfitability->filter(fn($client) => $client->total_commission > $avgCommission * 2)->count(),
            'medium_value' => $clientProfitability->filter(fn($client) => $client->total_commission >= $avgCommission && $client->total_commission <= $avgCommission * 2)->count(),
            'low_value' => $clientProfitability->filter(fn($client) => $client->total_commission < $avgCommission)->count(),
        ];
    }

    /**
     * Calculate commission concentration (80/20 rule)
     */
    private function calculateCommissionConcentration(Collection $clientProfitability, float $totalCommission): array
    {
        $sortedClients = $clientProfitability->sortByDesc('total_commission');
        $clientCount = $sortedClients->count();

        if ($clientCount === 0) {
            return ['top_20_percent_contribution' => 0, 'top_10_percent_contribution' => 0];
        }

        $top20Count = max(1, intval($clientCount * 0.2));
        $top10Count = max(1, intval($clientCount * 0.1));

        $top20Commission = $sortedClients->take($top20Count)->sum('total_commission');
        $top10Commission = $sortedClients->take($top10Count)->sum('total_commission');

        return [
            'top_20_percent_contribution' => $totalCommission > 0 ? ($top20Commission / $totalCommission) * 100 : 0,
            'top_10_percent_contribution' => $totalCommission > 0 ? ($top10Commission / $totalCommission) * 100 : 0,
        ];
    }

    /**
     * Analyze transaction frequency
     */
    private function analyzeTransactionFrequency(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $clientFrequency = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->selectRaw('client_id, COUNT(*) as transaction_count')
            ->groupBy('client_id')
            ->pluck('transaction_count');

        $frequencyDistribution = [
            'single_transaction' => $clientFrequency->filter(fn($count) => $count == 1)->count(),
            'low_frequency' => $clientFrequency->filter(fn($count) => $count >= 2 && $count <= 5)->count(),
            'medium_frequency' => $clientFrequency->filter(fn($count) => $count >= 6 && $count <= 15)->count(),
            'high_frequency' => $clientFrequency->filter(fn($count) => $count > 15)->count(),
        ];

        return [
            'distribution' => $frequencyDistribution,
            'avg_transactions_per_client' => $clientFrequency->avg(),
            'max_transactions_per_client' => $clientFrequency->max(),
        ];
    }

    /**
     * Analyze client preferences
     */
    private function analyzeClientPreferences(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $preferences = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->selectRaw('
                client_id,
                exchange_type,
                from_currency_code,
                to_currency_code,
                COUNT(*) as usage_count
            ')
            ->groupBy('client_id', 'exchange_type', 'from_currency_code', 'to_currency_code')
            ->get()
            ->groupBy('client_id');

        $commonPreferences = [
            'most_used_exchange_type' => CurrencyExchange::where('company_id', $company->id)
                ->whereBetween('exchange_date', [$startDate, $endDate])
                ->selectRaw('exchange_type, COUNT(*) as count')
                ->groupBy('exchange_type')
                ->orderByDesc('count')
                ->first(),
            'most_traded_currency_pair' => CurrencyExchange::where('company_id', $company->id)
                ->whereBetween('exchange_date', [$startDate, $endDate])
                ->selectRaw('CONCAT(from_currency_code, "/", to_currency_code) as pair, COUNT(*) as count')
                ->groupBy('pair')
                ->orderByDesc('count')
                ->first(),
        ];

        return [
            'individual_preferences' => $preferences,
            'common_preferences' => $commonPreferences,
        ];
    }

    /**
     * Analyze transaction timing
     */
    private function analyzeTransactionTiming(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $hourlyDistribution = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->selectRaw('HOUR(exchange_date) as hour, COUNT(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('count', 'hour');

        $dailyDistribution = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->selectRaw('DAYOFWEEK(exchange_date) as day, COUNT(*) as count')
            ->groupBy('day')
            ->orderBy('day')
            ->pluck('count', 'day');

        return [
            'hourly_distribution' => $hourlyDistribution,
            'daily_distribution' => $dailyDistribution,
            'peak_hours' => $hourlyDistribution->sortDesc()->take(3)->keys(),
            'peak_days' => $dailyDistribution->sortDesc()->take(3)->keys(),
        ];
    }

    /**
     * Segment clients by behavior
     */
    private function segmentClientsByBehavior(Collection $transactionPatterns): array
    {
        return [
            'frequent_traders' => $transactionPatterns->filter(fn($client) => $client->transaction_count > 10)->count(),
            'occasional_traders' => $transactionPatterns->filter(fn($client) => $client->transaction_count >= 3 && $client->transaction_count <= 10)->count(),
            'one_time_traders' => $transactionPatterns->filter(fn($client) => $client->transaction_count < 3)->count(),
            'high_volume_traders' => $transactionPatterns->filter(fn($client) => $client->avg_transaction_size > 10000)->count(),
            'diverse_traders' => $transactionPatterns->filter(fn($client) => $client->transaction_types_used > 2)->count(),
        ];
    }

    /**
     * Segment by activity level
     */
    private function segmentByActivityLevel(Collection $clients, Carbon $startDate, Carbon $endDate): array
    {
        return $clients->map(function ($client) use ($startDate, $endDate) {
            $transactionCount = $client->currencyExchanges->count();
            
            if ($transactionCount === 0) return 'inactive';
            if ($transactionCount >= 10) return 'highly_active';
            if ($transactionCount >= 3) return 'moderately_active';
            return 'low_activity';
        })->countBy();
    }

    /**
     * Segment by value tier
     */
    private function segmentByValueTier(Collection $clients): array
    {
        $clientValues = $clients->map(function ($client) {
            return $client->currencyExchanges->sum('commission_amount');
        });

        $avgValue = $clientValues->avg();

        return $clients->map(function ($client) use ($avgValue) {
            $value = $client->currencyExchanges->sum('commission_amount');
            
            if ($value > $avgValue * 3) return 'vip';
            if ($value > $avgValue * 1.5) return 'premium';
            if ($value > $avgValue * 0.5) return 'standard';
            return 'basic';
        })->countBy();
    }

    /**
     * Perform cohort analysis
     */
    private function performCohortAnalysis(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        // This is a simplified cohort analysis
        // In a real implementation, you'd want more sophisticated cohort tracking
        
        $monthlyNewClients = Client::where('company_id', $company->id)
            ->selectRaw('YEAR(created_at) as year, MONTH(created_at) as month, COUNT(*) as new_clients')
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();

        return [
            'monthly_cohorts' => $monthlyNewClients,
            'retention_by_cohort' => [], // Placeholder for detailed cohort retention analysis
        ];
    }

    /**
     * Analyze client churn
     */
    private function analyzeClientChurn(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $inactiveThreshold = now()->subDays(30);
        
        $churnedClients = Client::where('company_id', $company->id)
            ->whereDoesntHave('currencyExchanges', function ($query) use ($inactiveThreshold) {
                $query->where('exchange_date', '>', $inactiveThreshold);
            })
            ->whereHas('currencyExchanges') // Had transactions before
            ->count();

        $totalActiveClients = Client::where('company_id', $company->id)
            ->whereHas('currencyExchanges')
            ->count();

        return [
            'churned_clients' => $churnedClients,
            'churn_rate' => $totalActiveClients > 0 ? ($churnedClients / $totalActiveClients) * 100 : 0,
            'at_risk_clients' => $this->identifyAtRiskClients($company),
        ];
    }

    /**
     * Calculate loyalty metrics
     */
    private function calculateLoyaltyMetrics(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $loyalClients = Client::where('company_id', $company->id)
            ->whereHas('currencyExchanges', function ($query) use ($startDate, $endDate) {
                $query->whereBetween('exchange_date', [$startDate, $endDate]);
            }, '>=', 5) // At least 5 transactions
            ->count();

        $totalActiveClients = Client::where('company_id', $company->id)
            ->whereHas('currencyExchanges', function ($query) use ($startDate, $endDate) {
                $query->whereBetween('exchange_date', [$startDate, $endDate]);
            })
            ->count();

        return [
            'loyal_clients' => $loyalClients,
            'loyalty_rate' => $totalActiveClients > 0 ? ($loyalClients / $totalActiveClients) * 100 : 0,
        ];
    }

    /**
     * Calculate retention rate
     */
    private function calculateRetentionRate(Company $company, Carbon $startDate, Carbon $endDate): float
    {
        $previousPeriodStart = $startDate->copy()->subDays($startDate->diffInDays($endDate) + 1);
        $previousPeriodEnd = $startDate->copy()->subDay();

        $previousClients = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$previousPeriodStart, $previousPeriodEnd])
            ->distinct('client_id')
            ->pluck('client_id');

        $currentClients = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->distinct('client_id')
            ->pluck('client_id');

        $retainedClients = $previousClients->intersect($currentClients)->count();

        return $previousClients->count() > 0 ? ($retainedClients / $previousClients->count()) * 100 : 0;
    }

    /**
     * Analyze risk trends
     */
    private function analyzeRiskTrends(Company $company): array
    {
        // Placeholder for risk trend analysis
        return [
            'risk_score_trends' => [],
            'risk_level_changes' => [],
        ];
    }

    /**
     * Calculate growth rate
     */
    private function calculateGrowthRate(Company $company, Carbon $startDate, Carbon $endDate): float
    {
        $currentPeriodClients = Client::where('company_id', $company->id)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $previousPeriodStart = $startDate->copy()->subDays($startDate->diffInDays($endDate) + 1);
        $previousPeriodEnd = $startDate->copy()->subDay();

        $previousPeriodClients = Client::where('company_id', $company->id)
            ->whereBetween('created_at', [$previousPeriodStart, $previousPeriodEnd])
            ->count();

        if ($previousPeriodClients === 0) {
            return $currentPeriodClients > 0 ? 100 : 0;
        }

        return (($currentPeriodClients - $previousPeriodClients) / $previousPeriodClients) * 100;
    }

    /**
     * Analyze acquisition channels
     */
    private function analyzeAcquisitionChannels(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        // Placeholder - would need to track acquisition channels
        return [
            'referral' => 0,
            'direct' => 0,
            'marketing' => 0,
            'other' => 0,
        ];
    }

    /**
     * Identify at-risk clients
     */
    private function identifyAtRiskClients(Company $company): Collection
    {
        $riskThreshold = now()->subDays(14);
        
        return Client::where('company_id', $company->id)
            ->whereHas('currencyExchanges', function ($query) use ($riskThreshold) {
                $query->where('exchange_date', '<', $riskThreshold);
            })
            ->whereDoesntHave('currencyExchanges', function ($query) use ($riskThreshold) {
                $query->where('exchange_date', '>=', $riskThreshold);
            })
            ->with('profile')
            ->get();
    }
}
