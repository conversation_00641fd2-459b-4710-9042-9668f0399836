<?php

namespace Database\Seeders\ArenaDoviz;

use App\Models\ArenaDoviz\IranianBank;
use Illuminate\Database\Seeder;

class IranianBankSeeder extends Seeder
{
    /**
     * Seed Iranian banks for Arena Doviz operations.
     * 
     * Based on major Iranian banks that support international transactions
     * and currency exchange operations.
     */
    public function run(): void
    {
        $this->command->info('🏦 Seeding Iranian banks...');
        
        $banks = [
            [
                'name' => 'Bank Melli Iran',
                'persian_name' => 'بانک ملی ایران',
                'bank_code' => 'BMI',
                'swift_code' => 'MELIIRTHXXX',
                'central_bank_code' => '011',
                'bank_type' => 'government',
                'supports_international' => true,
                'supports_rial_exchange' => true,
                'contact_phone' => '+98 21 8888 0011',
                'website' => 'https://bmi.ir',
                'city' => 'Tehran',
                'province' => 'Tehran',
                'established_year' => 1928,
            ],
            
            [
                'name' => 'Bank Saderat Iran',
                'persian_name' => 'بانک صادرات ایران',
                'bank_code' => 'BSI',
                'swift_code' => 'BSIRIRTHXXX',
                'central_bank_code' => '013',
                'bank_type' => 'government',
                'supports_international' => true,
                'supports_rial_exchange' => true,
                'contact_phone' => '+98 21 8888 0013',
                'website' => 'https://bsi.ir',
                'city' => 'Tehran',
                'province' => 'Tehran',
                'established_year' => 1952,
            ],
            
            [
                'name' => 'Bank Tejarat',
                'persian_name' => 'بانک تجارت',
                'bank_code' => 'BTJ',
                'swift_code' => 'TEJAIRTXXX',
                'central_bank_code' => '018',
                'bank_type' => 'commercial',
                'supports_international' => true,
                'supports_rial_exchange' => true,
                'contact_phone' => '+98 21 8888 0018',
                'website' => 'https://tejaratbank.ir',
                'city' => 'Tehran',
                'province' => 'Tehran',
                'established_year' => 1979,
            ],
            
            [
                'name' => 'Bank Mellat',
                'persian_name' => 'بانک ملت',
                'bank_code' => 'BML',
                'swift_code' => 'MELATIRTHXXX',
                'central_bank_code' => '012',
                'bank_type' => 'private',
                'supports_international' => true,
                'supports_rial_exchange' => true,
                'contact_phone' => '+98 21 8888 0012',
                'website' => 'https://bankmellat.ir',
                'city' => 'Tehran',
                'province' => 'Tehran',
                'established_year' => 1980,
            ],
            
            [
                'name' => 'Bank Parsian',
                'persian_name' => 'بانک پارسیان',
                'bank_code' => 'BPS',
                'swift_code' => 'PARSIRTHXXX',
                'central_bank_code' => '054',
                'bank_type' => 'private',
                'supports_international' => true,
                'supports_rial_exchange' => true,
                'contact_phone' => '+98 21 8888 0054',
                'website' => 'https://parsian-bank.ir',
                'city' => 'Tehran',
                'province' => 'Tehran',
                'established_year' => 2001,
            ],
            
            [
                'name' => 'Bank Pasargad',
                'persian_name' => 'بانک پاسارگاد',
                'bank_code' => 'BPG',
                'swift_code' => 'PASAIRTHXXX',
                'central_bank_code' => '057',
                'bank_type' => 'private',
                'supports_international' => true,
                'supports_rial_exchange' => true,
                'contact_phone' => '+98 21 8888 0057',
                'website' => 'https://bpi.ir',
                'city' => 'Tehran',
                'province' => 'Tehran',
                'established_year' => 2005,
            ],
            
            [
                'name' => 'Bank Saman',
                'persian_name' => 'بانک سامان',
                'bank_code' => 'BSM',
                'swift_code' => 'SAMAIRTXXX',
                'central_bank_code' => '056',
                'bank_type' => 'private',
                'supports_international' => true,
                'supports_rial_exchange' => true,
                'contact_phone' => '+98 21 8888 0056',
                'website' => 'https://sb24.com',
                'city' => 'Tehran',
                'province' => 'Tehran',
                'established_year' => 2001,
            ],
            
            [
                'name' => 'Bank Refah Kargaran',
                'persian_name' => 'بانک رفاه کارگران',
                'bank_code' => 'BRK',
                'swift_code' => 'REFAIRTXXX',
                'central_bank_code' => '014',
                'bank_type' => 'specialized',
                'supports_international' => false,
                'supports_rial_exchange' => true,
                'contact_phone' => '+98 21 8888 0014',
                'website' => 'https://refah-bank.ir',
                'city' => 'Tehran',
                'province' => 'Tehran',
                'established_year' => 1960,
            ],
            
            [
                'name' => 'Bank Keshavarzi',
                'persian_name' => 'بانک کشاورزی',
                'bank_code' => 'BKS',
                'swift_code' => 'KESAIRTXXX',
                'central_bank_code' => '016',
                'bank_type' => 'specialized',
                'supports_international' => false,
                'supports_rial_exchange' => true,
                'contact_phone' => '+98 21 8888 0016',
                'website' => 'https://agri-bank.com',
                'city' => 'Tehran',
                'province' => 'Tehran',
                'established_year' => 1933,
            ],
            
            [
                'name' => 'Bank Sina',
                'persian_name' => 'بانک سینا',
                'bank_code' => 'BSN',
                'swift_code' => 'SINAIRTXXX',
                'central_bank_code' => '058',
                'bank_type' => 'private',
                'supports_international' => true,
                'supports_rial_exchange' => true,
                'contact_phone' => '+98 21 8888 0058',
                'website' => 'https://sinabank.ir',
                'city' => 'Tehran',
                'province' => 'Tehran',
                'established_year' => 2007,
            ],
        ];
        
        foreach ($banks as $bankData) {
            IranianBank::updateOrCreate(
                ['bank_code' => $bankData['bank_code']],
                array_merge($bankData, [
                    'company_id' => 1,
                    'is_active' => true,
                    'created_by' => 1,
                ])
            );
            
            $this->command->info("Created: {$bankData['name']} ({$bankData['persian_name']})");
        }
        
        $this->command->info('✅ Iranian banks seeded successfully!');
    }
}
