<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('currency_exchanges', function (Blueprint $table) {
            $table->foreignId('combined_exchange_id')
                ->nullable()
                ->after('iranian_bank_id')
                ->constrained('combined_exchanges')
                ->nullOnDelete();
                
            $table->index('combined_exchange_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('currency_exchanges', function (Blueprint $table) {
            $table->dropForeign(['combined_exchange_id']);
            $table->dropIndex(['combined_exchange_id']);
            $table->dropColumn('combined_exchange_id');
        });
    }
};
