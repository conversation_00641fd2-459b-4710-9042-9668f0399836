<?php

namespace App\Filament\Company\Widgets;

use App\Services\ArenaDoviz\DashboardAnalyticsService;
use Carbon\Carbon;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Number;

class ArenaDovizOverviewWidget extends BaseWidget
{
    protected static ?string $pollingInterval = '30s';
    protected static bool $isLazy = false;
    protected int | string | array $columnSpan = 'full';

    private function getAnalyticsService(): DashboardAnalyticsService
    {
        return app(DashboardAnalyticsService::class);
    }

    protected function getStats(): array
    {
        $company = auth()->user()->currentCompany;
        $startDate = now()->startOfMonth();
        $endDate = now()->endOfDay();

        $metrics = $this->getAnalyticsService()->getDashboardMetrics($company, $startDate, $endDate);
        $overview = $metrics['overview'];

        return [
            Stat::make('Total Transactions', Number::format($overview['total_transactions']))
                ->description($this->getGrowthDescription($overview['growth_rates']['transactions']))
                ->descriptionIcon($this->getGrowthIcon($overview['growth_rates']['transactions']))
                ->color($this->getGrowthColor($overview['growth_rates']['transactions']))
                ->chart($this->getTransactionChart()),

            Stat::make('Total Volume', Number::currency($overview['total_volume'], 'TRY'))
                ->description($this->getGrowthDescription($overview['growth_rates']['volume']))
                ->descriptionIcon($this->getGrowthIcon($overview['growth_rates']['volume']))
                ->color($this->getGrowthColor($overview['growth_rates']['volume']))
                ->chart($this->getVolumeChart()),

            Stat::make('Commission Revenue', Number::currency($overview['total_commission'], 'TRY'))
                ->description('This month')
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color('success')
                ->chart($this->getRevenueChart()),

            Stat::make('Active Clients', Number::format($overview['active_clients']))
                ->description('Unique clients this month')
                ->descriptionIcon('heroicon-m-users')
                ->color('info'),

            Stat::make('Avg Transaction Size', Number::currency($overview['avg_transaction_size'], 'TRY'))
                ->description('Per transaction')
                ->descriptionIcon('heroicon-m-calculator')
                ->color('warning'),

            Stat::make('Completion Rate', Number::percentage($metrics['transactions']['completion_rate'], 1))
                ->description('Transaction success rate')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color($metrics['transactions']['completion_rate'] >= 95 ? 'success' : 'warning'),
        ];
    }

    private function getGrowthDescription(float $growthRate): string
    {
        $rate = abs($growthRate);
        $direction = $growthRate >= 0 ? 'increase' : 'decrease';
        
        return Number::percentage($rate, 1) . " {$direction} from last period";
    }

    private function getGrowthIcon(float $growthRate): string
    {
        return $growthRate >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down';
    }

    private function getGrowthColor(float $growthRate): string
    {
        if ($growthRate > 10) return 'success';
        if ($growthRate > 0) return 'info';
        if ($growthRate > -10) return 'warning';
        return 'danger';
    }

    private function getTransactionChart(): array
    {
        $company = auth()->user()->currentCompany;
        $startDate = now()->subDays(7);
        $endDate = now();

        $metrics = $this->getAnalyticsService()->getDashboardMetrics($company, $startDate, $endDate);

        return $metrics['trends']['daily']->pluck('transaction_count')->toArray();
    }

    private function getVolumeChart(): array
    {
        $company = auth()->user()->currentCompany;
        $startDate = now()->subDays(7);
        $endDate = now();

        $metrics = $this->getAnalyticsService()->getDashboardMetrics($company, $startDate, $endDate);

        return $metrics['trends']['daily']->pluck('volume')->toArray();
    }

    private function getRevenueChart(): array
    {
        $company = auth()->user()->currentCompany;
        $startDate = now()->subDays(7);
        $endDate = now();

        $metrics = $this->getAnalyticsService()->getDashboardMetrics($company, $startDate, $endDate);

        return $metrics['trends']['daily']->pluck('revenue')->toArray();
    }
}
