<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Only create if table doesn't exist (<PERSON><PERSON><PERSON><PERSON> might have created it)
        if (!Schema::hasTable('locations')) {
            Schema::create('locations', function (Blueprint $table) {
                $table->id();
                $table->foreignId('company_id')->nullable()->constrained()->cascadeOnDelete();
                
                // Basic location information
                $table->string('name', 100);
                $table->string('code', 10)->unique();
                $table->text('address')->nullable();
                $table->string('timezone', 50)->default('UTC');
                $table->string('country_code', 2)->nullable();
                $table->string('currency_code', 3)->nullable();
                
                // Status and configuration
                $table->boolean('is_main_office')->default(false);
                $table->boolean('is_active')->default(true);
                
                // Contact information
                $table->string('phone', 20)->nullable();
                $table->string('email', 100)->nullable();
                $table->string('manager_name', 100)->nullable();
                $table->json('working_hours')->nullable();
                
                // Audit fields
                $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
                $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
                $table->timestamps();
                $table->softDeletes();
                
                // Indexes
                $table->index('code');
                $table->index('is_active');
                $table->index('country_code');
                $table->index('is_main_office');
            });
        } else {
            // If table exists, ensure it has the required columns
            Schema::table('locations', function (Blueprint $table) {
                if (!Schema::hasColumn('locations', 'company_id')) {
                    $table->foreignId('company_id')->nullable()->constrained()->cascadeOnDelete();
                }
                if (!Schema::hasColumn('locations', 'created_by')) {
                    $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
                }
                if (!Schema::hasColumn('locations', 'updated_by')) {
                    $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
                }
                if (!Schema::hasColumn('locations', 'deleted_at')) {
                    $table->softDeletes();
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('locations');
    }
};
