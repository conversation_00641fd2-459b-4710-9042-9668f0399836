<x-filament::section>
    <x-slot name="heading">
        Cash Flow Statement
    </x-slot>
    
    @if(isset($data['error']))
        <div class="text-center py-8">
            <div class="text-red-500 text-lg font-semibold">Error generating cash flow statement</div>
            <div class="text-gray-600 mt-2">{{ $data['error'] }}</div>
        </div>
    @elseif(isset($data['operating_activities']) || isset($data['investing_activities']) || isset($data['financing_activities']))
        <div class="space-y-6">
            <!-- Operating Activities Section -->
            @if(isset($data['operating_activities']))
                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Operating Activities</h3>
                    </div>
                    <div class="p-6">
                        <div class="overflow-x-auto">
                            <table class="w-full text-sm">
                                <thead>
                                    <tr class="border-b border-gray-200 dark:border-gray-700">
                                        <th class="text-left py-2 font-medium text-gray-900 dark:text-white">Description</th>
                                        <th class="text-right py-2 font-medium text-gray-900 dark:text-white">Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($data['operating_activities'] as $activity)
                                        <tr class="border-b border-gray-100 dark:border-gray-700">
                                            <td class="py-2 text-gray-700 dark:text-gray-300">{{ $activity['description'] ?? 'Unknown' }}</td>
                                            <td class="py-2 text-right text-gray-900 dark:text-white font-mono">
                                                {{ number_format($activity['amount'] ?? 0, 2) }}
                                            </td>
                                        </tr>
                                    @endforeach
                                    <tr class="border-t-2 border-gray-300 dark:border-gray-600 font-semibold">
                                        <td class="py-2 text-gray-900 dark:text-white">Net Cash from Operating Activities</td>
                                        <td class="py-2 text-right text-gray-900 dark:text-white font-mono">
                                            {{ number_format($data['net_operating_cash'] ?? 0, 2) }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Investing Activities Section -->
            @if(isset($data['investing_activities']))
                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Investing Activities</h3>
                    </div>
                    <div class="p-6">
                        <div class="overflow-x-auto">
                            <table class="w-full text-sm">
                                <thead>
                                    <tr class="border-b border-gray-200 dark:border-gray-700">
                                        <th class="text-left py-2 font-medium text-gray-900 dark:text-white">Description</th>
                                        <th class="text-right py-2 font-medium text-gray-900 dark:text-white">Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($data['investing_activities'] as $activity)
                                        <tr class="border-b border-gray-100 dark:border-gray-700">
                                            <td class="py-2 text-gray-700 dark:text-gray-300">{{ $activity['description'] ?? 'Unknown' }}</td>
                                            <td class="py-2 text-right text-gray-900 dark:text-white font-mono">
                                                {{ number_format($activity['amount'] ?? 0, 2) }}
                                            </td>
                                        </tr>
                                    @endforeach
                                    <tr class="border-t-2 border-gray-300 dark:border-gray-600 font-semibold">
                                        <td class="py-2 text-gray-900 dark:text-white">Net Cash from Investing Activities</td>
                                        <td class="py-2 text-right text-gray-900 dark:text-white font-mono">
                                            {{ number_format($data['net_investing_cash'] ?? 0, 2) }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Financing Activities Section -->
            @if(isset($data['financing_activities']))
                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Financing Activities</h3>
                    </div>
                    <div class="p-6">
                        <div class="overflow-x-auto">
                            <table class="w-full text-sm">
                                <thead>
                                    <tr class="border-b border-gray-200 dark:border-gray-700">
                                        <th class="text-left py-2 font-medium text-gray-900 dark:text-white">Description</th>
                                        <th class="text-right py-2 font-medium text-gray-900 dark:text-white">Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($data['financing_activities'] as $activity)
                                        <tr class="border-b border-gray-100 dark:border-gray-700">
                                            <td class="py-2 text-gray-700 dark:text-gray-300">{{ $activity['description'] ?? 'Unknown' }}</td>
                                            <td class="py-2 text-right text-gray-900 dark:text-white font-mono">
                                                {{ number_format($activity['amount'] ?? 0, 2) }}
                                            </td>
                                        </tr>
                                    @endforeach
                                    <tr class="border-t-2 border-gray-300 dark:border-gray-600 font-semibold">
                                        <td class="py-2 text-gray-900 dark:text-white">Net Cash from Financing Activities</td>
                                        <td class="py-2 text-right text-gray-900 dark:text-white font-mono">
                                            {{ number_format($data['net_financing_cash'] ?? 0, 2) }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Summary -->
            <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800 p-6">
                <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-4">Cash Flow Summary</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-blue-700 dark:text-blue-300">Net Cash from Operating Activities:</span>
                        <span class="font-mono font-semibold text-blue-900 dark:text-blue-100">
                            {{ number_format($data['net_operating_cash'] ?? 0, 2) }}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-blue-700 dark:text-blue-300">Net Cash from Investing Activities:</span>
                        <span class="font-mono font-semibold text-blue-900 dark:text-blue-100">
                            {{ number_format($data['net_investing_cash'] ?? 0, 2) }}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-blue-700 dark:text-blue-300">Net Cash from Financing Activities:</span>
                        <span class="font-mono font-semibold text-blue-900 dark:text-blue-100">
                            {{ number_format($data['net_financing_cash'] ?? 0, 2) }}
                        </span>
                    </div>
                    <div class="border-t border-blue-200 dark:border-blue-700 pt-3 flex justify-between">
                        <span class="text-lg font-semibold text-blue-900 dark:text-blue-100">Net Change in Cash:</span>
                        <span class="text-lg font-mono font-bold text-blue-900 dark:text-blue-100">
                            {{ number_format($data['net_cash_change'] ?? 0, 2) }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    @else
        <div class="text-center py-8">
            <div class="text-gray-500 text-lg">No cash flow data available for the selected period</div>
        </div>
    @endif
</x-filament::section>
