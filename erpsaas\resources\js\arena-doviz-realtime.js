/**
 * Arena Doviz Real-time Features
 * Handles live currency rate updates and notifications
 */

class ArenaDovizRealtime {
    constructor() {
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.rateElements = new Map();
        this.lastRates = new Map();
        
        this.init();
    }

    init() {
        this.setupWebSocket();
        this.setupRateElements();
        this.setupNotifications();
        this.startHeartbeat();
    }

    setupWebSocket() {
        // For development, we'll use polling instead of WebSocket
        // In production, you would use Laravel Echo with Pusher or Socket.io
        this.startPolling();
    }

    startPolling() {
        // Poll for rate updates every 30 seconds
        setInterval(() => {
            this.fetchLatestRates();
        }, 30000);

        // Initial fetch
        this.fetchLatestRates();
    }

    async fetchLatestRates() {
        try {
            const response = await fetch('/api/arena-doviz/rates/latest', {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.handleRateUpdates(data.rates);
                this.isConnected = true;
                this.reconnectAttempts = 0;
            }
        } catch (error) {
            console.error('Failed to fetch latest rates:', error);
            this.handleConnectionError();
        }
    }

    handleRateUpdates(rates) {
        rates.forEach(rate => {
            this.updateRateDisplay(rate);
            this.checkForSignificantChange(rate);
        });
    }

    updateRateDisplay(rate) {
        const elements = this.rateElements.get(rate.currency_code) || [];
        
        elements.forEach(element => {
            const oldValue = parseFloat(element.textContent.replace(/[^\d.-]/g, ''));
            const newValue = parseFloat(rate.buy_rate);
            
            // Update the display
            element.textContent = this.formatRate(newValue, rate.currency_code);
            
            // Add visual feedback for changes
            if (oldValue !== newValue) {
                this.animateRateChange(element, oldValue < newValue ? 'up' : 'down');
            }
        });

        // Update last known rate
        this.lastRates.set(rate.currency_code, rate);
    }

    animateRateChange(element, direction) {
        // Remove existing animation classes
        element.classList.remove('rate-up', 'rate-down', 'rate-pulse');
        
        // Add new animation class
        element.classList.add(`rate-${direction}`, 'rate-pulse');
        
        // Remove animation class after animation completes
        setTimeout(() => {
            element.classList.remove(`rate-${direction}`, 'rate-pulse');
        }, 2000);
    }

    checkForSignificantChange(rate) {
        const lastRate = this.lastRates.get(rate.currency_code);
        
        if (lastRate) {
            const changePercent = Math.abs((rate.buy_rate - lastRate.buy_rate) / lastRate.buy_rate) * 100;
            
            if (changePercent >= 1.0) { // 1% threshold
                this.showRateChangeNotification(rate, lastRate, changePercent);
            }
        }
    }

    showRateChangeNotification(newRate, oldRate, changePercent) {
        const direction = newRate.buy_rate > oldRate.buy_rate ? 'increased' : 'decreased';
        const icon = direction === 'increased' ? '📈' : '📉';
        
        const message = `${icon} ${newRate.currency_code} rate ${direction} by ${changePercent.toFixed(2)}%`;
        
        this.showNotification(message, 'rate-change');
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `arena-doviz-notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-message">${message}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        // Add to page
        const container = this.getNotificationContainer();
        container.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);

        // Animate in
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
    }

    getNotificationContainer() {
        let container = document.getElementById('arena-doviz-notifications');
        
        if (!container) {
            container = document.createElement('div');
            container.id = 'arena-doviz-notifications';
            container.className = 'arena-doviz-notifications-container';
            document.body.appendChild(container);
        }
        
        return container;
    }

    setupRateElements() {
        // Find all elements that display currency rates
        const rateElements = document.querySelectorAll('[data-currency-rate]');
        
        rateElements.forEach(element => {
            const currencyCode = element.getAttribute('data-currency-rate');
            
            if (!this.rateElements.has(currencyCode)) {
                this.rateElements.set(currencyCode, []);
            }
            
            this.rateElements.get(currencyCode).push(element);
        });
    }

    setupNotifications() {
        // Request notification permission
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }
    }

    startHeartbeat() {
        // Send heartbeat every 60 seconds to keep connection alive
        setInterval(() => {
            if (this.isConnected) {
                this.sendHeartbeat();
            }
        }, 60000);
    }

    sendHeartbeat() {
        // In a real WebSocket implementation, this would send a ping
        console.log('Heartbeat sent');
    }

    handleConnectionError() {
        this.isConnected = false;
        this.reconnectAttempts++;
        
        if (this.reconnectAttempts <= this.maxReconnectAttempts) {
            console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
            
            setTimeout(() => {
                this.fetchLatestRates();
            }, this.reconnectDelay * this.reconnectAttempts);
        } else {
            console.error('Max reconnection attempts reached');
            this.showNotification('Connection lost. Please refresh the page.', 'error');
        }
    }

    formatRate(rate, currencyCode) {
        // Use the existing NumberFormatter logic
        if (window.ArenaDovizFormatter) {
            return window.ArenaDovizFormatter.formatExchangeRate(rate);
        }
        
        // Fallback formatting
        const decimals = currencyCode.startsWith('IRR') ? 0 : 6;
        return new Intl.NumberFormat('en-US', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        }).format(rate);
    }

    // Public methods for manual control
    refreshRates() {
        this.fetchLatestRates();
    }

    disconnect() {
        this.isConnected = false;
        // Clear any intervals or connections
    }

    reconnect() {
        this.reconnectAttempts = 0;
        this.fetchLatestRates();
    }
}

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.arenaDovizRealtime = new ArenaDovizRealtime();
});

// Add CSS for notifications and animations
const style = document.createElement('style');
style.textContent = `
    .arena-doviz-notifications-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        max-width: 400px;
    }

    .arena-doviz-notification {
        background: #fff;
        border-left: 4px solid #3b82f6;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        margin-bottom: 10px;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
    }

    .arena-doviz-notification.show {
        opacity: 1;
        transform: translateX(0);
    }

    .arena-doviz-notification.notification-rate-change {
        border-left-color: #f59e0b;
    }

    .arena-doviz-notification.notification-error {
        border-left-color: #ef4444;
    }

    .notification-content {
        padding: 12px 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .notification-message {
        font-size: 14px;
        color: #374151;
    }

    .notification-close {
        background: none;
        border: none;
        font-size: 18px;
        color: #9ca3af;
        cursor: pointer;
        padding: 0;
        margin-left: 12px;
    }

    .notification-close:hover {
        color: #374151;
    }

    .rate-pulse {
        animation: ratePulse 0.6s ease-in-out;
    }

    .rate-up {
        color: #10b981 !important;
        font-weight: bold;
    }

    .rate-down {
        color: #ef4444 !important;
        font-weight: bold;
    }

    @keyframes ratePulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
`;
document.head.appendChild(style);
