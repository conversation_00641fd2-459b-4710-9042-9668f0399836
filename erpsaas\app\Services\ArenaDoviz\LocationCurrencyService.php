<?php

namespace App\Services\ArenaDoviz;

use App\Models\ArenaDoviz\Location;
use App\Models\ArenaDoviz\LocationCurrency;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

class LocationCurrencyService
{
    /**
     * Get all available location-based currency codes for dropdowns
     */
    public function getAvailableLocationCurrencies(): array
    {
        return Cache::remember('arena_location_currencies', 3600, function () {
            return LocationCurrency::active()
                ->with('location')
                ->orderBy('location_currency_code')
                ->get()
                ->mapWithKeys(function ($currency) {
                    return [
                        $currency->location_currency_code => $currency->display_name
                    ];
                })
                ->toArray();
        });
    }

    /**
     * Get currencies for a specific location
     */
    public function getCurrenciesForLocation(int $locationId): Collection
    {
        return LocationCurrency::forLocation($locationId)
            ->with('baseCurrency')
            ->orderBy('base_currency_code')
            ->get();
    }

    /**
     * Get current exchange rate between two location currencies
     */
    public function getExchangeRate(string $fromCurrencyCode, string $toCurrencyCode): ?float
    {
        $fromCurrency = LocationCurrency::where('location_currency_code', $fromCurrencyCode)->first();
        $toCurrency = LocationCurrency::where('location_currency_code', $toCurrencyCode)->first();

        if (!$fromCurrency || !$toCurrency) {
            return null;
        }

        // If same currency, rate is 1
        if ($fromCurrencyCode === $toCurrencyCode) {
            return 1.0;
        }

        // Calculate cross rate using mid rates
        if ($fromCurrency->mid_rate && $toCurrency->mid_rate) {
            return $toCurrency->mid_rate / $fromCurrency->mid_rate;
        }

        return null;
    }

    /**
     * Get buy and sell rates for a currency pair
     */
    public function getBuySellRates(string $fromCurrencyCode, string $toCurrencyCode): array
    {
        $fromCurrency = LocationCurrency::where('location_currency_code', $fromCurrencyCode)->first();
        $toCurrency = LocationCurrency::where('location_currency_code', $toCurrencyCode)->first();

        if (!$fromCurrency || !$toCurrency) {
            return ['buy_rate' => null, 'sell_rate' => null];
        }

        if ($fromCurrencyCode === $toCurrencyCode) {
            return ['buy_rate' => 1.0, 'sell_rate' => 1.0];
        }

        // For cross rates, we use the sell rate when buying and buy rate when selling
        // to ensure the exchange office always has a margin
        $buyRate = $fromCurrency->sell_rate && $toCurrency->buy_rate 
            ? $toCurrency->buy_rate / $fromCurrency->sell_rate 
            : null;
            
        $sellRate = $fromCurrency->buy_rate && $toCurrency->sell_rate 
            ? $toCurrency->sell_rate / $fromCurrency->buy_rate 
            : null;

        return [
            'buy_rate' => $buyRate ? round($buyRate, 6) : null,
            'sell_rate' => $sellRate ? round($sellRate, 6) : null,
        ];
    }

    /**
     * Update rates for a location currency
     */
    public function updateCurrencyRates(string $locationCurrencyCode, float $buyRate, float $sellRate): bool
    {
        $currency = LocationCurrency::where('location_currency_code', $locationCurrencyCode)->first();
        
        if (!$currency) {
            return false;
        }

        $currency->updateRates($buyRate, $sellRate);
        
        // Clear cache
        Cache::forget('arena_location_currencies');
        
        return true;
    }

    /**
     * Create a new location currency
     */
    public function createLocationCurrency(array $data): LocationCurrency
    {
        $locationCurrencyCode = LocationCurrency::generateLocationCurrencyCode(
            $data['base_currency_code'],
            $data['location_code']
        );

        $currency = LocationCurrency::create([
            'company_id' => $data['company_id'],
            'location_id' => $data['location_id'],
            'base_currency_code' => $data['base_currency_code'],
            'location_currency_code' => $locationCurrencyCode,
            'name' => $data['name'],
            'symbol' => $data['symbol'],
            'buy_rate' => $data['buy_rate'] ?? null,
            'sell_rate' => $data['sell_rate'] ?? null,
            'mid_rate' => $data['mid_rate'] ?? null,
            'rate_date' => now(),
            'is_active' => $data['is_active'] ?? true,
            'decimal_places' => $data['decimal_places'] ?? 2,
        ]);

        // Clear cache
        Cache::forget('arena_location_currencies');

        return $currency;
    }

    /**
     * Get currency rates widget data for dashboard
     */
    public function getCurrencyRatesWidget(): array
    {
        $majorCurrencies = ['USD_IST', 'EUR_IST', 'IRR_IST', 'AED_DXB'];
        
        return LocationCurrency::whereIn('location_currency_code', $majorCurrencies)
            ->with('location')
            ->where('is_active', true)
            ->get()
            ->map(function ($currency) {
                return [
                    'currency' => $currency->location_currency_code,
                    'name' => $currency->name,
                    'buy_rate' => number_format($currency->buy_rate, $currency->decimal_places),
                    'sell_rate' => number_format($currency->sell_rate, $currency->decimal_places),
                    'location' => $currency->location->name,
                    'updated_at' => $currency->rate_date?->format('H:i') ?? 'N/A',
                    'is_current' => $currency->hasCurrentRates(),
                ];
            })
            ->toArray();
    }

    /**
     * Parse location currency code to get base currency and location
     */
    public function parseLocationCurrencyCode(string $locationCurrencyCode): array
    {
        $parts = explode('_', $locationCurrencyCode);
        
        if (count($parts) !== 2) {
            return ['base_currency' => null, 'location_code' => null];
        }

        return [
            'base_currency' => $parts[0],
            'location_code' => $parts[1],
        ];
    }

    /**
     * Get all active locations
     */
    public function getActiveLocations(): Collection
    {
        return Location::active()->orderBy('name')->get();
    }

    /**
     * Format amount with proper currency symbol and decimal places
     */
    public function formatAmount(float $amount, string $locationCurrencyCode): string
    {
        $currency = LocationCurrency::where('location_currency_code', $locationCurrencyCode)->first();
        
        if (!$currency) {
            return number_format($amount, 2);
        }

        return number_format($amount, $currency->decimal_places) . ' ' . $currency->symbol;
    }
}
