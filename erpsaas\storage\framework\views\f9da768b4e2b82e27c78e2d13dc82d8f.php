<?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('heading', null, []); ?> 
        Balance Sheet Report
     <?php $__env->endSlot(); ?>
    
    <!--[if BLOCK]><![endif]--><?php if(isset($data['error'])): ?>
        <div class="text-center py-8">
            <div class="text-red-500 text-lg font-semibold">Error generating balance sheet</div>
            <div class="text-gray-600 mt-2"><?php echo e($data['error']); ?></div>
        </div>
    <?php elseif(isset($data['assets']) || isset($data['liabilities']) || isset($data['equity'])): ?>
        <div class="space-y-6">
            <!-- Assets Section -->
            <!--[if BLOCK]><![endif]--><?php if(isset($data['assets'])): ?>
                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Assets</h3>
                    </div>
                    <div class="p-6">
                        <div class="overflow-x-auto">
                            <table class="w-full text-sm">
                                <thead>
                                    <tr class="border-b border-gray-200 dark:border-gray-700">
                                        <th class="text-left py-2 font-medium text-gray-900 dark:text-white">Account</th>
                                        <th class="text-right py-2 font-medium text-gray-900 dark:text-white">Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $data['assets']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $asset): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr class="border-b border-gray-100 dark:border-gray-700">
                                            <td class="py-2 text-gray-700 dark:text-gray-300"><?php echo e($asset['name'] ?? 'Unknown'); ?></td>
                                            <td class="py-2 text-right text-gray-900 dark:text-white font-mono">
                                                <?php echo e(number_format($asset['amount'] ?? 0, 2)); ?>

                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                    <tr class="border-t-2 border-gray-300 dark:border-gray-600 font-semibold">
                                        <td class="py-2 text-gray-900 dark:text-white">Total Assets</td>
                                        <td class="py-2 text-right text-gray-900 dark:text-white font-mono">
                                            <?php echo e(number_format($data['total_assets'] ?? 0, 2)); ?>

                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

            <!-- Liabilities Section -->
            <!--[if BLOCK]><![endif]--><?php if(isset($data['liabilities'])): ?>
                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Liabilities</h3>
                    </div>
                    <div class="p-6">
                        <div class="overflow-x-auto">
                            <table class="w-full text-sm">
                                <thead>
                                    <tr class="border-b border-gray-200 dark:border-gray-700">
                                        <th class="text-left py-2 font-medium text-gray-900 dark:text-white">Account</th>
                                        <th class="text-right py-2 font-medium text-gray-900 dark:text-white">Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $data['liabilities']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $liability): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr class="border-b border-gray-100 dark:border-gray-700">
                                            <td class="py-2 text-gray-700 dark:text-gray-300"><?php echo e($liability['name'] ?? 'Unknown'); ?></td>
                                            <td class="py-2 text-right text-gray-900 dark:text-white font-mono">
                                                <?php echo e(number_format($liability['amount'] ?? 0, 2)); ?>

                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                    <tr class="border-t-2 border-gray-300 dark:border-gray-600 font-semibold">
                                        <td class="py-2 text-gray-900 dark:text-white">Total Liabilities</td>
                                        <td class="py-2 text-right text-gray-900 dark:text-white font-mono">
                                            <?php echo e(number_format($data['total_liabilities'] ?? 0, 2)); ?>

                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

            <!-- Equity Section -->
            <!--[if BLOCK]><![endif]--><?php if(isset($data['equity'])): ?>
                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Equity</h3>
                    </div>
                    <div class="p-6">
                        <div class="overflow-x-auto">
                            <table class="w-full text-sm">
                                <thead>
                                    <tr class="border-b border-gray-200 dark:border-gray-700">
                                        <th class="text-left py-2 font-medium text-gray-900 dark:text-white">Account</th>
                                        <th class="text-right py-2 font-medium text-gray-900 dark:text-white">Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $data['equity']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $equity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr class="border-b border-gray-100 dark:border-gray-700">
                                            <td class="py-2 text-gray-700 dark:text-gray-300"><?php echo e($equity['name'] ?? 'Unknown'); ?></td>
                                            <td class="py-2 text-right text-gray-900 dark:text-white font-mono">
                                                <?php echo e(number_format($equity['amount'] ?? 0, 2)); ?>

                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                    <tr class="border-t-2 border-gray-300 dark:border-gray-600 font-semibold">
                                        <td class="py-2 text-gray-900 dark:text-white">Total Equity</td>
                                        <td class="py-2 text-right text-gray-900 dark:text-white font-mono">
                                            <?php echo e(number_format($data['total_equity'] ?? 0, 2)); ?>

                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

            <!-- Summary -->
            <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800 p-6">
                <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-4">Balance Sheet Summary</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                            <?php echo e(number_format($data['total_assets'] ?? 0, 2)); ?>

                        </div>
                        <div class="text-sm text-blue-700 dark:text-blue-300">Total Assets</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-red-600 dark:text-red-400">
                            <?php echo e(number_format($data['total_liabilities'] ?? 0, 2)); ?>

                        </div>
                        <div class="text-sm text-red-700 dark:text-red-300">Total Liabilities</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                            <?php echo e(number_format($data['total_equity'] ?? 0, 2)); ?>

                        </div>
                        <div class="text-sm text-green-700 dark:text-green-300">Total Equity</div>
                    </div>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="text-center py-8">
            <div class="text-gray-500 text-lg">No balance sheet data available for the selected period</div>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views/filament/company/reports/balance-sheet.blade.php ENDPATH**/ ?>