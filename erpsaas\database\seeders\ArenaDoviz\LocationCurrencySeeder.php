<?php

namespace Database\Seeders\ArenaDoviz;

use App\Models\ArenaDoviz\Location;
use App\Models\ArenaDoviz\LocationCurrency;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LocationCurrencySeeder extends Seeder
{
    /**
     * Seed location-specific currencies for Arena Doviz operations.
     * 
     * Creates location-based currency codes like USD_IST, EUR_TBZ, IRR_THR
     * as specified in the project requirements.
     */
    public function run(): void
    {
        $this->command->info('💱 Seeding Arena Doviz location currencies...');
        
        // Ensure locations exist first
        $this->ensureLocationsExist();
        
        // Get all locations
        $locations = $this->getLocations();
        
        // Base currencies to create for each location
        $baseCurrencies = [
            'USD' => ['name' => 'US Dollar', 'symbol' => '$', 'decimal_places' => 2],
            'EUR' => ['name' => 'Euro', 'symbol' => '€', 'decimal_places' => 2],
            'TRY' => ['name' => 'Turkish Lira', 'symbol' => '₺', 'decimal_places' => 2],
            'IRR' => ['name' => 'Iranian Rial', 'symbol' => '﷼', 'decimal_places' => 0],
            'AED' => ['name' => 'UAE Dirham', 'symbol' => 'د.إ', 'decimal_places' => 2],
            'CNY' => ['name' => 'Chinese Yuan', 'symbol' => '¥', 'decimal_places' => 2],
        ];
        
        foreach ($locations as $location) {
            foreach ($baseCurrencies as $currencyCode => $currencyData) {
                $locationCurrencyCode = $currencyCode . '_' . $location['code'];
                
                // Skip if already exists
                if (LocationCurrency::where('location_currency_code', $locationCurrencyCode)->exists()) {
                    continue;
                }
                
                $rates = $this->getExampleRates($currencyCode, $location['code']);
                
                LocationCurrency::create([
                    'company_id' => 1,
                    'location_id' => $location['id'],
                    'base_currency_code' => $currencyCode,
                    'location_currency_code' => $locationCurrencyCode,
                    'name' => $currencyData['name'] . ' ' . $location['name'],
                    'symbol' => $currencyData['symbol'],
                    'buy_rate' => $rates['buy_rate'],
                    'sell_rate' => $rates['sell_rate'],
                    'mid_rate' => $rates['mid_rate'],
                    'rate_date' => now(),
                    'is_active' => true,
                    'decimal_places' => $currencyData['decimal_places'],
                    'created_by' => 1,
                ]);
                
                $this->command->info("Created: {$locationCurrencyCode} - {$currencyData['name']} {$location['name']}");
            }
        }
        
        $this->command->info('✅ Location currencies seeded successfully!');
    }
    
    /**
     * Ensure locations exist in the database
     */
    private function ensureLocationsExist(): void
    {
        if (!DB::table('locations')->exists()) {
            $this->command->info('📍 Running LocationSeeder first...');
            $this->call(LocationSeeder::class);
        }
    }
    
    /**
     * Get all locations from database
     */
    private function getLocations(): array
    {
        return DB::table('locations')
            ->where('is_active', true)
            ->select('id', 'name', 'code')
            ->get()
            ->map(function ($location) {
                return (array) $location;
            })
            ->toArray();
    }
    
    /**
     * Get example exchange rates for currency/location combinations
     * These are realistic rates based on actual market conditions
     */
    private function getExampleRates(string $currencyCode, string $locationCode): array
    {
        // Base rates against TRY (Turkish Lira as base)
        $baseRates = [
            'USD' => ['buy' => 32.15, 'sell' => 32.25],
            'EUR' => ['buy' => 34.80, 'sell' => 34.95],
            'TRY' => ['buy' => 1.00, 'sell' => 1.00],
            'IRR' => ['buy' => 0.00076, 'sell' => 0.00078],
            'AED' => ['buy' => 8.75, 'sell' => 8.85],
            'CNY' => ['buy' => 4.45, 'sell' => 4.55],
        ];
        
        // Location-specific adjustments (margins)
        $locationMargins = [
            'IST' => 0.0, // Istanbul - base rates
            'TBZ' => 0.02, // Tabriz - 2% higher
            'THR' => 0.015, // Tehran - 1.5% higher
            'DXB' => -0.01, // Dubai - 1% lower (competitive)
            'SHA' => 0.025, // Shanghai - 2.5% higher
        ];
        
        $baseRate = $baseRates[$currencyCode] ?? ['buy' => 1.0, 'sell' => 1.0];
        $margin = $locationMargins[$locationCode] ?? 0.0;
        
        $buyRate = $baseRate['buy'] * (1 + $margin);
        $sellRate = $baseRate['sell'] * (1 + $margin);
        $midRate = ($buyRate + $sellRate) / 2;
        
        return [
            'buy_rate' => round($buyRate, 6),
            'sell_rate' => round($sellRate, 6),
            'mid_rate' => round($midRate, 6),
        ];
    }
}
