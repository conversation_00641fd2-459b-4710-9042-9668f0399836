<?php

namespace Tests\Unit\ArenaDoviz;

use App\Helpers\ArenaDoviz\NumberFormatter;
use PHPUnit\Framework\TestCase;

class NumberFormatterTest extends TestCase
{
    public function test_format_number_with_default_parameters()
    {
        $result = NumberFormatter::formatNumber(1234567.89);
        $this->assertEquals('1,234,567.89', $result);
    }

    public function test_format_number_with_custom_decimals()
    {
        $result = NumberFormatter::formatNumber(1234567.89, 4);
        $this->assertEquals('1,234,567.8900', $result);
    }

    public function test_format_number_with_zero_decimals()
    {
        $result = NumberFormatter::formatNumber(1234567.89, 0);
        $this->assertEquals('1,234,568', $result);
    }

    public function test_format_number_with_null_value()
    {
        $result = NumberFormatter::formatNumber(null);
        $this->assertEquals('', $result);
    }

    public function test_format_number_with_empty_string()
    {
        $result = NumberFormatter::formatNumber('');
        $this->assertEquals('', $result);
    }

    public function test_format_currency_with_usd()
    {
        $result = NumberFormatter::formatCurrency(1234.56, 'USD');
        $this->assertEquals('1,234.56 $', $result);
    }

    public function test_format_currency_with_eur()
    {
        $result = NumberFormatter::formatCurrency(1234.56, 'EUR');
        $this->assertEquals('1,234.56 €', $result);
    }

    public function test_format_currency_with_try()
    {
        $result = NumberFormatter::formatCurrency(1234.56, 'TRY');
        $this->assertEquals('1,234.56 ₺', $result);
    }

    public function test_format_currency_with_irr()
    {
        $result = NumberFormatter::formatCurrency(1234567, 'IRR');
        $this->assertEquals('1,234,567.00 ﷼', $result);
    }

    public function test_format_currency_with_location_code()
    {
        $result = NumberFormatter::formatCurrency(1234.56, 'USD_IST');
        $this->assertEquals('1,234.56 $', $result);
    }

    public function test_parse_number_with_formatted_string()
    {
        $result = NumberFormatter::parseNumber('1,234,567.89');
        $this->assertEquals(1234567.89, $result);
    }

    public function test_parse_number_with_unformatted_string()
    {
        $result = NumberFormatter::parseNumber('1234567.89');
        $this->assertEquals(1234567.89, $result);
    }

    public function test_parse_number_with_empty_string()
    {
        $result = NumberFormatter::parseNumber('');
        $this->assertEquals(0.0, $result);
    }

    public function test_extract_base_currency()
    {
        $result = NumberFormatter::extractBaseCurrency('USD_IST');
        $this->assertEquals('USD', $result);
    }

    public function test_extract_base_currency_without_location()
    {
        $result = NumberFormatter::extractBaseCurrency('EUR');
        $this->assertEquals('EUR', $result);
    }

    public function test_get_currency_symbol()
    {
        $this->assertEquals('$', NumberFormatter::getCurrencySymbol('USD'));
        $this->assertEquals('€', NumberFormatter::getCurrencySymbol('EUR'));
        $this->assertEquals('₺', NumberFormatter::getCurrencySymbol('TRY'));
        $this->assertEquals('﷼', NumberFormatter::getCurrencySymbol('IRR'));
    }

    public function test_format_exchange_rate()
    {
        $result = NumberFormatter::formatExchangeRate(32.123456);
        $this->assertEquals('32.123456', $result);
    }

    public function test_format_percentage()
    {
        $result = NumberFormatter::formatPercentage(12.34);
        $this->assertEquals('12.34%', $result);
    }

    public function test_format_iranian_rial()
    {
        $result = NumberFormatter::formatIranianRial(1234567);
        $this->assertEquals('1,234,567 ﷼', $result);
    }

    public function test_format_by_currency_with_irr()
    {
        $result = NumberFormatter::formatByCurrency(1234567, 'IRR_TBZ');
        $this->assertEquals('1,234,567 ﷼', $result);
    }

    public function test_format_by_currency_with_usd()
    {
        $result = NumberFormatter::formatByCurrency(1234.56, 'USD_IST');
        $this->assertEquals('1,234.56 $', $result);
    }

    public function test_get_decimal_places_for_irr()
    {
        $result = NumberFormatter::getDecimalPlaces('IRR_TBZ');
        $this->assertEquals(0, $result);
    }

    public function test_get_decimal_places_for_usd()
    {
        $result = NumberFormatter::getDecimalPlaces('USD_IST');
        $this->assertEquals(2, $result);
    }

    public function test_format_for_input()
    {
        $result = NumberFormatter::formatForInput(1234.56, 'USD_IST');
        
        $this->assertIsArray($result);
        $this->assertEquals('1,234.56', $result['value']);
        $this->assertEquals('USD_IST', $result['currency']);
        $this->assertEquals(2, $result['decimals']);
        $this->assertEquals('$', $result['symbol']);
    }

    public function test_is_valid_formatted_number()
    {
        $this->assertTrue(NumberFormatter::isValidFormattedNumber('1,234,567.89'));
        $this->assertTrue(NumberFormatter::isValidFormattedNumber('123.45'));
        $this->assertTrue(NumberFormatter::isValidFormattedNumber('1,000'));
        $this->assertFalse(NumberFormatter::isValidFormattedNumber('1,23,456.78')); // Invalid comma placement
        $this->assertFalse(NumberFormatter::isValidFormattedNumber('abc'));
        $this->assertFalse(NumberFormatter::isValidFormattedNumber('1,234.56.78')); // Multiple decimal points
    }

    public function test_format_compact_number()
    {
        $this->assertEquals('1.2K', NumberFormatter::formatCompactNumber(1234));
        $this->assertEquals('1.2M', NumberFormatter::formatCompactNumber(1234567));
        $this->assertEquals('1.2B', NumberFormatter::formatCompactNumber(1234567890));
        $this->assertEquals('123.0', NumberFormatter::formatCompactNumber(123));
    }
}
