<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Analysis Form -->
        <x-filament::section>
            <x-slot name="heading">
                Analysis Parameters
            </x-slot>
            
            {{ $this->form }}
            
            <div class="mt-4">
                {{ $this->getAction('analyze') }}
                {{ $this->getAction('export') }}
            </div>
        </x-filament::section>

        @if($profitAnalysis)
            <!-- Stats Overview -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                @foreach($this->getStatsOverview() as $stat)
                    <x-filament::card>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-gray-900 dark:text-white">
                                {{ $stat->getValue() }}
                            </div>
                            <div class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                {{ $stat->getLabel() }}
                            </div>
                            @if($stat->getDescription())
                                <div class="text-xs text-gray-400 dark:text-gray-500 mt-1">
                                    {{ $stat->getDescription() }}
                                </div>
                            @endif
                        </div>
                    </x-filament::card>
                @endforeach
            </div>

            <!-- Transaction Summaries -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Buy Transactions -->
                <x-filament::section>
                    <x-slot name="heading">
                        Buy Transactions Summary
                    </x-slot>
                    
                    <div class="space-y-3">
                        @foreach([
                            ['label' => 'Transaction Count', 'value' => number_format($profitAnalysis['buy_summary']['transaction_count'])],
                            ['label' => 'Total Amount', 'value' => $profitAnalysis['buy_summary']['formatted_total_amount']],
                            ['label' => 'Total Value', 'value' => $profitAnalysis['buy_summary']['formatted_total_value']],
                            ['label' => 'Total Commission', 'value' => $profitAnalysis['buy_summary']['formatted_commission']],
                            ['label' => 'Average Rate', 'value' => $profitAnalysis['buy_summary']['formatted_avg_rate']],
                        ] as $item)
                            <div class="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700">
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ $item['label'] }}</span>
                                <span class="text-sm text-gray-900 dark:text-white font-mono">{{ $item['value'] }}</span>
                            </div>
                        @endforeach
                    </div>
                </x-filament::section>

                <!-- Sell Transactions -->
                <x-filament::section>
                    <x-slot name="heading">
                        Sell Transactions Summary
                    </x-slot>
                    
                    <div class="space-y-3">
                        @foreach([
                            ['label' => 'Transaction Count', 'value' => number_format($profitAnalysis['sell_summary']['transaction_count'])],
                            ['label' => 'Total Amount', 'value' => $profitAnalysis['sell_summary']['formatted_total_amount']],
                            ['label' => 'Total Value', 'value' => $profitAnalysis['sell_summary']['formatted_total_value']],
                            ['label' => 'Total Commission', 'value' => $profitAnalysis['sell_summary']['formatted_commission']],
                            ['label' => 'Average Rate', 'value' => $profitAnalysis['sell_summary']['formatted_avg_rate']],
                        ] as $item)
                            <div class="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700">
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ $item['label'] }}</span>
                                <span class="text-sm text-gray-900 dark:text-white font-mono">{{ $item['value'] }}</span>
                            </div>
                        @endforeach
                    </div>
                </x-filament::section>
            </div>

            <!-- Profit Analysis -->
            <x-filament::section>
                <x-slot name="heading">
                    Profit Analysis
                </x-slot>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    @php $analysis = $profitAnalysis['profit_analysis']; @endphp
                    
                    <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                        <h4 class="font-semibold text-blue-900 dark:text-blue-100">Volume Analysis</h4>
                        <div class="mt-2 space-y-1 text-sm">
                            <div>Buy Volume: {{ number_format($analysis['buy_volume'], 2) }}</div>
                            <div>Sell Volume: {{ number_format($analysis['sell_volume'], 2) }}</div>
                            <div>Matched Volume: {{ number_format($analysis['matched_volume'], 2) }}</div>
                        </div>
                    </div>
                    
                    <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                        <h4 class="font-semibold text-green-900 dark:text-green-100">Rate Analysis</h4>
                        <div class="mt-2 space-y-1 text-sm">
                            <div>Buy Rate: {{ $analysis['formatted_spread'] }}</div>
                            <div>Rate Spread: {{ number_format($analysis['rate_spread_percent'], 2) }}%</div>
                        </div>
                    </div>
                    
                    <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                        <h4 class="font-semibold text-purple-900 dark:text-purple-100">Profit Estimate</h4>
                        <div class="mt-2 space-y-1 text-sm">
                            <div>Theoretical: {{ number_format($analysis['theoretical_profit'], 2) }}</div>
                            <div>Commission: {{ number_format($analysis['commission_profit'], 2) }}</div>
                            <div class="font-semibold">Total: {{ $analysis['formatted_profit'] }}</div>
                        </div>
                    </div>
                </div>
            </x-filament::section>

            <!-- Recommendations -->
            @if(!empty($profitAnalysis['recommendations']))
                <x-filament::section>
                    <x-slot name="heading">
                        Recommendations
                    </x-slot>
                    
                    <div class="space-y-3">
                        @foreach($profitAnalysis['recommendations'] as $recommendation)
                            <div class="p-4 rounded-lg border-l-4 @if($recommendation['priority'] === 'critical') border-red-500 bg-red-50 dark:bg-red-900/20 @elseif($recommendation['priority'] === 'high') border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20 @else border-blue-500 bg-blue-50 dark:bg-blue-900/20 @endif">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        @if($recommendation['priority'] === 'critical')
                                            <x-heroicon-s-exclamation-triangle class="h-5 w-5 text-red-500" />
                                        @elseif($recommendation['priority'] === 'high')
                                            <x-heroicon-s-exclamation-circle class="h-5 w-5 text-yellow-500" />
                                        @else
                                            <x-heroicon-s-information-circle class="h-5 w-5 text-blue-500" />
                                        @endif
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium @if($recommendation['priority'] === 'critical') text-red-800 dark:text-red-200 @elseif($recommendation['priority'] === 'high') text-yellow-800 dark:text-yellow-200 @else text-blue-800 dark:text-blue-200 @endif">
                                            {{ ucfirst($recommendation['priority']) }} Priority
                                        </p>
                                        <p class="mt-1 text-sm @if($recommendation['priority'] === 'critical') text-red-700 dark:text-red-300 @elseif($recommendation['priority'] === 'high') text-yellow-700 dark:text-yellow-300 @else text-blue-700 dark:text-blue-300 @endif">
                                            {{ $recommendation['message'] }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </x-filament::section>
            @endif

            <!-- Top Currency Pairs -->
            @if($topPairs)
                <x-filament::section>
                    <x-slot name="heading">
                        Top Performing Currency Pairs
                    </x-slot>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-800">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Currency Pair</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Commission</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Transactions</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Volume</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Avg Rate</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                                @foreach($topPairs as $pair)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{{ $pair['currency_pair'] }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white font-mono">{{ $pair['formatted_commission'] }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">{{ number_format($pair['transaction_count']) }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white font-mono">{{ $pair['formatted_volume'] }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white font-mono">{{ $pair['formatted_rate'] }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </x-filament::section>
            @endif
        @else
            <x-filament::section>
                <div class="text-center py-12">
                    <x-heroicon-o-chart-bar class="mx-auto h-12 w-12 text-gray-400" />
                    <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No Analysis Yet</h3>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Click "Run Analysis" to generate profit analysis report.</p>
                </div>
            </x-filament::section>
        @endif
    </div>
</x-filament-panels::page>
