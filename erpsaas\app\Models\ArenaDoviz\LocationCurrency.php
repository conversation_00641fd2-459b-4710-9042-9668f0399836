<?php

namespace App\Models\ArenaDoviz;

use App\Concerns\Blamable;
use App\Concerns\CompanyOwned;
use App\Models\Setting\Currency;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class LocationCurrency extends Model
{
    use Blamable;
    use CompanyOwned;
    use HasFactory;
    use SoftDeletes;

    protected $table = 'location_currencies';

    protected $fillable = [
        'company_id',
        'location_id',
        'base_currency_code',
        'location_currency_code', // e.g., USD_IST, EUR_TBZ
        'name',
        'symbol',
        'buy_rate',
        'sell_rate',
        'mid_rate',
        'rate_date',
        'last_updated',
        'is_active',
        'decimal_places',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'buy_rate' => 'decimal:6',
        'sell_rate' => 'decimal:6',
        'mid_rate' => 'decimal:6',
        'rate_date' => 'datetime',
        'last_updated' => 'datetime',
        'is_active' => 'boolean',
        'decimal_places' => 'integer',
    ];

    /**
     * Get the location this currency belongs to
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    /**
     * Get the base currency
     */
    public function baseCurrency(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'base_currency_code', 'code');
    }

    /**
     * Generate location-specific currency code
     */
    public static function generateLocationCurrencyCode(string $baseCurrencyCode, string $locationCode): string
    {
        return strtoupper($baseCurrencyCode . '_' . $locationCode);
    }

    /**
     * Get formatted display name
     */
    public function getDisplayNameAttribute(): string
    {
        return "{$this->location_currency_code} - {$this->name}";
    }

    /**
     * Get formatted buy rate with currency symbol
     */
    public function getFormattedBuyRateAttribute(): string
    {
        return number_format($this->buy_rate, $this->decimal_places) . ' ' . $this->symbol;
    }

    /**
     * Get formatted sell rate with currency symbol
     */
    public function getFormattedSellRateAttribute(): string
    {
        return number_format($this->sell_rate, $this->decimal_places) . ' ' . $this->symbol;
    }

    /**
     * Check if rates are current (within last 24 hours)
     */
    public function hasCurrentRates(): bool
    {
        return $this->rate_date && $this->rate_date->isAfter(now()->subDay());
    }

    /**
     * Get active location currencies
     */
    public static function active()
    {
        return static::where('is_active', true);
    }

    /**
     * Get currencies for a specific location
     */
    public static function forLocation(int $locationId)
    {
        return static::where('location_id', $locationId)->active();
    }

    /**
     * Get all available location currency codes for dropdowns
     */
    public static function getAvailableCurrencyCodes(): array
    {
        return static::active()
            ->orderBy('location_currency_code')
            ->pluck('name', 'location_currency_code')
            ->toArray();
    }

    /**
     * Update exchange rates for this location currency
     */
    public function updateRates(float $buyRate, float $sellRate, ?float $midRate = null): void
    {
        $this->update([
            'buy_rate' => $buyRate,
            'sell_rate' => $sellRate,
            'mid_rate' => $midRate ?? (($buyRate + $sellRate) / 2),
            'rate_date' => now(),
        ]);
    }
}
