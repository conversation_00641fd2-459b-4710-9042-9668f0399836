<?php

namespace App\Services\ArenaDoviz;

use App\Events\ArenaDoviz\CurrencyRateUpdated;
use App\Models\ArenaDoviz\LocationCurrency;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class RealTimeRateService
{
    /**
     * External API endpoints for currency rates
     */
    private const API_ENDPOINTS = [
        'exchangerate_api' => 'https://api.exchangerate-api.com/v4/latest/',
        'fixer_io' => 'https://api.fixer.io/latest',
        'currencylayer' => 'http://api.currencylayer.com/live',
    ];

    /**
     * Update all location currency rates
     */
    public function updateAllRates(): array
    {
        $results = [];
        $locationCurrencies = LocationCurrency::where('is_active', true)->get();

        foreach ($locationCurrencies as $locationCurrency) {
            try {
                $result = $this->updateCurrencyRate($locationCurrency);
                $results[] = [
                    'currency' => $locationCurrency->location_currency_code,
                    'success' => $result['success'],
                    'message' => $result['message'],
                    'old_rate' => $result['old_rate'] ?? null,
                    'new_rate' => $result['new_rate'] ?? null,
                ];
            } catch (\Exception $e) {
                $results[] = [
                    'currency' => $locationCurrency->location_currency_code,
                    'success' => false,
                    'message' => 'Error: ' . $e->getMessage(),
                ];
                
                Log::error('Failed to update currency rate', [
                    'currency' => $locationCurrency->location_currency_code,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $results;
    }

    /**
     * Update a specific currency rate
     */
    public function updateCurrencyRate(LocationCurrency $locationCurrency): array
    {
        $baseCurrency = $this->extractBaseCurrency($locationCurrency->location_currency_code);
        $targetCurrency = $locationCurrency->base_currency ?? 'USD';

        // Get rate from external API
        $newRate = $this->fetchRateFromAPI($baseCurrency, $targetCurrency);
        
        if (!$newRate) {
            return [
                'success' => false,
                'message' => 'Failed to fetch rate from external APIs',
            ];
        }

        $oldBuyRate = $locationCurrency->buy_rate;
        $oldSellRate = $locationCurrency->sell_rate;

        // Apply spread for buy/sell rates
        $spread = $this->calculateSpread($baseCurrency, $newRate);
        $newBuyRate = $newRate - $spread;
        $newSellRate = $newRate + $spread;

        // Update the location currency
        $locationCurrency->update([
            'buy_rate' => $newBuyRate,
            'sell_rate' => $newSellRate,
            'mid_rate' => $newRate,
            'rate_date' => now(),
            'rate_source' => 'api_update',
            'last_updated' => now(),
        ]);

        // Broadcast the update
        event(new CurrencyRateUpdated($locationCurrency, $oldBuyRate, $oldSellRate));

        // Cache the new rate
        $this->cacheRate($locationCurrency);

        return [
            'success' => true,
            'message' => 'Rate updated successfully',
            'old_rate' => $oldBuyRate,
            'new_rate' => $newBuyRate,
            'spread' => $spread,
        ];
    }

    /**
     * Fetch rate from external API
     */
    private function fetchRateFromAPI(string $baseCurrency, string $targetCurrency): ?float
    {
        // Try multiple APIs for reliability
        $apis = [
            'exchangerate_api' => function() use ($baseCurrency, $targetCurrency) {
                $response = Http::timeout(10)->get(self::API_ENDPOINTS['exchangerate_api'] . $baseCurrency);
                if ($response->successful()) {
                    $data = $response->json();
                    return $data['rates'][$targetCurrency] ?? null;
                }
                return null;
            },
            
            'mock_api' => function() use ($baseCurrency, $targetCurrency) {
                // Mock API for development/testing
                return $this->getMockRate($baseCurrency, $targetCurrency);
            }
        ];

        foreach ($apis as $apiName => $apiCall) {
            try {
                $rate = $apiCall();
                if ($rate) {
                    Log::info("Successfully fetched rate from {$apiName}", [
                        'base' => $baseCurrency,
                        'target' => $targetCurrency,
                        'rate' => $rate,
                    ]);
                    return $rate;
                }
            } catch (\Exception $e) {
                Log::warning("Failed to fetch rate from {$apiName}", [
                    'error' => $e->getMessage(),
                ]);
                continue;
            }
        }

        return null;
    }

    /**
     * Get mock rate for development
     */
    private function getMockRate(string $baseCurrency, string $targetCurrency): float
    {
        $mockRates = [
            'USD_TRY' => 32.15 + (rand(-100, 100) / 1000), // Add some variation
            'EUR_TRY' => 34.80 + (rand(-100, 100) / 1000),
            'USD_IRR' => 42000 + (rand(-1000, 1000)),
            'EUR_IRR' => 45500 + (rand(-1000, 1000)),
            'TRY_IRR' => 1300 + (rand(-50, 50)),
            'USD_AED' => 3.67 + (rand(-10, 10) / 1000),
            'EUR_AED' => 3.98 + (rand(-10, 10) / 1000),
        ];

        $pair = $baseCurrency . '_' . $targetCurrency;
        return $mockRates[$pair] ?? 1.0;
    }

    /**
     * Calculate spread based on currency and volatility
     */
    private function calculateSpread(string $currency, float $rate): float
    {
        $spreadPercentages = [
            'USD' => 0.002, // 0.2%
            'EUR' => 0.002, // 0.2%
            'TRY' => 0.005, // 0.5%
            'IRR' => 0.01,  // 1.0%
            'AED' => 0.003, // 0.3%
        ];

        $spreadPercent = $spreadPercentages[$currency] ?? 0.005;
        return $rate * $spreadPercent;
    }

    /**
     * Extract base currency from location currency code
     */
    private function extractBaseCurrency(string $locationCurrencyCode): string
    {
        return explode('_', $locationCurrencyCode)[0];
    }

    /**
     * Cache the rate for quick access
     */
    private function cacheRate(LocationCurrency $locationCurrency): void
    {
        $cacheKey = "currency_rate_{$locationCurrency->location_currency_code}";
        $cacheData = [
            'buy_rate' => $locationCurrency->buy_rate,
            'sell_rate' => $locationCurrency->sell_rate,
            'mid_rate' => $locationCurrency->mid_rate,
            'updated_at' => $locationCurrency->last_updated,
        ];

        Cache::put($cacheKey, $cacheData, now()->addMinutes(15));
    }

    /**
     * Get cached rate
     */
    public function getCachedRate(string $locationCurrencyCode): ?array
    {
        $cacheKey = "currency_rate_{$locationCurrencyCode}";
        return Cache::get($cacheKey);
    }

    /**
     * Schedule automatic rate updates
     */
    public function scheduleRateUpdates(): void
    {
        // This would typically be called from a scheduled job
        $this->updateAllRates();
    }

    /**
     * Get rate update history
     */
    public function getRateHistory(string $locationCurrencyCode, int $days = 7): array
    {
        // This could be expanded to store historical rates
        $locationCurrency = LocationCurrency::where('location_currency_code', $locationCurrencyCode)->first();
        
        if (!$locationCurrency) {
            return [];
        }

        // For now, return current rate
        return [
            [
                'date' => $locationCurrency->rate_date,
                'buy_rate' => $locationCurrency->buy_rate,
                'sell_rate' => $locationCurrency->sell_rate,
                'mid_rate' => $locationCurrency->mid_rate,
            ]
        ];
    }

    /**
     * Check if rates need updating
     */
    public function ratesNeedUpdate(): bool
    {
        $staleThreshold = now()->subMinutes(30);
        
        return LocationCurrency::where('is_active', true)
            ->where('last_updated', '<', $staleThreshold)
            ->exists();
    }

    /**
     * Get rate change alerts
     */
    public function getRateChangeAlerts(float $thresholdPercent = 2.0): array
    {
        $alerts = [];
        $locationCurrencies = LocationCurrency::where('is_active', true)->get();

        foreach ($locationCurrencies as $currency) {
            $cachedRate = $this->getCachedRate($currency->location_currency_code);
            
            if ($cachedRate) {
                $changePercent = abs(($currency->buy_rate - $cachedRate['buy_rate']) / $cachedRate['buy_rate']) * 100;
                
                if ($changePercent >= $thresholdPercent) {
                    $alerts[] = [
                        'currency' => $currency->location_currency_code,
                        'change_percent' => $changePercent,
                        'old_rate' => $cachedRate['buy_rate'],
                        'new_rate' => $currency->buy_rate,
                        'direction' => $currency->buy_rate > $cachedRate['buy_rate'] ? 'up' : 'down',
                    ];
                }
            }
        }

        return $alerts;
    }
}
