<div class="space-y-6">
    <!-- Report Header -->
    <x-filament::section>
        <x-slot name="heading">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                    <x-heroicon-o-currency-dollar class="w-5 h-5" />
                    Profit & Loss Statement
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400">
                    {{ \Carbon\Carbon::parse($data['period']['start_date'])->format('M d, Y') }} - 
                    {{ \Carbon\Carbon::parse($data['period']['end_date'])->format('M d, Y') }}
                    ({{ $data['period']['days'] }} days)
                </div>
            </div>
        </x-slot>
        
        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg text-center">
                <div class="text-green-600 dark:text-green-400 text-sm font-medium">Total Revenue</div>
                <div class="text-2xl font-bold text-green-900 dark:text-green-100">
                    {{ number_format($data['revenue']['total'], 2) }} TRY
                </div>
                <div class="text-xs text-green-600 dark:text-green-400 mt-1">
                    {{ number_format($data['daily_average']['revenue'], 2) }} TRY/day
                </div>
            </div>
            
            <div class="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg text-center">
                <div class="text-red-600 dark:text-red-400 text-sm font-medium">Total Expenses</div>
                <div class="text-2xl font-bold text-red-900 dark:text-red-100">
                    {{ number_format($data['expenses']['total'], 2) }} TRY
                </div>
                <div class="text-xs text-red-600 dark:text-red-400 mt-1">
                    {{ number_format($data['daily_average']['expenses'], 2) }} TRY/day
                </div>
            </div>
            
            <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg text-center">
                <div class="text-blue-600 dark:text-blue-400 text-sm font-medium">Net Income</div>
                <div class="text-2xl font-bold {{ $data['net_income'] >= 0 ? 'text-green-900 dark:text-green-100' : 'text-red-900 dark:text-red-100' }}">
                    {{ number_format($data['net_income'], 2) }} TRY
                </div>
                <div class="text-xs text-blue-600 dark:text-blue-400 mt-1">
                    {{ number_format($data['profit_margin'], 1) }}% margin
                </div>
            </div>
        </div>
    </x-filament::section>

    <!-- Detailed P&L Statement -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Revenue Breakdown -->
        <x-filament::section>
            <x-slot name="heading">Revenue Breakdown</x-slot>
            
            <div class="space-y-4">
                <div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                    <span class="font-medium">Commission Revenue</span>
                    <span class="font-semibold text-green-600">
                        {{ number_format($data['revenue']['commission_revenue'], 2) }} TRY
                    </span>
                </div>
                
                @if($data['revenue']['other_revenue'] > 0)
                    <div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                        <span class="font-medium">Other Revenue</span>
                        <span class="font-semibold text-green-600">
                            {{ number_format($data['revenue']['other_revenue'], 2) }} TRY
                        </span>
                    </div>
                @endif
                
                <div class="flex justify-between items-center py-3 bg-green-50 dark:bg-green-900/20 px-4 rounded-lg">
                    <span class="font-bold text-green-900 dark:text-green-100">Total Revenue</span>
                    <span class="font-bold text-green-900 dark:text-green-100">
                        {{ number_format($data['revenue']['total'], 2) }} TRY
                    </span>
                </div>
                
                <!-- Revenue by Transaction Type -->
                @if(count($data['revenue']['by_type']) > 0)
                    <div class="mt-6">
                        <h4 class="font-semibold text-gray-900 dark:text-gray-100 mb-3">Revenue by Transaction Type</h4>
                        <div class="space-y-2">
                            @foreach($data['revenue']['by_type'] as $type => $amount)
                                <div class="flex justify-between items-center py-1">
                                    <span class="text-sm capitalize">{{ str_replace('_', ' ', $type) }}</span>
                                    <span class="text-sm font-medium">{{ number_format($amount, 2) }} TRY</span>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>
        </x-filament::section>

        <!-- Expense Breakdown -->
        <x-filament::section>
            <x-slot name="heading">Expense Breakdown</x-slot>
            
            <div class="space-y-4">
                <div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                    <span class="font-medium">Operational Expenses</span>
                    <span class="font-semibold text-red-600">
                        {{ number_format($data['expenses']['operational_expenses'], 2) }} TRY
                    </span>
                </div>
                
                <div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                    <span class="font-medium">Delivery Costs</span>
                    <span class="font-semibold text-red-600">
                        {{ number_format($data['expenses']['delivery_costs'], 2) }} TRY
                    </span>
                </div>
                
                <div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                    <span class="font-medium">Administrative Expenses</span>
                    <span class="font-semibold text-red-600">
                        {{ number_format($data['expenses']['administrative_expenses'], 2) }} TRY
                    </span>
                </div>
                
                <div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                    <span class="font-medium">Other Expenses</span>
                    <span class="font-semibold text-red-600">
                        {{ number_format($data['expenses']['other_expenses'], 2) }} TRY
                    </span>
                </div>
                
                <div class="flex justify-between items-center py-3 bg-red-50 dark:bg-red-900/20 px-4 rounded-lg">
                    <span class="font-bold text-red-900 dark:text-red-100">Total Expenses</span>
                    <span class="font-bold text-red-900 dark:text-red-100">
                        {{ number_format($data['expenses']['total'], 2) }} TRY
                    </span>
                </div>
                
                @if($data['expenses']['total'] == 0)
                    <div class="text-center py-4 text-gray-500 text-sm">
                        <x-heroicon-o-information-circle class="w-5 h-5 mx-auto mb-2" />
                        No expense tracking implemented yet.<br>
                        Consider adding expense categories for complete P&L analysis.
                    </div>
                @endif
            </div>
        </x-filament::section>
    </div>

    <!-- Performance Metrics -->
    <x-filament::section>
        <x-slot name="heading">Performance Metrics</x-slot>
        
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div class="text-gray-600 dark:text-gray-400 text-sm font-medium">Profit Margin</div>
                <div class="text-xl font-bold {{ $data['profit_margin'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                    {{ number_format($data['profit_margin'], 2) }}%
                </div>
            </div>
            
            <div class="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div class="text-gray-600 dark:text-gray-400 text-sm font-medium">Daily Avg Revenue</div>
                <div class="text-xl font-bold text-green-600">
                    {{ number_format($data['daily_average']['revenue'], 2) }} TRY
                </div>
            </div>
            
            <div class="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div class="text-gray-600 dark:text-gray-400 text-sm font-medium">Daily Avg Expenses</div>
                <div class="text-xl font-bold text-red-600">
                    {{ number_format($data['daily_average']['expenses'], 2) }} TRY
                </div>
            </div>
            
            <div class="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div class="text-gray-600 dark:text-gray-400 text-sm font-medium">Daily Avg Profit</div>
                <div class="text-xl font-bold {{ $data['daily_average']['net_income'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                    {{ number_format($data['daily_average']['net_income'], 2) }} TRY
                </div>
            </div>
        </div>
    </x-filament::section>

    <!-- Summary -->
    <x-filament::section>
        <x-slot name="heading">Summary</x-slot>
        
        <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
            <div class="text-center">
                <div class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                    Financial Performance Summary
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    For the period {{ \Carbon\Carbon::parse($data['period']['start_date'])->format('M d, Y') }} - 
                    {{ \Carbon\Carbon::parse($data['period']['end_date'])->format('M d, Y') }}
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                    <div>
                        <div class="text-2xl font-bold text-green-600">
                            {{ number_format($data['revenue']['total'], 0) }} TRY
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Total Revenue</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-red-600">
                            {{ number_format($data['expenses']['total'], 0) }} TRY
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Total Expenses</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold {{ $data['net_income'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                            {{ number_format($data['net_income'], 0) }} TRY
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Net Income</div>
                    </div>
                </div>
            </div>
        </div>
    </x-filament::section>
</div>
