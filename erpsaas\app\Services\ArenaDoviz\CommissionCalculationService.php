<?php

namespace App\Services\ArenaDoviz;

use App\Models\ArenaDoviz\CommissionRule;
use App\Models\ArenaDoviz\CurrencyExchange;
use Illuminate\Support\Collection;

class CommissionCalculationService
{
    /**
     * Calculate commission for a currency exchange
     */
    public function calculateCommission(array $exchangeData): array
    {
        $rules = $this->findApplicableRules($exchangeData);
        
        if ($rules->isEmpty()) {
            return $this->getDefaultCommission($exchangeData);
        }

        // Use the highest priority rule
        $rule = $rules->first();
        
        return $rule->calculateCommission(
            $exchangeData['source_amount'],
            $exchangeData['destination_amount'],
            $exchangeData['source_currency'],
            $exchangeData['destination_currency']
        );
    }

    /**
     * Find applicable commission rules for exchange
     */
    private function findApplicableRules(array $exchangeData): Collection
    {
        $criteria = [
            'client_id' => $exchangeData['client_id'] ?? null,
            'location_id' => $exchangeData['location_id'] ?? null,
            'exchange_type' => $exchangeData['exchange_type'] ?? null,
            'from_currency_code' => $exchangeData['source_currency'] ?? null,
            'to_currency_code' => $exchangeData['destination_currency'] ?? null,
        ];

        return CommissionRule::valid()
            ->byPriority()
            ->get()
            ->filter(function ($rule) use ($criteria) {
                return $rule->matches($criteria);
            });
    }

    /**
     * Get default commission when no rules apply
     */
    private function getDefaultCommission(array $exchangeData): array
    {
        // Default commission: 0.5% of destination amount
        $defaultRate = 0.5;
        $commissionAmount = $exchangeData['destination_amount'] * ($defaultRate / 100);

        return [
            'commission_amount' => round($commissionAmount, 2),
            'commission_currency' => $exchangeData['destination_currency'],
            'commission_rate_used' => $defaultRate,
            'base_amount' => $exchangeData['destination_amount'],
            'calculation_method' => 'post_conversion',
            'rule_applied' => 'Default Rule',
        ];
    }

    /**
     * Calculate commission with flexible currency selection
     */
    public function calculateFlexibleCommission(array $exchangeData, string $preferredCommissionCurrency = null): array
    {
        $commission = $this->calculateCommission($exchangeData);
        
        // If preferred currency is specified and different from calculated currency
        if ($preferredCommissionCurrency && $preferredCommissionCurrency !== $commission['commission_currency']) {
            $commission = $this->convertCommissionCurrency($commission, $preferredCommissionCurrency);
        }

        return $commission;
    }

    /**
     * Convert commission to different currency
     */
    private function convertCommissionCurrency(array $commission, string $targetCurrency): array
    {
        $locationCurrencyService = app(LocationCurrencyService::class);
        $rate = $locationCurrencyService->getExchangeRate(
            $commission['commission_currency'],
            $targetCurrency
        );

        if ($rate) {
            $commission['commission_amount'] = round($commission['commission_amount'] * $rate, 2);
            $commission['commission_currency'] = $targetCurrency;
            $commission['conversion_rate'] = $rate;
        }

        return $commission;
    }

    /**
     * Calculate commission for combined exchanges
     */
    public function calculateCombinedExchangeCommission(array $buyData, array $sellData): array
    {
        $buyCommission = $this->calculateCommission($buyData);
        $sellCommission = $this->calculateCommission($sellData);

        // Calculate profit-based commission
        $profit = $sellData['destination_amount'] - $buyData['source_amount'];
        $profitCommission = max(0, $profit * 0.1); // 10% of profit

        return [
            'buy_commission' => $buyCommission,
            'sell_commission' => $sellCommission,
            'profit_commission' => [
                'commission_amount' => round($profitCommission, 2),
                'commission_currency' => $sellData['destination_currency'],
                'commission_rate_used' => 10.0,
                'base_amount' => $profit,
                'calculation_method' => 'profit_based',
                'rule_applied' => 'Combined Exchange Profit Rule',
            ],
            'total_commission' => round($buyCommission['commission_amount'] + $sellCommission['commission_amount'] + $profitCommission, 2),
        ];
    }

    /**
     * Get commission breakdown for display
     */
    public function getCommissionBreakdown(CurrencyExchange $exchange): array
    {
        $exchangeData = [
            'client_id' => $exchange->client_id,
            'location_id' => $exchange->location_id,
            'exchange_type' => $exchange->exchange_type->value,
            'source_currency' => $exchange->from_currency_code,
            'destination_currency' => $exchange->to_currency_code,
            'source_amount' => $exchange->from_amount,
            'destination_amount' => $exchange->to_amount,
        ];

        $commission = $this->calculateCommission($exchangeData);

        return [
            'rule_name' => $commission['rule_applied'],
            'calculation_method' => $commission['calculation_method'],
            'base_amount' => \App\Helpers\ArenaDoviz\NumberFormatter::formatByCurrency(
                $commission['base_amount'],
                $this->getBaseCurrency($commission['calculation_method'], $exchange)
            ),
            'commission_rate' => $commission['commission_rate_used'] . '%',
            'commission_amount' => \App\Helpers\ArenaDoviz\NumberFormatter::formatByCurrency(
                $commission['commission_amount'],
                $commission['commission_currency']
            ),
            'net_amount' => \App\Helpers\ArenaDoviz\NumberFormatter::formatByCurrency(
                $exchange->to_amount - $commission['commission_amount'],
                $exchange->to_currency_code
            ),
        ];
    }

    /**
     * Get base currency for calculation method
     */
    private function getBaseCurrency(string $method, CurrencyExchange $exchange): string
    {
        return match ($method) {
            'pre_conversion' => $exchange->from_currency_code,
            'post_conversion' => $exchange->to_currency_code,
            'higher_amount', 'lower_amount' => $exchange->to_currency_code,
            default => $exchange->to_currency_code,
        };
    }

    /**
     * Create default commission rules for a company
     */
    public function createDefaultRules(int $companyId): void
    {
        $defaultRules = [
            [
                'rule_name' => 'Standard Buy Commission',
                'exchange_type' => 'buy',
                'commission_type' => 'percentage',
                'commission_rate' => 0.25,
                'calculation_method' => 'post_conversion',
                'priority' => 1,
            ],
            [
                'rule_name' => 'Standard Sell Commission',
                'exchange_type' => 'sell',
                'commission_type' => 'percentage',
                'commission_rate' => 0.25,
                'calculation_method' => 'post_conversion',
                'priority' => 1,
            ],
            [
                'rule_name' => 'Transfer Commission',
                'exchange_type' => 'transfer',
                'commission_type' => 'fixed',
                'fixed_amount' => 50.00,
                'commission_currency' => 'TRY_IST',
                'calculation_method' => 'post_conversion',
                'priority' => 1,
            ],
            [
                'rule_name' => 'High Volume Discount',
                'commission_type' => 'tiered',
                'tier_rules' => [
                    ['min_amount' => 0, 'max_amount' => 10000, 'rate' => 0.5],
                    ['min_amount' => 10000, 'max_amount' => 50000, 'rate' => 0.3],
                    ['min_amount' => 50000, 'max_amount' => PHP_FLOAT_MAX, 'rate' => 0.2],
                ],
                'calculation_method' => 'post_conversion',
                'priority' => 2,
            ],
        ];

        foreach ($defaultRules as $ruleData) {
            CommissionRule::create(array_merge($ruleData, [
                'company_id' => $companyId,
                'is_active' => true,
                'created_by' => 1,
            ]));
        }
    }

    /**
     * Validate commission calculation
     */
    public function validateCommission(array $commission, float $maxAllowedRate = 5.0): array
    {
        $errors = [];

        if ($commission['commission_rate_used'] > $maxAllowedRate) {
            $errors[] = "Commission rate ({$commission['commission_rate_used']}%) exceeds maximum allowed rate ({$maxAllowedRate}%)";
        }

        if ($commission['commission_amount'] < 0) {
            $errors[] = "Commission amount cannot be negative";
        }

        if ($commission['commission_amount'] > $commission['base_amount']) {
            $errors[] = "Commission amount cannot exceed base amount";
        }

        return [
            'is_valid' => empty($errors),
            'errors' => $errors,
            'commission' => $commission,
        ];
    }
}
