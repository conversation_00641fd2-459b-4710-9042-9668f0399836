<?php

namespace App\Filament\Company\Resources\ArenaDoviz\LocationCurrencyResource\Pages;

use App\Filament\Company\Resources\ArenaDoviz\LocationCurrencyResource;
use Filament\Resources\Pages\CreateRecord;

class CreateLocationCurrency extends CreateRecord
{
    protected static string $resource = LocationCurrencyResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
