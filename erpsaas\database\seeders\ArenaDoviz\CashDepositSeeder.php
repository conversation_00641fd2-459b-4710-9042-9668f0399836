<?php

namespace Database\Seeders\ArenaDoviz;

use App\Enums\ArenaDoviz\CashDepositStatus;
use App\Models\Common\Client;
use App\Models\ArenaDoviz\CurrencyExchange;
use App\Models\ArenaDoviz\CashDeposit;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class CashDepositSeeder extends Seeder
{
    /**
     * Seed cash deposit transactions for Arena Doviz.
     * 
     * Creates realistic cash deposit data including:
     * - Various currency cash deposits
     * - Denomination breakdowns for verification
     * - Different processing statuses
     * - Batch processing for Iranian Rial (IRR)
     * - Vault location tracking
     */
    public function run(): void
    {
        $this->command->info('💵 Seeding Arena Doviz cash deposits...');

        // Set a temporary authenticated user for the CompanyOwned trait
        $adminUser = \App\Models\User::where('email', '<EMAIL>')->first();
        if ($adminUser) {
            Auth::login($adminUser);
        }

        // Temporarily disable event dispatcher to prevent WhatsApp notifications
        CurrencyExchange::unsetEventDispatcher();

        $clients = Client::where('company_id', 1)->get();
        
        if ($clients->isEmpty()) {
            $this->command->warn('No clients found. Please run ClientProfileSeeder first.');
            return;
        }
        
        $depositCount = 0;
        
        // Create cash deposits for the last 3 months
        $startDate = Carbon::now()->subMonths(3);
        $endDate = Carbon::now();
        
        for ($date = $startDate->copy(); $date->lte($endDate); $date->addDays(rand(1, 3))) {
            // Skip weekends
            if ($date->isWeekend()) {
                continue;
            }
            
            // Generate 1-5 cash deposits per day
            $dailyDeposits = rand(1, 5);
            
            for ($i = 0; $i < $dailyDeposits; $i++) {
                $this->createCashDeposit($clients, $date->copy()->addHours(rand(9, 17)));
                $depositCount++;
            }
        }
        
        // Create some specific scenario deposits
        $this->createSpecificScenarios($clients);
        $depositCount += 3;

        // Re-enable event dispatcher
        CurrencyExchange::setEventDispatcher(app('events'));

        // Logout the temporary user
        Auth::logout();

        $this->command->info("✅ Created {$depositCount} cash deposits");
    }
    
    private function createCashDeposit($clients, Carbon $date): void
    {
        $client = $clients->random();
        $currency = $this->getRandomCurrency();
        $amount = $this->generateAmount($currency);
        $status = $this->getDepositStatus($date);
        
        // Create associated currency exchange first
        $exchange = $this->createAssociatedExchange($client, $currency, $amount, $date);
        
        CashDeposit::create([
            'company_id' => 1,
            'currency_exchange_id' => $exchange->id,
            'client_id' => $client->id,
            'deposit_reference' => $this->generateDepositReference($date),
            'status' => $status,
            'currency_code' => $currency,
            'amount' => $amount,
            'denomination_breakdown' => $this->generateDenominationBreakdown($currency, $amount),
            'deposit_date' => $date,
            'processed_date' => $status === CashDepositStatus::PROCESSED ? 
                $date->copy()->addHours(rand(1, 4)) : null,
            'processed_by' => $status->isComplete() ? $this->getValidUserId() : null,
            'vault_location' => $this->getVaultLocation(),
            'batch_number' => $currency === 'IRR' ? $this->generateBatchNumber($date) : null,
            'verification_notes' => $this->getVerificationNotes($status, $currency),
            'photo_path' => $this->getPhotoPath($status),
            'created_by' => $this->getValidUserId(),
            'created_at' => $date,
            'updated_at' => $date,
        ]);
    }
    
    private function createAssociatedExchange(Client $client, string $currency, float $amount, Carbon $date): CurrencyExchange
    {
        $exchangeRate = $this->getExchangeRate($currency);
        $tryAmount = $currency === 'TRY' ? $amount : $amount * $exchangeRate;
        
        return CurrencyExchange::create([
            'company_id' => 1,
            'client_id' => $client->id,
            'exchange_number' => $this->generateExchangeNumber($date),
            'exchange_type' => 'transfer',
            'status' => 'completed',
            'from_currency_code' => $currency,
            'to_currency_code' => 'TRY',
            'from_amount' => $amount,
            'to_amount' => $tryAmount,
            'exchange_rate' => $exchangeRate,
            'commission_rate' => 0.0,
            'commission_amount' => 0.0,
            'net_amount' => $tryAmount,
            'notes' => "Cash deposit - {$currency}",
            'exchange_date' => $date,
            'settlement_date' => $date,
            'delivery_method' => null, // Cash deposits don't have delivery
            'delivery_address' => null,
            'reference_number' => 'CASH' . $date->format('Ymd') . rand(1000, 9999),
            'created_by' => $this->getValidUserId(),
            'created_at' => $date,
            'updated_at' => $date,
        ]);
    }
    
    private function getRandomCurrency(): string
    {
        $currencies = [
            'TRY' => 40,  // 40% Turkish Lira
            'USD' => 25,  // 25% US Dollar
            'EUR' => 20,  // 20% Euro
            'AED' => 10,  // 10% UAE Dirham
            'IRR' => 5,   // 5% Iranian Rial
        ];
        
        $rand = rand(1, 100);
        $cumulative = 0;
        
        foreach ($currencies as $currency => $weight) {
            $cumulative += $weight;
            if ($rand <= $cumulative) {
                return $currency;
            }
        }
        
        return 'TRY';
    }
    
    private function generateAmount(string $currency): float
    {
        return match ($currency) {
            'TRY' => rand(1000, 50000),
            'USD' => rand(100, 5000),
            'EUR' => rand(100, 4500),
            'AED' => rand(500, 15000),
            'IRR' => rand(10000000, 500000000),
            default => rand(1000, 10000),
        };
    }
    
    private function getExchangeRate(string $currency): float
    {
        return match ($currency) {
            'TRY' => 1.0,
            'USD' => 32.50,
            'EUR' => 35.20,
            'AED' => 8.85,
            'IRR' => 0.0008,
            default => 1.0,
        };
    }
    
    private function getDepositStatus(Carbon $date): CashDepositStatus
    {
        $daysAgo = $date->diffInDays(Carbon::now());
        
        if ($daysAgo > 7) {
            // Old deposits: mostly processed
            return rand(1, 100) <= 90 ? CashDepositStatus::PROCESSED : CashDepositStatus::REJECTED;
        } elseif ($daysAgo > 1) {
            // Recent deposits: mix of statuses
            $statuses = [
                ['status' => CashDepositStatus::PROCESSED, 'weight' => 70],
                ['status' => CashDepositStatus::VERIFIED, 'weight' => 20],
                ['status' => CashDepositStatus::PENDING, 'weight' => 8],
                ['status' => CashDepositStatus::REJECTED, 'weight' => 2],
            ];
        } else {
            // Very recent: more pending/verified
            $statuses = [
                ['status' => CashDepositStatus::PROCESSED, 'weight' => 30],
                ['status' => CashDepositStatus::VERIFIED, 'weight' => 40],
                ['status' => CashDepositStatus::PENDING, 'weight' => 28],
                ['status' => CashDepositStatus::REJECTED, 'weight' => 2],
            ];
        }

        $rand = rand(1, 100);
        $cumulative = 0;

        foreach ($statuses as $statusData) {
            $cumulative += $statusData['weight'];
            if ($rand <= $cumulative) {
                return $statusData['status'];
            }
        }
        
        return CashDepositStatus::PROCESSED;
    }
    
    private function generateDenominationBreakdown(string $currency, float $amount): array
    {
        $denominations = $this->getDenominations($currency);
        $breakdown = [];
        $remaining = $amount;
        
        // Start with largest denominations
        rsort($denominations);
        
        foreach ($denominations as $denom) {
            if ($remaining >= $denom) {
                $count = intval($remaining / $denom);
                if ($count > 0) {
                    $breakdown[$denom] = $count;
                    $remaining -= ($count * $denom);
                }
            }
        }
        
        return $breakdown;
    }
    
    private function getDenominations(string $currency): array
    {
        return match ($currency) {
            'TRY' => [200, 100, 50, 20, 10, 5],
            'USD' => [100, 50, 20, 10, 5, 1],
            'EUR' => [500, 200, 100, 50, 20, 10, 5],
            'AED' => [1000, 500, 200, 100, 50, 20, 10, 5],
            'IRR' => [1000000, 500000, 200000, 100000, 50000, 20000, 10000],
            default => [100, 50, 20, 10, 5, 1],
        };
    }
    
    private function getVaultLocation(): string
    {
        $locations = [
            'Main Vault - Level B1',
            'Secondary Vault - Level B2',
            'Branch Vault - Istanbul',
            'Temporary Storage - Office Safe',
            'Central Vault - Security Room',
        ];
        
        return $locations[array_rand($locations)];
    }
    
    private function generateBatchNumber(Carbon $date): string
    {
        return 'IRR_BATCH_' . $date->format('Ymd') . '_' . rand(100, 999);
    }
    
    private function getVerificationNotes(CashDepositStatus $status, string $currency): ?string
    {
        if ($status === CashDepositStatus::PENDING) {
            return null;
        }
        
        $notes = match ($status) {
            CashDepositStatus::VERIFIED => [
                "Cash counted and verified - {$currency} denomination breakdown confirmed",
                "Amount verified by cashier - all notes authentic",
                "Verification complete - ready for processing",
            ],
            CashDepositStatus::PROCESSED => [
                "Cash processed and stored in vault",
                "Deposit completed successfully - client balance updated",
                "Processing complete - all documentation filed",
            ],
            CashDepositStatus::REJECTED => [
                "Counterfeit notes detected - deposit rejected",
                "Amount discrepancy found - client notified",
                "Incomplete documentation - deposit returned",
            ],
            default => ["Standard verification completed"],
        };
        
        return $notes[array_rand($notes)];
    }
    
    private function getPhotoPath(CashDepositStatus $status): ?string
    {
        if ($status === CashDepositStatus::PENDING || rand(1, 100) > 70) {
            return null; // 30% have photos
        }
        
        return 'cash_deposits/' . date('Y/m/d') . '/deposit_' . rand(100000, 999999) . '.jpg';
    }
    
    private function createSpecificScenarios($clients): void
    {
        // Large IRR batch deposit
        $client = $clients->random();
        $date = Carbon::now()->subDays(10);
        $amount = 1000000000; // 1 billion IRR
        
        $exchange = $this->createAssociatedExchange($client, 'IRR', $amount, $date);
        
        CashDeposit::create([
            'company_id' => 1,
            'currency_exchange_id' => $exchange->id,
            'client_id' => $client->id,
            'status' => CashDepositStatus::PROCESSED,
            'currency_code' => 'IRR',
            'amount' => $amount,
            'denomination_breakdown' => [
                '1000000' => 800,
                '500000' => 400,
            ],
            'deposit_date' => $date,
            'processed_date' => $date->copy()->addHours(3),
            'processed_by' => 2,
            'vault_location' => 'Central Vault - Security Room',
            'batch_number' => 'IRR_BATCH_LARGE_001',
            'verification_notes' => 'Large IRR batch deposit - special handling required',
            'photo_path' => 'cash_deposits/special/large_irr_batch_001.jpg',
            'created_by' => $this->getValidUserId(),
            'created_at' => $date,
            'updated_at' => $date->copy()->addHours(3),
        ]);
        
        // Rejected deposit scenario
        $client = $clients->random();
        $date = Carbon::now()->subDays(5);
        
        $exchange = $this->createAssociatedExchange($client, 'USD', 1000, $date);
        
        CashDeposit::create([
            'company_id' => 1,
            'currency_exchange_id' => $exchange->id,
            'client_id' => $client->id,
            'status' => CashDepositStatus::REJECTED,
            'currency_code' => 'USD',
            'amount' => 1000,
            'denomination_breakdown' => ['100' => 10],
            'deposit_date' => $date,
            'processed_date' => null,
            'processed_by' => null,
            'vault_location' => 'Temporary Storage - Office Safe',
            'batch_number' => null,
            'verification_notes' => 'Counterfeit $100 bills detected - deposit rejected and returned to client',
            'photo_path' => 'cash_deposits/rejected/counterfeit_usd_001.jpg',
            'created_by' => 3,
            'created_at' => $date,
            'updated_at' => $date->copy()->addHours(1),
        ]);
        
        // Pending verification
        $client = $clients->random();
        $date = Carbon::now()->subHours(2);
        
        $exchange = $this->createAssociatedExchange($client, 'EUR', 2500, $date);
        
        CashDeposit::create([
            'company_id' => 1,
            'currency_exchange_id' => $exchange->id,
            'client_id' => $client->id,
            'status' => CashDepositStatus::PENDING,
            'currency_code' => 'EUR',
            'amount' => 2500,
            'denomination_breakdown' => [
                '500' => 3,
                '200' => 2,
                '100' => 6,
            ],
            'deposit_date' => $date,
            'processed_date' => null,
            'processed_by' => null,
            'vault_location' => 'Temporary Storage - Office Safe',
            'batch_number' => null,
            'verification_notes' => null,
            'photo_path' => null,
            'created_by' => 4,
            'created_at' => $date,
            'updated_at' => $date,
        ]);
    }

    private function generateExchangeNumber(Carbon $date): string
    {
        $prefix = 'EX';
        $year = $date->format('Y');
        $month = $date->format('m');

        // Use a more robust counter for seeding
        static $counters = [];
        $key = $year . $month;

        if (!isset($counters[$key])) {
            $counters[$key] = 20000; // Start from a high number to avoid conflicts
        } else {
            $counters[$key]++;
        }

        return sprintf('%s%s%s%04d', $prefix, $year, $month, $counters[$key]);
    }

    private function getValidUserId(): int
    {
        static $validUserIds = null;

        if ($validUserIds === null) {
            $validUserIds = \App\Models\User::where('current_company_id', 1)->pluck('id')->toArray();
        }

        return !empty($validUserIds) ? $validUserIds[array_rand($validUserIds)] : 1;
    }

    private function generateDepositReference(Carbon $date): string
    {
        $prefix = 'CD';
        $year = $date->format('Y');
        $month = $date->format('m');

        // Use a more robust counter for seeding
        static $counters = [];
        $key = $year . $month;

        if (!isset($counters[$key])) {
            $counters[$key] = 1;
        } else {
            $counters[$key]++;
        }

        return sprintf('%s%s%s%04d', $prefix, $year, $month, $counters[$key]);
    }
}
