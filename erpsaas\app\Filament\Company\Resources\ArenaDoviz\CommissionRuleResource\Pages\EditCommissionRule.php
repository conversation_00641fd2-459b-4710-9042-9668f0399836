<?php

namespace App\Filament\Company\Resources\ArenaDoviz\CommissionRuleResource\Pages;

use App\Filament\Company\Resources\ArenaDoviz\CommissionRuleResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditCommissionRule extends EditRecord
{
    protected static string $resource = CommissionRuleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
