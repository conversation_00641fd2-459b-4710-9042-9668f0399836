<?php

namespace App\Filament\Company\Resources\ArenaDoviz\DeliveryResource\Pages;

use App\Filament\Company\Resources\ArenaDoviz\DeliveryResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditDelivery extends EditRecord
{
    protected static string $resource = DeliveryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
