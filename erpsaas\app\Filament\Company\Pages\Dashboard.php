<?php

namespace App\Filament\Company\Pages;

use App\Filament\Company\Widgets\ArenaDovizOverviewWidget;
use App\Filament\Company\Widgets\CurrencyDistributionWidget;
use App\Filament\Company\Widgets\TopClientsWidget;
use App\Filament\Company\Widgets\TransactionAnalyticsWidget;
use App\Services\ArenaDoviz\NavigationService;
use App\Services\ArenaDoviz\UIComponentService;
use Filament\Actions\Action;
use Filament\Pages\Dashboard as BaseDashboard;
use Filament\Support\Enums\IconPosition;
use Illuminate\Contracts\Support\Htmlable;

class Dashboard extends BaseDashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-home';
    protected static ?string $navigationLabel = 'Dashboard';
    protected static ?string $title = 'Arena Doviz Dashboard';
    protected static ?int $navigationSort = -10;

    public function getTitle(): string|Htmlable
    {
        return 'Arena Doviz Dashboard';
    }

    public function getSubheading(): string|Htmlable|null
    {
        return 'Overview and key metrics for your currency exchange operations';
    }

    /**
     * @return array<class-string<Widget> | WidgetConfiguration>
     */
    public function getWidgets(): array
    {
        return [
            ArenaDovizOverviewWidget::class,
            TransactionAnalyticsWidget::class,
            CurrencyDistributionWidget::class,
            TopClientsWidget::class,
        ];
    }

    /**
     * @return int | string | array<string, int | string | null>
     */
    public function getColumns(): int | string | array
    {
        return [
            'sm' => 1,
            'md' => 2,
            'lg' => 3,
            'xl' => 4,
        ];
    }

    protected function getHeaderActions(): array
    {
        $navigationService = app(NavigationService::class);
        $quickActions = $navigationService->getQuickActions();

        $actions = [
            Action::make('newTransaction')
                ->label('New Transaction')
                ->icon('heroicon-o-plus')
                ->color('primary')
                ->button()
                ->url(fn () => \App\Filament\Company\Resources\CurrencyExchangeResource::getUrl('create'))
                ->visible(fn () => auth()->user()->can('exchange:create')),
        ];

        if (!empty($quickActions)) {
            $dropdownActions = collect($quickActions)->map(function ($action) {
                return Action::make(str_replace(' ', '', strtolower($action['label'])))
                    ->label($action['label'])
                    ->icon($action['icon'])
                    ->color($action['color'])
                    ->url($action['url']);
            })->toArray();

            $actions[] = Action::make('quickActions')
                ->label('Quick Actions')
                ->icon('heroicon-o-bolt')
                ->iconPosition(IconPosition::After)
                ->color('warning')
                ->button()
                ->outlined()
                ->dropdownPlacement('bottom-end')
                ->actions($dropdownActions);
        }

        $actions[] = Action::make('viewReports')
            ->label('View Reports')
            ->icon('heroicon-o-chart-bar')
            ->color('gray')
            ->button()
            ->outlined()
            ->url(fn () => \App\Filament\Company\Pages\ArenaDovizReports::getUrl());

        return $actions;
    }

    public function getHeaderWidgets(): array
    {
        return [];
    }

    public function getFooterWidgets(): array
    {
        return [];
    }

    protected function getStats(): array
    {
        $company = auth()->user()->currentCompany;
        $uiService = app(UIComponentService::class);

        return $uiService->getDashboardStats($company->id);
    }
}
