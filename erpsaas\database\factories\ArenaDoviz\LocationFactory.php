<?php

namespace Database\Factories\ArenaDoviz;

use App\Models\ArenaDoviz\Location;
use App\Models\Common\Company;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ArenaDoviz\Location>
 */
class LocationFactory extends Factory
{
    protected $model = Location::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $cities = [
            ['name' => 'Istanbul', 'country' => 'Turkey', 'code' => 'IST', 'timezone' => 'Europe/Istanbul'],
            ['name' => 'Tabriz', 'country' => 'Iran', 'code' => 'TBZ', 'timezone' => 'Asia/Tehran'],
            ['name' => 'Tehran', 'country' => 'Iran', 'code' => 'THR', 'timezone' => 'Asia/Tehran'],
            ['name' => 'Dubai', 'country' => 'UAE', 'code' => 'DXB', 'timezone' => 'Asia/Dubai'],
            ['name' => 'Ankara', 'country' => 'Turkey', 'code' => 'ANK', 'timezone' => 'Europe/Istanbul'],
        ];

        $city = $this->faker->randomElement($cities);

        return [
            'company_id' => Company::factory(),
            'name' => $city['name'] . ' ' . $this->faker->randomElement(['Office', 'Branch', 'Center']),
            'code' => $city['code'],
            'city' => $city['name'],
            'country' => $city['country'],
            'address' => $this->faker->streetAddress . ', ' . $city['name'] . ', ' . $city['country'],
            'timezone' => $city['timezone'],
            'phone' => $this->faker->phoneNumber,
            'email' => strtolower($city['code']) . '@arenadoviz.com',
            'manager_name' => $this->faker->name,
            'working_hours' => $this->generateWorkingHours(),
            'services' => $this->faker->randomElements([
                'currency_exchange',
                'money_transfer',
                'cash_deposit',
                'delivery',
                'iranian_banking',
                'swift_transfer',
            ], $this->faker->numberBetween(2, 4)),
            'max_transaction_amount' => $this->faker->randomFloat(2, 10000, 200000),
            'is_active' => true,
            'created_by' => User::factory(),
        ];
    }

    /**
     * Generate realistic working hours.
     */
    private function generateWorkingHours(): array
    {
        return [
            'monday' => ['open' => '09:00', 'close' => '18:00'],
            'tuesday' => ['open' => '09:00', 'close' => '18:00'],
            'wednesday' => ['open' => '09:00', 'close' => '18:00'],
            'thursday' => ['open' => '09:00', 'close' => '18:00'],
            'friday' => ['open' => '09:00', 'close' => '18:00'],
            'saturday' => ['open' => '10:00', 'close' => '16:00'],
            'sunday' => ['closed' => true],
        ];
    }

    /**
     * Indicate that the location is in Istanbul.
     */
    public function istanbul(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Istanbul Office',
            'code' => 'IST',
            'city' => 'Istanbul',
            'country' => 'Turkey',
            'timezone' => 'Europe/Istanbul',
            'email' => '<EMAIL>',
        ]);
    }

    /**
     * Indicate that the location is in Tabriz.
     */
    public function tabriz(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Tabriz Branch',
            'code' => 'TBZ',
            'city' => 'Tabriz',
            'country' => 'Iran',
            'timezone' => 'Asia/Tehran',
            'email' => '<EMAIL>',
            'services' => ['currency_exchange', 'money_transfer', 'iranian_banking'],
        ]);
    }

    /**
     * Indicate that the location is in Tehran.
     */
    public function tehran(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Tehran Office',
            'code' => 'THR',
            'city' => 'Tehran',
            'country' => 'Iran',
            'timezone' => 'Asia/Tehran',
            'email' => '<EMAIL>',
            'services' => ['currency_exchange', 'money_transfer', 'iranian_banking', 'swift_transfer'],
        ]);
    }

    /**
     * Indicate that the location is in Dubai.
     */
    public function dubai(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Dubai Branch',
            'code' => 'DXB',
            'city' => 'Dubai',
            'country' => 'UAE',
            'timezone' => 'Asia/Dubai',
            'email' => '<EMAIL>',
            'services' => ['currency_exchange', 'money_transfer', 'swift_transfer', 'delivery'],
        ]);
    }

    /**
     * Indicate that the location is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the location has high transaction limits.
     */
    public function highLimits(): static
    {
        return $this->state(fn (array $attributes) => [
            'max_transaction_amount' => $this->faker->randomFloat(2, 100000, 500000),
        ]);
    }

    /**
     * Indicate that the location has low transaction limits.
     */
    public function lowLimits(): static
    {
        return $this->state(fn (array $attributes) => [
            'max_transaction_amount' => $this->faker->randomFloat(2, 5000, 25000),
        ]);
    }

    /**
     * Indicate that the location supports Iranian banking.
     */
    public function withIranianBanking(): static
    {
        return $this->state(fn (array $attributes) => [
            'services' => array_unique(array_merge(
                $attributes['services'] ?? [],
                ['iranian_banking']
            )),
        ]);
    }

    /**
     * Indicate that the location supports delivery services.
     */
    public function withDelivery(): static
    {
        return $this->state(fn (array $attributes) => [
            'services' => array_unique(array_merge(
                $attributes['services'] ?? [],
                ['delivery']
            )),
        ]);
    }

    /**
     * Indicate that the location supports SWIFT transfers.
     */
    public function withSwiftTransfer(): static
    {
        return $this->state(fn (array $attributes) => [
            'services' => array_unique(array_merge(
                $attributes['services'] ?? [],
                ['swift_transfer']
            )),
        ]);
    }

    /**
     * Indicate that the location has weekend hours.
     */
    public function withWeekendHours(): static
    {
        return $this->state(fn (array $attributes) => [
            'working_hours' => [
                'monday' => ['open' => '09:00', 'close' => '18:00'],
                'tuesday' => ['open' => '09:00', 'close' => '18:00'],
                'wednesday' => ['open' => '09:00', 'close' => '18:00'],
                'thursday' => ['open' => '09:00', 'close' => '18:00'],
                'friday' => ['open' => '09:00', 'close' => '18:00'],
                'saturday' => ['open' => '10:00', 'close' => '16:00'],
                'sunday' => ['open' => '12:00', 'close' => '17:00'],
            ],
        ]);
    }

    /**
     * Indicate that the location is closed on weekends.
     */
    public function weekdaysOnly(): static
    {
        return $this->state(fn (array $attributes) => [
            'working_hours' => [
                'monday' => ['open' => '09:00', 'close' => '18:00'],
                'tuesday' => ['open' => '09:00', 'close' => '18:00'],
                'wednesday' => ['open' => '09:00', 'close' => '18:00'],
                'thursday' => ['open' => '09:00', 'close' => '18:00'],
                'friday' => ['open' => '09:00', 'close' => '18:00'],
                'saturday' => ['closed' => true],
                'sunday' => ['closed' => true],
            ],
        ]);
    }
}
