<?php

namespace App\Models\ArenaDoviz;

use App\Concerns\Blamable;
use App\Concerns\CompanyOwned;
use App\Enums\ArenaDoviz\DebitNoteStatus;
use App\Models\Common\Client;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class DebitNote extends Model
{
    use Blamable;
    use CompanyOwned;
    use HasFactory;
    use SoftDeletes;

    protected $table = 'debit_notes';

    protected $fillable = [
        'company_id',
        'client_id',
        'delivery_id',
        'location_id',
        'debit_note_number',
        'status',
        'debit_type',
        'currency_code',
        'amount',
        'description',
        'recipient_name',
        'recipient_phone',
        'recipient_email',
        'delivery_address',
        'scheduled_date',
        'completed_date',
        'signature_path',
        'photo_path',
        'receipt_path',
        'verification_code',
        'special_instructions',
        'courier_notes',
        'client_notes',
        'internal_notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'status' => DebitNoteStatus::class,
        'amount' => 'decimal:2',
        'scheduled_date' => 'datetime',
        'completed_date' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($debitNote) {
            if (empty($debitNote->debit_note_number)) {
                $debitNote->debit_note_number = static::generateDebitNoteNumber();
            }
            if (empty($debitNote->verification_code)) {
                $debitNote->verification_code = static::generateVerificationCode();
            }
        });
    }

    /**
     * Get the client this debit note belongs to
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Get the delivery associated with this debit note
     */
    public function delivery(): BelongsTo
    {
        return $this->belongsTo(Delivery::class);
    }

    /**
     * Get the location for this debit note
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    /**
     * Get the user who created this debit note
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get debit note items (if itemized)
     */
    public function items(): HasMany
    {
        return $this->hasMany(DebitNoteItem::class);
    }

    /**
     * Generate unique debit note number
     */
    public static function generateDebitNoteNumber(): string
    {
        $prefix = 'DN';
        $year = date('Y');
        $month = date('m');
        
        $lastNote = static::whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastNote ? 
            (int) substr($lastNote->debit_note_number, -6) + 1 : 1;

        return sprintf('%s%s%s%06d', $prefix, $year, $month, $sequence);
    }

    /**
     * Generate verification code for recipient
     */
    public static function generateVerificationCode(): string
    {
        return strtoupper(substr(md5(uniqid(rand(), true)), 0, 6));
    }

    /**
     * Get formatted debit note number with prefix
     */
    public function getFormattedNumberAttribute(): string
    {
        return $this->debit_note_number;
    }

    /**
     * Get formatted amount with currency
     */
    public function getFormattedAmountAttribute(): string
    {
        return \App\Helpers\ArenaDoviz\NumberFormatter::formatByCurrency($this->amount, $this->currency_code);
    }

    /**
     * Check if debit note is pending
     */
    public function isPending(): bool
    {
        return $this->status === DebitNoteStatus::PENDING;
    }

    /**
     * Check if debit note is scheduled
     */
    public function isScheduled(): bool
    {
        return $this->status === DebitNoteStatus::SCHEDULED;
    }

    /**
     * Check if debit note is in progress
     */
    public function isInProgress(): bool
    {
        return $this->status === DebitNoteStatus::IN_PROGRESS;
    }

    /**
     * Check if debit note is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === DebitNoteStatus::COMPLETED;
    }

    /**
     * Check if debit note is cancelled
     */
    public function isCancelled(): bool
    {
        return $this->status === DebitNoteStatus::CANCELLED;
    }

    /**
     * Check if signature is required
     */
    public function requiresSignature(): bool
    {
        return in_array($this->debit_type, ['delivery', 'cash_collection', 'document_delivery']);
    }

    /**
     * Check if photo proof is required
     */
    public function requiresPhoto(): bool
    {
        return in_array($this->debit_type, ['delivery', 'cash_collection']);
    }

    /**
     * Check if debit note has all required documents
     */
    public function hasRequiredDocuments(): bool
    {
        $hasSignature = !$this->requiresSignature() || !empty($this->signature_path);
        $hasPhoto = !$this->requiresPhoto() || !empty($this->photo_path);
        
        return $hasSignature && $hasPhoto;
    }

    /**
     * Mark as completed with documents
     */
    public function markCompleted(array $documents = []): void
    {
        $this->update([
            'status' => DebitNoteStatus::COMPLETED,
            'completed_date' => now(),
            'signature_path' => $documents['signature_path'] ?? $this->signature_path,
            'photo_path' => $documents['photo_path'] ?? $this->photo_path,
            'receipt_path' => $documents['receipt_path'] ?? $this->receipt_path,
        ]);
    }

    /**
     * Get status badge color
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            DebitNoteStatus::PENDING => 'warning',
            DebitNoteStatus::SCHEDULED => 'info',
            DebitNoteStatus::IN_PROGRESS => 'primary',
            DebitNoteStatus::COMPLETED => 'success',
            DebitNoteStatus::CANCELLED => 'danger',
            default => 'gray',
        };
    }

    /**
     * Scope for pending debit notes
     */
    public function scopePending($query)
    {
        return $query->where('status', DebitNoteStatus::PENDING);
    }

    /**
     * Scope for today's debit notes
     */
    public function scopeToday($query)
    {
        return $query->whereDate('scheduled_date', today());
    }

    /**
     * Scope for overdue debit notes
     */
    public function scopeOverdue($query)
    {
        return $query->where('scheduled_date', '<', now())
            ->whereNotIn('status', [DebitNoteStatus::COMPLETED, DebitNoteStatus::CANCELLED]);
    }
}
