<?php

namespace App\Services\ArenaDoviz;

use App\Models\ArenaDoviz\ClientBalance;
use App\Models\ArenaDoviz\CurrencyExchange;
use App\Models\ArenaDoviz\Delivery;
use App\Models\Common\Client;
use App\Models\Company;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class DashboardAnalyticsService
{
    /**
     * Get comprehensive dashboard metrics
     */
    public function getDashboardMetrics(Company $company, ?Carbon $startDate = null, ?Carbon $endDate = null): array
    {
        $startDate = $startDate ?? now()->startOfMonth();
        $endDate = $endDate ?? now()->endOfDay();

        return [
            'overview' => $this->getOverviewMetrics($company, $startDate, $endDate),
            'transactions' => $this->getTransactionMetrics($company, $startDate, $endDate),
            'clients' => $this->getClientMetrics($company, $startDate, $endDate),
            'financial' => $this->getFinancialMetrics($company, $startDate, $endDate),
            'operational' => $this->getOperationalMetrics($company, $startDate, $endDate),
            'trends' => $this->getTrendAnalysis($company, $startDate, $endDate),
        ];
    }

    /**
     * Get overview metrics
     */
    public function getOverviewMetrics(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $totalTransactions = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->count();

        $totalVolume = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->sum('from_amount');

        $totalCommission = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->sum('commission_amount');

        $activeClients = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->distinct('client_id')
            ->count('client_id');

        // Previous period comparison
        $previousStart = $startDate->copy()->subDays($startDate->diffInDays($endDate) + 1);
        $previousEnd = $startDate->copy()->subDay();

        $previousTransactions = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$previousStart, $previousEnd])
            ->count();

        $previousVolume = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$previousStart, $previousEnd])
            ->sum('from_amount');

        return [
            'total_transactions' => $totalTransactions,
            'total_volume' => $totalVolume,
            'total_commission' => $totalCommission,
            'active_clients' => $activeClients,
            'avg_transaction_size' => $totalTransactions > 0 ? $totalVolume / $totalTransactions : 0,
            'growth_rates' => [
                'transactions' => $this->calculateGrowthRate($totalTransactions, $previousTransactions),
                'volume' => $this->calculateGrowthRate($totalVolume, $previousVolume),
            ],
        ];
    }

    /**
     * Get transaction metrics
     */
    public function getTransactionMetrics(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $transactionsByType = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->selectRaw('exchange_type, COUNT(*) as count, SUM(from_amount) as volume, SUM(commission_amount) as commission')
            ->groupBy('exchange_type')
            ->get()
            ->keyBy(fn($item) => $item->exchange_type->value);

        $transactionsByStatus = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get()
            ->pluck('count', fn($item) => $item->status->value);

        $transactionsByCurrency = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->selectRaw('from_currency_code as currency, COUNT(*) as count, SUM(from_amount) as volume')
            ->groupBy('from_currency_code')
            ->orderByDesc('volume')
            ->limit(10)
            ->get();

        $hourlyDistribution = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->selectRaw('HOUR(exchange_date) as hour, COUNT(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('count', 'hour');

        return [
            'by_type' => $transactionsByType,
            'by_status' => $transactionsByStatus,
            'by_currency' => $transactionsByCurrency,
            'hourly_distribution' => $hourlyDistribution,
            'completion_rate' => $this->calculateCompletionRate($company, $startDate, $endDate),
        ];
    }

    /**
     * Get client metrics
     */
    public function getClientMetrics(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $topClientsByVolume = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->selectRaw('client_id, COUNT(*) as transaction_count, SUM(from_amount) as total_volume, SUM(commission_amount) as total_commission')
            ->groupBy('client_id')
            ->orderByDesc('total_volume')
            ->limit(10)
            ->with('client')
            ->get();

        $newClients = Client::where('company_id', $company->id)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $clientsByCategory = Client::where('clients.company_id', $company->id)
            ->whereHas('profile')
            ->join('client_profiles', 'clients.id', '=', 'client_profiles.client_id')
            ->where('client_profiles.company_id', $company->id)
            ->selectRaw('client_profiles.category, COUNT(*) as count')
            ->groupBy('client_profiles.category')
            ->pluck('count', 'category');

        $clientsByRisk = Client::where('clients.company_id', $company->id)
            ->whereHas('profile')
            ->join('client_profiles', 'clients.id', '=', 'client_profiles.client_id')
            ->where('client_profiles.company_id', $company->id)
            ->selectRaw('client_profiles.risk_level, COUNT(*) as count')
            ->groupBy('client_profiles.risk_level')
            ->pluck('count', 'risk_level');

        return [
            'top_clients' => $topClientsByVolume,
            'new_clients' => $newClients,
            'by_category' => $clientsByCategory,
            'by_risk_level' => $clientsByRisk,
            'retention_rate' => $this->calculateClientRetentionRate($company, $startDate, $endDate),
        ];
    }

    /**
     * Get financial metrics
     */
    public function getFinancialMetrics(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $totalRevenue = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->sum('commission_amount');

        $revenueByType = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->selectRaw('exchange_type, SUM(commission_amount) as revenue')
            ->groupBy('exchange_type')
            ->get()
            ->pluck('revenue', fn($item) => $item->exchange_type->value);

        $dailyRevenue = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->selectRaw('DATE(exchange_date) as date, SUM(commission_amount) as revenue')
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('revenue', 'date');

        $profitMargins = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->selectRaw('
                exchange_type,
                AVG((commission_amount / from_amount) * 100) as avg_margin,
                MIN((commission_amount / from_amount) * 100) as min_margin,
                MAX((commission_amount / from_amount) * 100) as max_margin
            ')
            ->groupBy('exchange_type')
            ->get()
            ->keyBy(fn($item) => $item->exchange_type->value);

        return [
            'total_revenue' => $totalRevenue,
            'revenue_by_type' => $revenueByType,
            'daily_revenue' => $dailyRevenue,
            'profit_margins' => $profitMargins,
            'avg_commission_rate' => $this->calculateAverageCommissionRate($company, $startDate, $endDate),
        ];
    }

    /**
     * Get operational metrics
     */
    public function getOperationalMetrics(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $deliveryMetrics = [
            'total_deliveries' => Delivery::where('company_id', $company->id)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count(),
            'completed_deliveries' => Delivery::where('company_id', $company->id)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->where('status', 'delivered')
                ->count(),
            'avg_delivery_time' => $this->calculateAverageDeliveryTime($company, $startDate, $endDate),
        ];

        $balanceMetrics = [
            'total_client_balances' => ClientBalance::where('company_id', $company->id)
                ->sum('balance'),
            'negative_balances' => ClientBalance::where('company_id', $company->id)
                ->where('balance', '<', 0)
                ->count(),
            'currency_exposure' => $this->getCurrencyExposure($company),
        ];

        return [
            'delivery' => $deliveryMetrics,
            'balance' => $balanceMetrics,
            'processing_times' => $this->getProcessingTimeMetrics($company, $startDate, $endDate),
        ];
    }

    /**
     * Get trend analysis
     */
    public function getTrendAnalysis(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $dailyTrends = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->selectRaw('
                DATE(exchange_date) as date,
                COUNT(*) as transaction_count,
                SUM(from_amount) as volume,
                SUM(commission_amount) as revenue
            ')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $weeklyTrends = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->selectRaw('
                YEARWEEK(exchange_date) as week,
                COUNT(*) as transaction_count,
                SUM(from_amount) as volume,
                SUM(commission_amount) as revenue
            ')
            ->groupBy('week')
            ->orderBy('week')
            ->get();

        return [
            'daily' => $dailyTrends,
            'weekly' => $weeklyTrends,
            'growth_forecast' => $this->calculateGrowthForecast($dailyTrends),
        ];
    }

    /**
     * Calculate growth rate between two values
     */
    private function calculateGrowthRate(float $current, float $previous): float
    {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }

        return (($current - $previous) / $previous) * 100;
    }

    /**
     * Calculate transaction completion rate
     */
    private function calculateCompletionRate(Company $company, Carbon $startDate, Carbon $endDate): float
    {
        $total = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->count();

        $completed = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->where('status', 'completed')
            ->count();

        return $total > 0 ? ($completed / $total) * 100 : 0;
    }

    /**
     * Calculate client retention rate
     */
    private function calculateClientRetentionRate(Company $company, Carbon $startDate, Carbon $endDate): float
    {
        $previousPeriodStart = $startDate->copy()->subDays($startDate->diffInDays($endDate) + 1);
        $previousPeriodEnd = $startDate->copy()->subDay();

        $previousClients = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$previousPeriodStart, $previousPeriodEnd])
            ->distinct('client_id')
            ->pluck('client_id');

        $currentClients = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->distinct('client_id')
            ->pluck('client_id');

        $retainedClients = $previousClients->intersect($currentClients)->count();

        return $previousClients->count() > 0 ? ($retainedClients / $previousClients->count()) * 100 : 0;
    }

    /**
     * Calculate average commission rate
     */
    private function calculateAverageCommissionRate(Company $company, Carbon $startDate, Carbon $endDate): float
    {
        return CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->where('from_amount', '>', 0)
            ->selectRaw('AVG((commission_amount / from_amount) * 100) as avg_rate')
            ->value('avg_rate') ?? 0;
    }

    /**
     * Calculate average delivery time in minutes
     */
    private function calculateAverageDeliveryTime(Company $company, Carbon $startDate, Carbon $endDate): ?float
    {
        $deliveries = Delivery::where('company_id', $company->id)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->whereNotNull('assigned_at')
            ->whereNotNull('delivered_at')
            ->get();

        if ($deliveries->isEmpty()) {
            return null;
        }

        $totalMinutes = $deliveries->sum(function ($delivery) {
            return $delivery->assigned_at->diffInMinutes($delivery->delivered_at);
        });

        return $totalMinutes / $deliveries->count();
    }

    /**
     * Get currency exposure
     */
    private function getCurrencyExposure(Company $company): Collection
    {
        return ClientBalance::where('company_id', $company->id)
            ->selectRaw('currency_code, SUM(balance) as total_balance')
            ->groupBy('currency_code')
            ->orderByDesc('total_balance')
            ->get();
    }

    /**
     * Get processing time metrics
     */
    private function getProcessingTimeMetrics(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $exchanges = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->whereNotNull('settlement_date')
            ->get();

        if ($exchanges->isEmpty()) {
            return [
                'avg_processing_time' => null,
                'min_processing_time' => null,
                'max_processing_time' => null,
            ];
        }

        $processingTimes = $exchanges->map(function ($exchange) {
            return $exchange->exchange_date->diffInMinutes($exchange->settlement_date);
        });

        return [
            'avg_processing_time' => $processingTimes->avg(),
            'min_processing_time' => $processingTimes->min(),
            'max_processing_time' => $processingTimes->max(),
        ];
    }

    /**
     * Calculate growth forecast based on historical data
     */
    private function calculateGrowthForecast(Collection $dailyTrends): array
    {
        if ($dailyTrends->count() < 7) {
            return ['forecast' => null, 'confidence' => 0];
        }

        $revenues = $dailyTrends->pluck('revenue')->toArray();
        $n = count($revenues);
        
        // Simple linear regression for trend
        $sumX = array_sum(range(1, $n));
        $sumY = array_sum($revenues);
        $sumXY = 0;
        $sumX2 = 0;

        for ($i = 0; $i < $n; $i++) {
            $x = $i + 1;
            $y = $revenues[$i];
            $sumXY += $x * $y;
            $sumX2 += $x * $x;
        }

        $slope = ($n * $sumXY - $sumX * $sumY) / ($n * $sumX2 - $sumX * $sumX);
        $intercept = ($sumY - $slope * $sumX) / $n;

        // Forecast next 7 days
        $forecast = [];
        for ($i = 1; $i <= 7; $i++) {
            $forecast[] = $slope * ($n + $i) + $intercept;
        }

        return [
            'forecast' => $forecast,
            'trend' => $slope > 0 ? 'increasing' : ($slope < 0 ? 'decreasing' : 'stable'),
            'confidence' => min(90, max(10, 70 + ($n - 7) * 2)), // Confidence increases with more data
        ];
    }
}
