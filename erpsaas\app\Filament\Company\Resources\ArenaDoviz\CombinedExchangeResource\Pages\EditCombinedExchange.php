<?php

namespace App\Filament\Company\Resources\ArenaDoviz\CombinedExchangeResource\Pages;

use App\Filament\Company\Resources\ArenaDoviz\CombinedExchangeResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditCombinedExchange extends EditRecord
{
    protected static string $resource = CombinedExchangeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
