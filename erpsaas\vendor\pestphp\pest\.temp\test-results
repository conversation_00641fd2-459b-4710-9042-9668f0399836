{"version": "pest_3.8.4", "defects": {"P\\Tests\\Feature\\Accounting\\TransactionTest::__pest_evaluable_it_creates_correct_journal_entries_for_a_deposit_transaction": 8, "P\\Tests\\Feature\\Accounting\\TransactionTest::__pest_evaluable_it_creates_correct_journal_entries_for_a_withdrawal_transaction": 8, "P\\Tests\\Feature\\Accounting\\TransactionTest::__pest_evaluable_it_creates_correct_journal_entries_for_a_transfer_transaction": 8, "P\\Tests\\Feature\\Accounting\\TransactionTest::__pest_evaluable_it_does_not_create_journal_entries_for_a_journal_transaction": 8, "P\\Tests\\Feature\\Accounting\\TransactionTest::__pest_evaluable_it_stores_and_sums_correct_debit_and_credit_amounts_for_different_transaction_types with data set \"('asDeposit', 'forUncategorizedRevenue', 2000)\"": 8, "P\\Tests\\Feature\\Accounting\\TransactionTest::__pest_evaluable_it_stores_and_sums_correct_debit_and_credit_amounts_for_different_transaction_types with data set \"('asWithdrawal', 'forUncategorizedExpense', 500)\"": 8, "P\\Tests\\Feature\\Accounting\\TransactionTest::__pest_evaluable_it_stores_and_sums_correct_debit_and_credit_amounts_for_different_transaction_types with data set \"('asTransfer', 'forDestinationBankAccount', 1500)\"": 8, "P\\Tests\\Feature\\Accounting\\TransactionTest::__pest_evaluable_it_deletes_associated_journal_entries_when_transaction_is_deleted": 8, "P\\Tests\\Feature\\Accounting\\TransactionTest::__pest_evaluable_it_handles_multi_currency_transfers_without_conversion_when_the_source_bank_account_is_in_the_default_currency": 8, "P\\Tests\\Feature\\Accounting\\TransactionTest::__pest_evaluable_it_handles_multi_currency_transfers_correctly": 8, "P\\Tests\\Feature\\Accounting\\TransactionTest::__pest_evaluable_it_handles_multi_currency_deposits_correctly": 8, "P\\Tests\\Feature\\Accounting\\TransactionTest::__pest_evaluable_it_handles_multi_currency_withdrawals_correctly": 8, "P\\Tests\\Feature\\Accounting\\TransactionTest::__pest_evaluable_it_can_add_an_income_or_expense_transaction with data set \"(App\\Enums\\Accounting\\TransactionType Enum (Deposit, 'deposit'), 'createDeposit')\"": 8, "P\\Tests\\Feature\\Accounting\\TransactionTest::__pest_evaluable_it_can_add_an_income_or_expense_transaction with data set \"(App\\Enums\\Accounting\\TransactionType Enum (Withdrawal, 'withdrawal'), 'createWithdrawal')\"": 8, "P\\Tests\\Feature\\Accounting\\TransactionTest::__pest_evaluable_it_can_add_a_transfer_transaction": 8, "P\\Tests\\Feature\\Accounting\\TransactionTest::__pest_evaluable_it_can_add_a_journal_transaction": 8, "P\\Tests\\Feature\\Accounting\\TransactionTest::__pest_evaluable_it_can_update_a_deposit_or_withdrawal_transaction with data set \"(App\\Enums\\Accounting\\TransactionType Enum (Deposit, 'deposit'))\"": 8, "P\\Tests\\Feature\\Accounting\\TransactionTest::__pest_evaluable_it_can_update_a_deposit_or_withdrawal_transaction with data set \"(App\\Enums\\Accounting\\TransactionType Enum (Withdrawal, 'withdrawal'))\"": 8, "P\\Tests\\Feature\\Accounting\\TransactionTest::__pest_evaluable_it_can_update_a_transfer_transaction": 8, "P\\Tests\\Feature\\Accounting\\TransactionTest::__pest_evaluable_it_replicates_a_transaction_with_correct_journal_entries": 8, "P\\Tests\\Feature\\Accounting\\TransactionTest::__pest_evaluable_it_bulk_replicates_transactions_with_correct_journal_entries": 8, "P\\Tests\\Feature\\Accounting\\TransactionTest::__pest_evaluable_it_can_delete_a_transaction_with_journal_entries": 8, "P\\Tests\\Feature\\Accounting\\TransactionTest::__pest_evaluable_it_can_bulk_delete_transactions_with_journal_entries": 8, "Tests\\Unit\\ArenaDoviz\\NumberFormatterTest::test_format_currency_with_irr": 7, "Tests\\Unit\\ArenaDoviz\\NumberFormatterTest::test_format_compact_number": 7, "Tests\\Unit\\ArenaDoviz\\CommissionCalculationServiceTest::test_calculate_commission_with_percentage_rule": 8, "Tests\\Unit\\ArenaDoviz\\CommissionCalculationServiceTest::test_calculate_commission_with_fixed_rule": 8, "Tests\\Unit\\ArenaDoviz\\CommissionCalculationServiceTest::test_calculate_commission_with_hybrid_rule": 8, "Tests\\Unit\\ArenaDoviz\\CommissionCalculationServiceTest::test_calculate_commission_with_min_commission_limit": 8, "Tests\\Unit\\ArenaDoviz\\CommissionCalculationServiceTest::test_calculate_commission_with_max_commission_limit": 8, "Tests\\Unit\\ArenaDoviz\\CommissionCalculationServiceTest::test_calculate_commission_with_pre_conversion_method": 8, "Tests\\Unit\\ArenaDoviz\\CommissionCalculationServiceTest::test_calculate_commission_with_default_rule_when_no_matches": 8, "Tests\\Unit\\ArenaDoviz\\CommissionCalculationServiceTest::test_calculate_commission_with_priority_rules": 8, "Tests\\Unit\\ArenaDoviz\\CommissionCalculationServiceTest::test_calculate_commission_ignores_inactive_rules": 8, "Tests\\Unit\\ArenaDoviz\\CommissionCalculationServiceTest::test_validate_commission_with_valid_commission": 8, "Tests\\Unit\\ArenaDoviz\\CommissionCalculationServiceTest::test_validate_commission_with_high_rate": 8, "Tests\\Unit\\ArenaDoviz\\CommissionCalculationServiceTest::test_validate_commission_with_negative_amount": 8, "P\\Tests\\Unit\\ExampleTest::__pest_evaluable_true_is_true": 8}, "times": {"Tests\\Unit\\ArenaDoviz\\NumberFormatterTest::test_format_number_with_default_parameters": 0.002, "Tests\\Unit\\ArenaDoviz\\NumberFormatterTest::test_format_number_with_custom_decimals": 0, "Tests\\Unit\\ArenaDoviz\\NumberFormatterTest::test_format_number_with_zero_decimals": 0, "Tests\\Unit\\ArenaDoviz\\NumberFormatterTest::test_format_number_with_null_value": 0, "Tests\\Unit\\ArenaDoviz\\NumberFormatterTest::test_format_number_with_empty_string": 0, "Tests\\Unit\\ArenaDoviz\\NumberFormatterTest::test_format_currency_with_usd": 0, "Tests\\Unit\\ArenaDoviz\\NumberFormatterTest::test_format_currency_with_eur": 0, "Tests\\Unit\\ArenaDoviz\\NumberFormatterTest::test_format_currency_with_try": 0, "Tests\\Unit\\ArenaDoviz\\NumberFormatterTest::test_format_currency_with_irr": 0, "Tests\\Unit\\ArenaDoviz\\NumberFormatterTest::test_format_currency_with_location_code": 0, "Tests\\Unit\\ArenaDoviz\\NumberFormatterTest::test_parse_number_with_formatted_string": 0, "Tests\\Unit\\ArenaDoviz\\NumberFormatterTest::test_parse_number_with_unformatted_string": 0, "Tests\\Unit\\ArenaDoviz\\NumberFormatterTest::test_parse_number_with_empty_string": 0, "Tests\\Unit\\ArenaDoviz\\NumberFormatterTest::test_extract_base_currency": 0, "Tests\\Unit\\ArenaDoviz\\NumberFormatterTest::test_extract_base_currency_without_location": 0, "Tests\\Unit\\ArenaDoviz\\NumberFormatterTest::test_get_currency_symbol": 0, "Tests\\Unit\\ArenaDoviz\\NumberFormatterTest::test_format_exchange_rate": 0, "Tests\\Unit\\ArenaDoviz\\NumberFormatterTest::test_format_percentage": 0, "Tests\\Unit\\ArenaDoviz\\NumberFormatterTest::test_format_iranian_rial": 0, "Tests\\Unit\\ArenaDoviz\\NumberFormatterTest::test_format_by_currency_with_irr": 0, "Tests\\Unit\\ArenaDoviz\\NumberFormatterTest::test_format_by_currency_with_usd": 0, "Tests\\Unit\\ArenaDoviz\\NumberFormatterTest::test_get_decimal_places_for_irr": 0, "Tests\\Unit\\ArenaDoviz\\NumberFormatterTest::test_get_decimal_places_for_usd": 0, "Tests\\Unit\\ArenaDoviz\\NumberFormatterTest::test_format_for_input": 0.001, "Tests\\Unit\\ArenaDoviz\\NumberFormatterTest::test_is_valid_formatted_number": 0.001, "Tests\\Unit\\ArenaDoviz\\NumberFormatterTest::test_format_compact_number": 0}}