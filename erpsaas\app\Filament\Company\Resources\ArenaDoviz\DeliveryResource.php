<?php

namespace App\Filament\Company\Resources\ArenaDoviz;

use App\Filament\Company\Resources\ArenaDoviz\DeliveryResource\Pages;
use App\Models\ArenaDoviz\Delivery;
use App\Enums\ArenaDoviz\DeliveryStatus;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class DeliveryResource extends Resource
{
    protected static ?string $model = Delivery::class;
    protected static ?string $navigationIcon = 'heroicon-o-truck';
    protected static ?string $navigationLabel = 'Deliveries';
    protected static ?string $modelLabel = 'Delivery';
    protected static ?string $pluralModelLabel = 'Deliveries';
    protected static ?string $navigationGroup = 'Arena Doviz';
    protected static ?int $navigationSort = 4;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('currency_exchange_id')
                    ->relationship('currencyExchange', 'id')
                    ->required()
                    ->searchable()
                    ->preload(),

                Forms\Components\Select::make('courier_id')
                    ->relationship('courier', 'name')
                    ->searchable()
                    ->preload(),

                Forms\Components\Select::make('status')
                    ->options(DeliveryStatus::class)
                    ->required()
                    ->default(DeliveryStatus::PENDING),

                Forms\Components\TextInput::make('delivery_address')
                    ->required()
                    ->maxLength(500)
                    ->columnSpanFull(),

                Forms\Components\TextInput::make('recipient_name')
                    ->required()
                    ->maxLength(255),

                Forms\Components\TextInput::make('recipient_phone')
                    ->tel()
                    ->maxLength(20),

                Forms\Components\DateTimePicker::make('scheduled_at')
                    ->label('Scheduled Delivery Time'),

                Forms\Components\DateTimePicker::make('assigned_at')
                    ->label('Assigned Time'),

                Forms\Components\DateTimePicker::make('delivered_at')
                    ->label('Delivered Time'),

                Forms\Components\Textarea::make('delivery_notes')
                    ->maxLength(1000)
                    ->columnSpanFull(),

                Forms\Components\FileUpload::make('proof_of_delivery')
                    ->image()
                    ->directory('deliveries/proof')
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('Delivery #')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('currencyExchange.client.name')
                    ->label('Client')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('courier.name')
                    ->label('Courier')
                    ->searchable()
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('recipient_name')
                    ->label('Recipient')
                    ->searchable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('scheduled_at')
                    ->label('Scheduled')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('delivered_at')
                    ->label('Delivered')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options(DeliveryStatus::class),

                Tables\Filters\SelectFilter::make('courier_id')
                    ->relationship('courier', 'name')
                    ->label('Courier'),

                Tables\Filters\Filter::make('scheduled_today')
                    ->query(fn (Builder $query): Builder => $query->whereDate('scheduled_at', today()))
                    ->label('Scheduled Today'),

                Tables\Filters\Filter::make('overdue')
                    ->query(fn (Builder $query): Builder => $query->where('scheduled_at', '<', now())->where('status', '!=', DeliveryStatus::DELIVERED))
                    ->label('Overdue'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                
                Tables\Actions\Action::make('mark_delivered')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->action(function (Delivery $record) {
                        $record->update([
                            'status' => DeliveryStatus::DELIVERED,
                            'delivered_at' => now(),
                        ]);
                    })
                    ->requiresConfirmation()
                    ->visible(fn (Delivery $record) => $record->status !== DeliveryStatus::DELIVERED),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDeliveries::route('/'),
            'create' => Pages\CreateDelivery::route('/create'),
            'view' => Pages\ViewDelivery::route('/{record}'),
            'edit' => Pages\EditDelivery::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('status', DeliveryStatus::PENDING)->count() ?: null;
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return static::getModel()::where('status', DeliveryStatus::PENDING)->count() > 0 ? 'warning' : null;
    }
}
