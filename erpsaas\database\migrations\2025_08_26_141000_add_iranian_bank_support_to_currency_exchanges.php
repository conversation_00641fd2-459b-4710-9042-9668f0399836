<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('currency_exchanges', function (Blueprint $table) {
            // Add Iranian bank support
            $table->foreignId('iranian_bank_id')->nullable()->after('location_id')
                ->constrained('iranian_banks')->nullOnDelete();
            
            // Add Iranian banking specific fields
            $table->string('iranian_account_number', 50)->nullable()->after('reference_number');
            $table->string('iranian_card_number', 20)->nullable()->after('iranian_account_number');
            $table->string('iranian_tracking_code', 50)->nullable()->after('iranian_card_number');
            $table->timestamp('iranian_transfer_date')->nullable()->after('iranian_tracking_code');
            $table->decimal('iranian_transfer_fee', 10, 2)->nullable()->after('iranian_transfer_date');
            $table->text('iranian_transfer_notes')->nullable()->after('iranian_transfer_fee');
            
            // Add batch processing support
            $table->string('batch_id', 50)->nullable()->after('iranian_transfer_notes');
            $table->integer('batch_sequence')->nullable()->after('batch_id');
            $table->timestamp('batch_processed_at')->nullable()->after('batch_sequence');
            
            // Add indexes for Iranian banking queries
            $table->index('iranian_bank_id');
            $table->index('iranian_tracking_code');
            $table->index('batch_id');
            $table->index(['batch_id', 'batch_sequence']);
            $table->index('iranian_transfer_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('currency_exchanges', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['iranian_bank_id']);
            $table->dropIndex(['iranian_tracking_code']);
            $table->dropIndex(['batch_id']);
            $table->dropIndex(['batch_id', 'batch_sequence']);
            $table->dropIndex(['iranian_transfer_date']);
            
            // Drop foreign key constraint
            $table->dropForeign(['iranian_bank_id']);
            
            // Drop columns
            $table->dropColumn([
                'iranian_bank_id',
                'iranian_account_number',
                'iranian_card_number',
                'iranian_tracking_code',
                'iranian_transfer_date',
                'iranian_transfer_fee',
                'iranian_transfer_notes',
                'batch_id',
                'batch_sequence',
                'batch_processed_at',
            ]);
        });
    }
};
