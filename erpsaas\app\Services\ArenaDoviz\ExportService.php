<?php

namespace App\Services\ArenaDoviz;

use App\Models\ArenaDoviz\CurrencyExchange;
use App\Models\ArenaDoviz\LocationCurrency;
use App\Models\ArenaDoviz\Location;
use App\Models\ArenaDoviz\DebitNote;
use App\Models\ArenaDoviz\CombinedExchange;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;
// Note: PhpSpreadsheet not available, using basic CSV export

class ExportService
{
    /**
     * Export currency exchanges to CSV
     */
    public function exportCurrencyExchanges(array $filters = [], string $format = 'csv'): string
    {
        $query = CurrencyExchange::with(['client', 'location', 'iranianBank'])
            ->orderBy('created_at', 'desc');

        // Apply filters
        if (!empty($filters['date_from'])) {
            $query->whereDate('exchange_date', '>=', $filters['date_from']);
        }
        
        if (!empty($filters['date_to'])) {
            $query->whereDate('exchange_date', '<=', $filters['date_to']);
        }
        
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }
        
        if (!empty($filters['location_id'])) {
            $query->where('location_id', $filters['location_id']);
        }

        $exchanges = $query->get();

        // Prepare CSV data
        $csvData = [];

        // Headers
        $csvData[] = [
            'Exchange Number', 'Date', 'Status', 'Client', 'Location',
            'From Currency', 'To Currency', 'From Amount', 'To Amount',
            'Exchange Rate', 'Commission Rate', 'Commission Amount',
            'Net Amount', 'Iranian Bank', 'Reference Number', 'Notes'
        ];

        // Data rows
        foreach ($exchanges as $exchange) {
            $csvData[] = [
                $exchange->exchange_number,
                $exchange->exchange_date->format('Y-m-d H:i:s'),
                $exchange->status->value,
                $exchange->client->name ?? 'N/A',
                $exchange->location->name ?? 'N/A',
                $exchange->from_currency_code,
                $exchange->to_currency_code,
                $exchange->from_amount,
                $exchange->to_amount,
                $exchange->exchange_rate,
                $exchange->commission_rate,
                $exchange->commission_amount,
                $exchange->net_amount,
                $exchange->iranianBank->name ?? 'N/A',
                $exchange->reference_number,
                $exchange->notes,
            ];
        }

        return $this->saveCsvData($csvData, 'currency_exchanges');
    }

    /**
     * Export location currencies (rates) to CSV
     */
    public function exportLocationCurrencies(string $format = 'csv'): string
    {
        $currencies = LocationCurrency::with('location')
            ->where('is_active', true)
            ->orderBy('location_currency_code')
            ->get();

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('Currency Rates');

        // Headers
        $headers = [
            'Currency Code', 'Location', 'Base Currency', 'Name', 'Symbol',
            'Buy Rate', 'Sell Rate', 'Mid Rate', 'Rate Date', 'Last Updated',
            'Decimal Places', 'Active'
        ];

        $sheet->fromArray($headers, null, 'A1');

        // Data
        $row = 2;
        foreach ($currencies as $currency) {
            $data = [
                $currency->location_currency_code,
                $currency->location->name ?? 'N/A',
                $currency->base_currency_code,
                $currency->name,
                $currency->symbol,
                $currency->buy_rate,
                $currency->sell_rate,
                $currency->mid_rate,
                $currency->rate_date?->format('Y-m-d H:i:s'),
                $currency->last_updated?->format('Y-m-d H:i:s'),
                $currency->decimal_places,
                $currency->is_active ? 'Yes' : 'No',
            ];
            
            $sheet->fromArray($data, null, "A{$row}");
            $row++;
        }

        // Auto-size columns
        foreach (range('A', 'L') as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }

        return $this->saveSpreadsheet($spreadsheet, 'currency_rates', $format);
    }

    /**
     * Export profit analysis data
     */
    public function exportProfitAnalysis(array $filters = [], string $format = 'xlsx'): string
    {
        $query = CombinedExchange::with(['location', 'buyClient', 'sellClient'])
            ->orderBy('exchange_date', 'desc');

        // Apply filters
        if (!empty($filters['date_from'])) {
            $query->whereDate('exchange_date', '>=', $filters['date_from']);
        }
        
        if (!empty($filters['date_to'])) {
            $query->whereDate('exchange_date', '<=', $filters['date_to']);
        }

        $exchanges = $query->get();

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('Profit Analysis');

        // Headers
        $headers = [
            'Exchange Number', 'Date', 'Location', 'Currency',
            'Buy Client', 'Sell Client', 'Buy Amount', 'Sell Amount',
            'Buy Rate', 'Sell Rate', 'Profit Amount', 'Profit Margin %',
            'Commission', 'Net Profit', 'Status'
        ];

        $sheet->fromArray($headers, null, 'A1');

        // Data
        $row = 2;
        foreach ($exchanges as $exchange) {
            $data = [
                $exchange->combined_exchange_number,
                $exchange->exchange_date->format('Y-m-d H:i:s'),
                $exchange->location->name ?? 'N/A',
                $exchange->currency_code,
                $exchange->buyClient->name ?? 'N/A',
                $exchange->sellClient->name ?? 'N/A',
                $exchange->buy_amount,
                $exchange->sell_amount,
                $exchange->buy_rate,
                $exchange->sell_rate,
                $exchange->profit_amount,
                $exchange->profit_margin,
                $exchange->total_commission,
                $exchange->net_profit,
                $exchange->status->value,
            ];
            
            $sheet->fromArray($data, null, "A{$row}");
            $row++;
        }

        // Auto-size columns
        foreach (range('A', 'O') as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }

        return $this->saveSpreadsheet($spreadsheet, 'profit_analysis', $format);
    }

    /**
     * Export debit notes
     */
    public function exportDebitNotes(array $filters = [], string $format = 'xlsx'): string
    {
        $query = DebitNote::with(['client', 'location'])
            ->orderBy('created_at', 'desc');

        // Apply filters
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }
        
        if (!empty($filters['location_id'])) {
            $query->where('location_id', $filters['location_id']);
        }

        $debitNotes = $query->get();

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('Debit Notes');

        // Headers
        $headers = [
            'Debit Note Number', 'Client', 'Location', 'Status', 'Type',
            'Currency', 'Amount', 'Recipient Name', 'Recipient Phone',
            'Delivery Address', 'Scheduled Date', 'Completed Date',
            'Description', 'Notes'
        ];

        $sheet->fromArray($headers, null, 'A1');

        // Data
        $row = 2;
        foreach ($debitNotes as $note) {
            $data = [
                $note->debit_note_number,
                $note->client->name ?? 'N/A',
                $note->location->name ?? 'N/A',
                $note->status->value,
                $note->debit_type,
                $note->currency_code,
                $note->amount,
                $note->recipient_name,
                $note->recipient_phone,
                $note->delivery_address,
                $note->scheduled_date?->format('Y-m-d H:i:s'),
                $note->completed_date?->format('Y-m-d H:i:s'),
                $note->description,
                $note->client_notes,
            ];
            
            $sheet->fromArray($data, null, "A{$row}");
            $row++;
        }

        // Auto-size columns
        foreach (range('A', 'N') as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }

        return $this->saveSpreadsheet($spreadsheet, 'debit_notes', $format);
    }

    /**
     * Save CSV data to storage and return path
     */
    private function saveCsvData(array $data, string $filename): string
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $fullFilename = "{$filename}_{$timestamp}.csv";
        $path = "exports/arena-doviz/{$fullFilename}";

        // Ensure directory exists
        Storage::makeDirectory('exports/arena-doviz');

        // Create CSV content
        $csvContent = '';
        foreach ($data as $row) {
            $csvContent .= $this->arrayToCsvLine($row) . "\n";
        }

        Storage::put($path, $csvContent);

        return $path;
    }

    /**
     * Convert array to CSV line
     */
    private function arrayToCsvLine(array $data): string
    {
        $output = fopen('php://temp', 'r+');
        fputcsv($output, $data);
        rewind($output);
        $csvLine = stream_get_contents($output);
        fclose($output);

        return rtrim($csvLine, "\n");
    }

    /**
     * Get export file URL
     */
    public function getExportUrl(string $path): string
    {
        return Storage::url($path);
    }

    /**
     * Clean up old export files
     */
    public function cleanupOldExports(int $daysOld = 7): int
    {
        $files = Storage::files('exports/arena-doviz');
        $deletedCount = 0;
        $cutoffDate = now()->subDays($daysOld);

        foreach ($files as $file) {
            $lastModified = Storage::lastModified($file);
            
            if ($lastModified < $cutoffDate->timestamp) {
                Storage::delete($file);
                $deletedCount++;
            }
        }

        return $deletedCount;
    }
}
