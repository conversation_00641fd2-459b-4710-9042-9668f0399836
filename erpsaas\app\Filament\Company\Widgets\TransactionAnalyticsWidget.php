<?php

namespace App\Filament\Company\Widgets;

use App\Services\ArenaDoviz\DashboardAnalyticsService;
use Filament\Widgets\ChartWidget;
use Carbon\Carbon;

class TransactionAnalyticsWidget extends ChartWidget
{
    protected static ?string $heading = 'Transaction Analytics';
    protected static ?string $description = 'Transaction volume and count trends over time';
    protected static ?int $sort = 2;
    protected int | string | array $columnSpan = 'full';
    protected static ?string $pollingInterval = '30s';

    private function getAnalyticsService(): DashboardAnalyticsService
    {
        return app(DashboardAnalyticsService::class);
    }

    protected function getData(): array
    {
        $company = auth()->user()->currentCompany;
        $startDate = now()->subDays(30);
        $endDate = now();

        $metrics = $this->getAnalyticsService()->getDashboardMetrics($company, $startDate, $endDate);
        $dailyTrends = $metrics['trends']['daily'];

        $labels = $dailyTrends->pluck('date')->map(function ($date) {
            return Carbon::parse($date)->format('M d');
        })->toArray();

        $transactionCounts = $dailyTrends->pluck('transaction_count')->toArray();
        $volumes = $dailyTrends->pluck('volume')->toArray();
        $revenues = $dailyTrends->pluck('revenue')->toArray();

        return [
            'datasets' => [
                [
                    'label' => 'Transaction Count',
                    'data' => $transactionCounts,
                    'borderColor' => 'rgb(59, 130, 246)',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'yAxisID' => 'y',
                ],
                [
                    'label' => 'Volume (TRY)',
                    'data' => $volumes,
                    'borderColor' => 'rgb(16, 185, 129)',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                    'yAxisID' => 'y1',
                ],
                [
                    'label' => 'Revenue (TRY)',
                    'data' => $revenues,
                    'borderColor' => 'rgb(245, 158, 11)',
                    'backgroundColor' => 'rgba(245, 158, 11, 0.1)',
                    'yAxisID' => 'y1',
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'responsive' => true,
            'interaction' => [
                'mode' => 'index',
                'intersect' => false,
            ],
            'scales' => [
                'x' => [
                    'display' => true,
                    'title' => [
                        'display' => true,
                        'text' => 'Date',
                    ],
                ],
                'y' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'left',
                    'title' => [
                        'display' => true,
                        'text' => 'Transaction Count',
                    ],
                ],
                'y1' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'right',
                    'title' => [
                        'display' => true,
                        'text' => 'Amount (TRY)',
                    ],
                    'grid' => [
                        'drawOnChartArea' => false,
                    ],
                ],
            ],
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'mode' => 'index',
                    'intersect' => false,
                ],
            ],
        ];
    }
}
