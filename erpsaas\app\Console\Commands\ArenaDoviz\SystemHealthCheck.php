<?php

namespace App\Console\Commands\ArenaDoviz;

use App\Models\ArenaDoviz\Location;
use App\Models\ArenaDoviz\LocationCurrency;
use App\Models\ArenaDoviz\CommissionRule;
use App\Models\ArenaDoviz\IranianBank;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class SystemHealthCheck extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'arena-doviz:health-check {--fix : Automatically fix issues where possible}';

    /**
     * The console command description.
     */
    protected $description = 'Check Arena Doviz system health and fix common issues';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Arena Doviz System Health Check');
        $this->info('=====================================');

        $issues = [];
        $fixes = [];

        // Check database connectivity
        $this->info('📊 Checking database connectivity...');
        try {
            DB::connection()->getPdo();
            $this->info('✅ Database connection: OK');
        } catch (\Exception $e) {
            $issues[] = 'Database connection failed: ' . $e->getMessage();
            $this->error('❌ Database connection: FAILED');
        }

        // Check required tables exist
        $this->info('🗄️ Checking required tables...');
        $requiredTables = [
            'locations',
            'location_currencies', 
            'iranian_banks',
            'debit_notes',
            'commission_rules',
            'currency_exchanges'
        ];

        foreach ($requiredTables as $table) {
            if (Schema::hasTable($table)) {
                $this->info("✅ Table '{$table}': EXISTS");
            } else {
                $issues[] = "Missing table: {$table}";
                $this->error("❌ Table '{$table}': MISSING");
            }
        }

        // Check data integrity
        $this->info('🔍 Checking data integrity...');
        
        // Check locations
        $locationCount = Location::count();
        if ($locationCount > 0) {
            $this->info("✅ Locations: {$locationCount} records");
        } else {
            $issues[] = 'No locations found';
            $this->warn('⚠️ Locations: No records found');
            if ($this->option('fix')) {
                $fixes[] = 'Run LocationSeeder';
            }
        }

        // Check location currencies
        $currencyCount = LocationCurrency::count();
        if ($currencyCount > 0) {
            $this->info("✅ Location Currencies: {$currencyCount} records");
        } else {
            $issues[] = 'No location currencies found';
            $this->warn('⚠️ Location Currencies: No records found');
            if ($this->option('fix')) {
                $fixes[] = 'Run LocationCurrencySeeder';
            }
        }

        // Check Iranian banks
        $bankCount = IranianBank::count();
        if ($bankCount > 0) {
            $this->info("✅ Iranian Banks: {$bankCount} records");
        } else {
            $issues[] = 'No Iranian banks found';
            $this->warn('⚠️ Iranian Banks: No records found');
            if ($this->option('fix')) {
                $fixes[] = 'Run IranianBankSeeder';
            }
        }

        // Check commission rules
        $ruleCount = CommissionRule::count();
        if ($ruleCount > 0) {
            $this->info("✅ Commission Rules: {$ruleCount} records");
        } else {
            $issues[] = 'No commission rules found';
            $this->warn('⚠️ Commission Rules: No records found');
            if ($this->option('fix')) {
                $fixes[] = 'Run CommissionRuleSeeder';
            }
        }

        // Check for orphaned records
        $this->info('🔗 Checking for orphaned records...');
        
        // Check location currencies without locations
        $orphanedCurrencies = LocationCurrency::whereNotIn('location_id', Location::pluck('id'))->count();
        if ($orphanedCurrencies > 0) {
            $issues[] = "Found {$orphanedCurrencies} orphaned location currencies";
            $this->warn("⚠️ Orphaned Location Currencies: {$orphanedCurrencies}");
            if ($this->option('fix')) {
                $fixes[] = 'Clean up orphaned location currencies';
            }
        } else {
            $this->info('✅ Location Currencies: No orphaned records');
        }

        // Check file permissions
        $this->info('📁 Checking file permissions...');
        $storageWritable = is_writable(storage_path());
        if ($storageWritable) {
            $this->info('✅ Storage directory: Writable');
        } else {
            $issues[] = 'Storage directory is not writable';
            $this->error('❌ Storage directory: Not writable');
        }

        // Check cache
        $this->info('🗂️ Checking cache...');
        try {
            cache()->put('health_check', 'test', 60);
            $cacheValue = cache()->get('health_check');
            if ($cacheValue === 'test') {
                $this->info('✅ Cache: Working');
                cache()->forget('health_check');
            } else {
                $issues[] = 'Cache is not working properly';
                $this->error('❌ Cache: Not working');
            }
        } catch (\Exception $e) {
            $issues[] = 'Cache error: ' . $e->getMessage();
            $this->error('❌ Cache: Error - ' . $e->getMessage());
        }

        // Apply fixes if requested
        if ($this->option('fix') && !empty($fixes)) {
            $this->info('🔧 Applying fixes...');
            
            foreach ($fixes as $fix) {
                $this->info("Applying: {$fix}");
                
                switch ($fix) {
                    case 'Run LocationSeeder':
                        $this->call('db:seed', ['--class' => 'Database\\Seeders\\ArenaDoviz\\LocationSeeder']);
                        break;
                    case 'Run LocationCurrencySeeder':
                        $this->call('db:seed', ['--class' => 'Database\\Seeders\\ArenaDoviz\\LocationCurrencySeeder']);
                        break;
                    case 'Run IranianBankSeeder':
                        $this->call('db:seed', ['--class' => 'Database\\Seeders\\ArenaDoviz\\IranianBankSeeder']);
                        break;
                    case 'Run CommissionRuleSeeder':
                        $this->call('db:seed', ['--class' => 'Database\\Seeders\\ArenaDoviz\\CommissionRuleSeeder']);
                        break;
                    case 'Clean up orphaned location currencies':
                        LocationCurrency::whereNotIn('location_id', Location::pluck('id'))->delete();
                        $this->info('Cleaned up orphaned location currencies');
                        break;
                }
            }
        }

        // Summary
        $this->info('');
        $this->info('📋 Health Check Summary');
        $this->info('=======================');
        
        if (empty($issues)) {
            $this->info('🎉 All checks passed! System is healthy.');
            return 0;
        } else {
            $this->error('⚠️ Found ' . count($issues) . ' issue(s):');
            foreach ($issues as $issue) {
                $this->error("  • {$issue}");
            }
            
            if (!$this->option('fix')) {
                $this->info('');
                $this->info('💡 Run with --fix to automatically resolve issues where possible:');
                $this->info('   php artisan arena-doviz:health-check --fix');
            }
            
            return 1;
        }
    }
}
