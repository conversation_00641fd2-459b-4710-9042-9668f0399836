<?php

namespace App\Console\Commands\ArenaDoviz;

use App\Services\ArenaDoviz\ImportService;
use Illuminate\Console\Command;

class GenerateImportTemplatesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'arena-doviz:generate-templates {--type=all : Template type (rates, commission, all)}';

    /**
     * The console command description.
     */
    protected $description = 'Generate import templates for Arena Doviz data';

    /**
     * Execute the console command.
     */
    public function handle(ImportService $importService)
    {
        $this->info('🔧 Generating Arena Doviz Import Templates');
        $this->info('==========================================');

        $type = $this->option('type');
        $generated = [];

        if ($type === 'all' || $type === 'rates') {
            $this->info('📊 Generating currency rates template...');
            $path = $importService->generateRatesTemplate();
            $generated[] = ['Type' => 'Currency Rates', 'Path' => $path];
            $this->info("✅ Currency rates template: {$path}");
        }

        if ($type === 'all' || $type === 'commission') {
            $this->info('💰 Generating commission rules template...');
            $path = $importService->generateCommissionRulesTemplate();
            $generated[] = ['Type' => 'Commission Rules', 'Path' => $path];
            $this->info("✅ Commission rules template: {$path}");
        }

        if (empty($generated)) {
            $this->error('❌ Invalid template type. Use: rates, commission, or all');
            return 1;
        }

        $this->info('');
        $this->info('📋 Generated Templates Summary');
        $this->info('==============================');
        $this->table(['Type', 'Path'], $generated);

        $this->info('');
        $this->info('💡 Usage Instructions:');
        $this->info('1. Download the template files from the paths above');
        $this->info('2. Fill in your data following the sample format');
        $this->info('3. Upload through the Filament admin interface');
        $this->info('4. Or use the import commands directly');

        return 0;
    }
}
