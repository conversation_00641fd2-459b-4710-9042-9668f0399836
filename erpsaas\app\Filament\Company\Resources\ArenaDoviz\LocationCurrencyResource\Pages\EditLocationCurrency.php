<?php

namespace App\Filament\Company\Resources\ArenaDoviz\LocationCurrencyResource\Pages;

use App\Filament\Company\Resources\ArenaDoviz\LocationCurrencyResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditLocationCurrency extends EditRecord
{
    protected static string $resource = LocationCurrencyResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
