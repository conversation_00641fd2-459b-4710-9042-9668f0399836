<?php

namespace App\Filament\Company\Resources\ArenaDoviz;

use App\Enums\ArenaDoviz\ExchangeStatus;
use App\Enums\ArenaDoviz\ExchangeType;
use App\Filament\Company\Resources\ArenaDoviz\CombinedExchangeResource\Pages;
use App\Models\ArenaDoviz\CurrencyExchange;
use App\Models\Common\Client;
use App\Services\ArenaDoviz\LocationCurrencyService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class CombinedExchangeResource extends Resource
{
    protected static ?string $model = CurrencyExchange::class;

    protected static ?string $navigationIcon = 'heroicon-o-arrow-path-rounded-square';

    protected static ?string $navigationLabel = 'Combined Exchanges';

    protected static ?string $modelLabel = 'Combined Exchange';

    protected static ?string $pluralModelLabel = 'Combined Exchanges';

    protected static ?string $navigationGroup = 'Arena Doviz';

    protected static ?int $navigationSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Combined Exchange Details')
                    ->description('Buy from one customer and sell to another in a single transaction')
                    ->schema([
                        Forms\Components\Select::make('location_id')
                            ->label('Location')
                            ->relationship('location', 'name')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->live(),

                        Forms\Components\DateTimePicker::make('exchange_date')
                            ->label('Exchange Date')
                            ->default(now())
                            ->required(),

                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options(ExchangeStatus::class)
                            ->default(ExchangeStatus::PENDING)
                            ->required(),
                    ])->columns(3),

                Forms\Components\Section::make('Buy Transaction (From Customer)')
                    ->schema([
                        Forms\Components\Select::make('buy_from_client_id')
                            ->label('Buy From Customer')
                            ->options(Client::pluck('name', 'id'))
                            ->searchable()
                            ->required()
                            ->live(),

                        Forms\Components\Select::make('buy_from_currency_code')
                            ->label('Currency to Buy')
                            ->options(function () {
                                return app(LocationCurrencyService::class)
                                    ->getAvailableLocationCurrencies();
                            })
                            ->searchable()
                            ->required()
                            ->live(),

                        Forms\Components\TextInput::make('buy_amount')
                            ->label('Amount to Buy')
                            ->numeric()
                            ->step(0.01)
                            ->required()
                            ->live(debounce: 500),

                        Forms\Components\TextInput::make('buy_rate')
                            ->label('Buy Rate')
                            ->numeric()
                            ->step(0.000001)
                            ->required()
                            ->live(debounce: 500),

                        Forms\Components\TextInput::make('buy_total')
                            ->label('Total Buy Amount (TRY)')
                            ->numeric()
                            ->disabled()
                            ->dehydrated(false)
                            ->afterStateHydrated(function (Forms\Components\TextInput $component, Forms\Get $get) {
                                $amount = $get('buy_amount');
                                $rate = $get('buy_rate');
                                if ($amount && $rate) {
                                    $component->state(number_format($amount * $rate, 2));
                                }
                            }),
                    ])->columns(2),

                Forms\Components\Section::make('Sell Transaction (To Customer)')
                    ->schema([
                        Forms\Components\Select::make('sell_to_client_id')
                            ->label('Sell To Customer')
                            ->options(Client::pluck('name', 'id'))
                            ->searchable()
                            ->required()
                            ->live(),

                        Forms\Components\Select::make('sell_to_currency_code')
                            ->label('Currency to Sell')
                            ->options(function () {
                                return app(LocationCurrencyService::class)
                                    ->getAvailableLocationCurrencies();
                            })
                            ->searchable()
                            ->required()
                            ->live(),

                        Forms\Components\TextInput::make('sell_amount')
                            ->label('Amount to Sell')
                            ->numeric()
                            ->step(0.01)
                            ->required()
                            ->live(debounce: 500),

                        Forms\Components\TextInput::make('sell_rate')
                            ->label('Sell Rate')
                            ->numeric()
                            ->step(0.000001)
                            ->required()
                            ->live(debounce: 500),

                        Forms\Components\TextInput::make('sell_total')
                            ->label('Total Sell Amount (TRY)')
                            ->numeric()
                            ->disabled()
                            ->dehydrated(false)
                            ->afterStateHydrated(function (Forms\Components\TextInput $component, Forms\Get $get) {
                                $amount = $get('sell_amount');
                                $rate = $get('sell_rate');
                                if ($amount && $rate) {
                                    $component->state(number_format($amount * $rate, 2));
                                }
                            }),
                    ])->columns(2),

                Forms\Components\Section::make('Profit Calculation')
                    ->schema([
                        Forms\Components\TextInput::make('profit_amount')
                            ->label('Profit (TRY)')
                            ->numeric()
                            ->disabled()
                            ->dehydrated(false)
                            ->afterStateHydrated(function (Forms\Components\TextInput $component, Forms\Get $get) {
                                $buyTotal = $get('buy_amount') * $get('buy_rate');
                                $sellTotal = $get('sell_amount') * $get('sell_rate');
                                if ($buyTotal && $sellTotal) {
                                    $profit = $sellTotal - $buyTotal;
                                    $component->state(number_format($profit, 2));
                                }
                            }),

                        Forms\Components\TextInput::make('profit_margin')
                            ->label('Profit Margin (%)')
                            ->numeric()
                            ->disabled()
                            ->dehydrated(false)
                            ->afterStateHydrated(function (Forms\Components\TextInput $component, Forms\Get $get) {
                                $buyTotal = $get('buy_amount') * $get('buy_rate');
                                $sellTotal = $get('sell_amount') * $get('sell_rate');
                                if ($buyTotal && $sellTotal && $buyTotal > 0) {
                                    $margin = (($sellTotal - $buyTotal) / $buyTotal) * 100;
                                    $component->state(number_format($margin, 2));
                                }
                            }),
                    ])->columns(2),

                Forms\Components\Section::make('Additional Information')
                    ->schema([
                        Forms\Components\Textarea::make('notes')
                            ->label('Notes')
                            ->rows(3)
                            ->columnSpanFull(),

                        Forms\Components\TextInput::make('reference_number')
                            ->label('Reference Number')
                            ->maxLength(100),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn (Builder $query) => $query->where('exchange_type', ExchangeType::COMBINED_EXCHANGE))
            ->columns([
                Tables\Columns\TextColumn::make('exchange_number')
                    ->label('Exchange #')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('exchange_date')
                    ->label('Date')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('client.name')
                    ->label('Buy From')
                    ->searchable(),

                Tables\Columns\TextColumn::make('from_currency_code')
                    ->label('Buy Currency')
                    ->badge(),

                Tables\Columns\TextColumn::make('formatted_from_amount')
                    ->label('Buy Amount'),

                Tables\Columns\TextColumn::make('to_currency_code')
                    ->label('Sell Currency')
                    ->badge(),

                Tables\Columns\TextColumn::make('formatted_to_amount')
                    ->label('Sell Amount'),

                Tables\Columns\TextColumn::make('status')
                    ->badge(),

                Tables\Columns\TextColumn::make('location.name')
                    ->label('Location')
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options(ExchangeStatus::class),

                Tables\Filters\SelectFilter::make('location_id')
                    ->relationship('location', 'name')
                    ->label('Location'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('exchange_type', ExchangeType::COMBINED_EXCHANGE);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCombinedExchanges::route('/'),
            'create' => Pages\CreateCombinedExchange::route('/create'),
            'view' => Pages\ViewCombinedExchange::route('/{record}'),
            'edit' => Pages\EditCombinedExchange::route('/{record}/edit'),
        ];
    }
}
