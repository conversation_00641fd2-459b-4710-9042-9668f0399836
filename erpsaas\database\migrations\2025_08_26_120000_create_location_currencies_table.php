<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('location_currencies', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->constrained()->cascadeOnDelete();
            $table->foreignId('location_id')->constrained('locations')->cascadeOnDelete();
            
            // Currency information
            $table->string('base_currency_code', 3); // USD, EUR, etc.
            $table->string('location_currency_code', 10)->unique(); // USD_IST, EUR_TBZ, etc.
            $table->string('name', 100); // US Dollar Istanbul, Euro Tabriz
            $table->string('symbol', 10)->default('$');
            
            // Exchange rates
            $table->decimal('buy_rate', 15, 6)->nullable();
            $table->decimal('sell_rate', 15, 6)->nullable();
            $table->decimal('mid_rate', 15, 6)->nullable();
            $table->timestamp('rate_date')->nullable();
            
            // Configuration
            $table->boolean('is_active')->default(true);
            $table->tinyInteger('decimal_places')->default(2);
            
            // Audit fields
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes
            $table->index(['company_id', 'location_id']);
            $table->index(['base_currency_code', 'location_id']);
            $table->index(['location_currency_code', 'is_active']);
            $table->index('rate_date');
            
            // Unique constraint
            $table->unique(['location_id', 'base_currency_code'], 'unique_location_base_currency');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('location_currencies');
    }
};
