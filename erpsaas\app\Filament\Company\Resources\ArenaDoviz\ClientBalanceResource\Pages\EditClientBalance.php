<?php

namespace App\Filament\Company\Resources\ArenaDoviz\ClientBalanceResource\Pages;

use App\Filament\Company\Resources\ArenaDoviz\ClientBalanceResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditClientBalance extends EditRecord
{
    protected static string $resource = ClientBalanceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
