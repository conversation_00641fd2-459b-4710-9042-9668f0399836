<?php

namespace App\Filament\Company\Resources\ArenaDoviz\ClientBalanceResource\Pages;

use App\Filament\Company\Resources\ArenaDoviz\ClientBalanceResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListClientBalances extends ListRecords
{
    protected static string $resource = ClientBalanceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
