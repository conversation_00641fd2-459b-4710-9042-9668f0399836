<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('iranian_banks', function (Blueprint $table) {
            $table->string('swift_code', 15)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('iranian_banks', function (Blueprint $table) {
            $table->string('swift_code', 11)->nullable()->change();
        });
    }
};
