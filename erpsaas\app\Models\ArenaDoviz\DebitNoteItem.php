<?php

namespace App\Models\ArenaDoviz;

use App\Concerns\Blamable;
use App\Concerns\CompanyOwned;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class DebitNoteItem extends Model
{
    use Blamable;
    use CompanyOwned;
    use HasFactory;
    use SoftDeletes;

    protected $table = 'debit_note_items';

    protected $fillable = [
        'company_id',
        'debit_note_id',
        'item_type',
        'description',
        'quantity',
        'unit_price',
        'total_amount',
        'currency_code',
        'reference_number',
        'notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'quantity' => 'decimal:4',
        'unit_price' => 'decimal:2',
        'total_amount' => 'decimal:2',
    ];

    /**
     * Get the debit note this item belongs to
     */
    public function debitNote(): BelongsTo
    {
        return $this->belongsTo(DebitNote::class);
    }

    /**
     * Get formatted total amount
     */
    public function getFormattedTotalAmountAttribute(): string
    {
        return \App\Helpers\ArenaDoviz\NumberFormatter::formatByCurrency($this->total_amount, $this->currency_code);
    }

    /**
     * Get formatted unit price
     */
    public function getFormattedUnitPriceAttribute(): string
    {
        return \App\Helpers\ArenaDoviz\NumberFormatter::formatByCurrency($this->unit_price, $this->currency_code);
    }

    /**
     * Calculate total amount based on quantity and unit price
     */
    public function calculateTotal(): void
    {
        $this->total_amount = $this->quantity * $this->unit_price;
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($item) {
            // Auto-calculate total if not set
            if (is_null($item->total_amount) && $item->quantity && $item->unit_price) {
                $item->calculateTotal();
            }
        });
    }
}
