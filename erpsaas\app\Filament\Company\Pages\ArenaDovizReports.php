<?php

namespace App\Filament\Company\Pages;

use App\Services\ArenaDoviz\DashboardAnalyticsService;
use App\Services\ArenaDoviz\FinancialReportsService;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Contracts\Support\Htmlable;

class ArenaDovizReports extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';
    protected static ?string $navigationLabel = 'Arena Doviz Reports';
    protected static ?string $title = 'Arena Doviz Reports';
    protected static string $view = 'filament.company.pages.arena-doviz-reports';
    protected static ?string $slug = 'arena-doviz-reports';
    protected static ?int $navigationSort = 10;

    public ?array $data = [];
    public string $reportType = 'dashboard';
    public string $startDate = '';
    public string $endDate = '';
    public ?array $reportData = null;

    protected function getAnalyticsService(): DashboardAnalyticsService
    {
        return app(DashboardAnalyticsService::class);
    }

    protected function getReportsService(): FinancialReportsService
    {
        return app(FinancialReportsService::class);
    }

    public function mount(): void
    {
        $this->startDate = now()->startOfMonth()->format('Y-m-d');
        $this->endDate = now()->format('Y-m-d');
        
        $this->form->fill([
            'report_type' => $this->reportType,
            'start_date' => $this->startDate,
            'end_date' => $this->endDate,
        ]);

        $this->generateReport();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make(3)
                    ->schema([
                        Forms\Components\Select::make('report_type')
                            ->label('Report Type')
                            ->options([
                                'dashboard' => 'Dashboard Analytics',
                                'profit_loss' => 'Profit & Loss',
                                'balance_sheet' => 'Balance Sheet',
                                'cash_flow' => 'Cash Flow',
                                'transaction_summary' => 'Transaction Summary',
                                'commission_analysis' => 'Commission Analysis',
                                'client_profitability' => 'Client Profitability',
                            ])
                            ->default('dashboard')
                            ->live()
                            ->afterStateUpdated(fn() => $this->generateReport()),

                        Forms\Components\DatePicker::make('start_date')
                            ->label('Start Date')
                            ->default(now()->startOfMonth())
                            ->live()
                            ->afterStateUpdated(fn() => $this->generateReport()),

                        Forms\Components\DatePicker::make('end_date')
                            ->label('End Date')
                            ->default(now())
                            ->live()
                            ->afterStateUpdated(fn() => $this->generateReport()),
                    ]),
            ])
            ->statePath('data');
    }

    public function generateReport(): void
    {
        $company = auth()->user()->currentCompany;
        $startDate = Carbon::parse($this->data['start_date'] ?? $this->startDate);
        $endDate = Carbon::parse($this->data['end_date'] ?? $this->endDate);
        $reportType = $this->data['report_type'] ?? $this->reportType;

        try {
            $this->reportData = match ($reportType) {
                'dashboard' => $this->getAnalyticsService()->getDashboardMetrics($company, $startDate, $endDate),
                'profit_loss' => $this->getReportsService()->generateProfitLossReport($company, $startDate, $endDate),
                'balance_sheet' => $this->getReportsService()->generateBalanceSheetReport($company, $endDate),
                'cash_flow' => $this->getReportsService()->generateCashFlowReport($company, $startDate, $endDate),
                'transaction_summary' => $this->getReportsService()->generateTransactionSummaryReport($company, $startDate, $endDate),
                'commission_analysis' => $this->getReportsService()->generateCommissionAnalysisReport($company, $startDate, $endDate),
                'client_profitability' => $this->getReportsService()->generateClientProfitabilityReport($company, $startDate, $endDate),
                default => null,
            };

            $this->reportType = $reportType;
        } catch (\Exception $e) {
            $this->reportData = ['error' => $e->getMessage()];
        }
    }

    public function getTitle(): string|Htmlable
    {
        return 'Arena Doviz Reports';
    }

    public function getMaxWidth(): MaxWidth
    {
        return MaxWidth::Full;
    }

    protected function getViewData(): array
    {
        return [
            'reportData' => $this->reportData,
            'reportType' => $this->reportType,
            'startDate' => $this->startDate,
            'endDate' => $this->endDate,
        ];
    }
}
