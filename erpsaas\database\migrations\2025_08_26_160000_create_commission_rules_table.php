<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('commission_rules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->constrained()->cascadeOnDelete();
            $table->foreignId('client_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('location_id')->nullable()->constrained('locations')->nullOnDelete();
            
            // Rule identification
            $table->string('rule_name', 100);
            $table->enum('exchange_type', ['buy', 'sell', 'transfer', 'cash_deposit', 'combined_exchange'])
                ->nullable(); // null means applies to all types
            
            // Currency filters
            $table->string('from_currency_code', 10)->nullable();
            $table->string('to_currency_code', 10)->nullable();
            
            // Commission configuration
            $table->enum('commission_type', ['percentage', 'fixed', 'tiered', 'hybrid'])
                ->default('percentage');
            $table->decimal('commission_rate', 8, 4)->nullable(); // Percentage rate
            $table->decimal('fixed_amount', 15, 2)->nullable(); // Fixed amount
            $table->decimal('min_commission', 15, 2)->nullable(); // Minimum commission
            $table->decimal('max_commission', 15, 2)->nullable(); // Maximum commission
            
            // Calculation method
            $table->enum('calculation_method', [
                'pre_conversion', 'post_conversion', 'higher_amount', 'lower_amount'
            ])->default('post_conversion');
            
            // Commission currency
            $table->string('commission_currency', 10)->nullable(); // Currency for commission
            
            // Tiered rules (JSON)
            $table->json('tier_rules')->nullable(); // For tiered commission structure
            
            // Rule validity
            $table->boolean('is_active')->default(true);
            $table->date('valid_from')->nullable();
            $table->date('valid_until')->nullable();
            $table->integer('priority')->default(1); // Higher number = higher priority
            
            // Notes
            $table->text('notes')->nullable();
            
            // Audit fields
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes
            $table->index(['company_id', 'is_active']);
            $table->index(['client_id', 'is_active']);
            $table->index(['location_id', 'is_active']);
            $table->index(['exchange_type', 'is_active']);
            $table->index(['from_currency_code', 'to_currency_code']);
            $table->index(['valid_from', 'valid_until']);
            $table->index(['priority', 'is_active']);
            $table->index(['commission_type', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('commission_rules');
    }
};
