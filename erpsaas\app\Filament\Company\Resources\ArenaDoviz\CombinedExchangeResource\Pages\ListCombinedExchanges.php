<?php

namespace App\Filament\Company\Resources\ArenaDoviz\CombinedExchangeResource\Pages;

use App\Filament\Company\Resources\ArenaDoviz\CombinedExchangeResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCombinedExchanges extends ListRecords
{
    protected static string $resource = CombinedExchangeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
