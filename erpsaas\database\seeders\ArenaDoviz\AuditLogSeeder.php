<?php

namespace Database\Seeders\ArenaDoviz;

use App\Models\User;
use App\Models\Common\Client;
use App\Models\ArenaDoviz\CurrencyExchange;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AuditLogSeeder extends Seeder
{
    /**
     * Seed audit logs for Arena Doviz operations.
     * 
     * Creates comprehensive audit trail including:
     * - User login/logout activities
     * - Transaction creation, modification, and approval
     * - Client profile changes and KYC updates
     * - System configuration changes
     * - Security events and access attempts
     * - Data export and report generation
     */
    public function run(): void
    {
        $this->command->info('📋 Seeding Arena Doviz audit logs...');
        
        // Create audit_logs table if it doesn't exist
        $this->createAuditLogsTable();
        
        $users = User::where('current_company_id', 1)->get();
        $clients = Client::where('company_id', 1)->get();
        $exchanges = CurrencyExchange::where('company_id', 1)->limit(50)->get();
        
        if ($users->isEmpty()) {
            $this->command->warn('No users found. Please run UserRoleSeeder first.');
            return;
        }
        
        $logCount = 0;
        
        // Generate audit logs for the last 3 months
        $startDate = Carbon::now()->subMonths(3);
        $endDate = Carbon::now();
        
        for ($date = $startDate->copy(); $date->lte($endDate); $date->addDay()) {
            // Generate 5-20 audit events per day
            $dailyEvents = rand(5, 20);
            
            for ($i = 0; $i < $dailyEvents; $i++) {
                $this->createAuditLog($users, $clients, $exchanges, $date->copy()->addHours(rand(8, 20)));
                $logCount++;
            }
        }
        
        // Create specific audit scenarios
        $this->createSpecificAuditScenarios($users, $clients, $exchanges);
        $logCount += 15;
        
        $this->command->info("✅ Created {$logCount} audit log entries");
    }
    
    private function createAuditLogsTable(): void
    {
        if (DB::getSchemaBuilder()->hasTable('audit_logs')) {
            return;
        }
        
        DB::statement("
            CREATE TABLE audit_logs (
                id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
                user_id BIGINT UNSIGNED,
                event_type VARCHAR(50) NOT NULL,
                event_category VARCHAR(30) NOT NULL,
                description TEXT NOT NULL,
                model_type VARCHAR(100),
                model_id BIGINT UNSIGNED,
                old_values JSON,
                new_values JSON,
                ip_address VARCHAR(45),
                user_agent TEXT,
                session_id VARCHAR(100),
                risk_level ENUM('low', 'medium', 'high', 'critical') DEFAULT 'low',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_user_id (user_id),
                INDEX idx_event_type (event_type),
                INDEX idx_event_category (event_category),
                INDEX idx_model (model_type, model_id),
                INDEX idx_created_at (created_at),
                INDEX idx_risk_level (risk_level),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
            )
        ");
    }
    
    private function createAuditLog($users, $clients, $exchanges, Carbon $timestamp): void
    {
        $user = $users->random();
        $eventData = $this->generateRandomEvent($user, $clients, $exchanges);
        
        DB::table('audit_logs')->insert([
            'user_id' => $user->id,
            'event_type' => $eventData['event_type'],
            'event_category' => $eventData['event_category'],
            'description' => $eventData['description'],
            'model_type' => $eventData['model_type'],
            'model_id' => $eventData['model_id'],
            'old_values' => $eventData['old_values'] ? json_encode($eventData['old_values']) : null,
            'new_values' => $eventData['new_values'] ? json_encode($eventData['new_values']) : null,
            'ip_address' => $this->generateIpAddress(),
            'user_agent' => $this->generateUserAgent(),
            'session_id' => $this->generateSessionId(),
            'risk_level' => $eventData['risk_level'],
            'created_at' => $timestamp,
        ]);
    }
    
    private function generateRandomEvent(User $user, $clients, $exchanges): array
    {
        $eventTypes = [
            'authentication' => 30,
            'transaction' => 25,
            'client_management' => 20,
            'system_config' => 10,
            'security' => 8,
            'reporting' => 7,
        ];
        
        $category = $this->weightedRandom($eventTypes);
        
        return match ($category) {
            'authentication' => $this->generateAuthEvent($user),
            'transaction' => $this->generateTransactionEvent($user, $exchanges),
            'client_management' => $this->generateClientEvent($user, $clients),
            'system_config' => $this->generateSystemEvent($user),
            'security' => $this->generateSecurityEvent($user),
            'reporting' => $this->generateReportingEvent($user),
            default => $this->generateAuthEvent($user),
        };
    }
    
    private function generateAuthEvent(User $user): array
    {
        $events = [
            'login' => [
                'description' => "User {$user->name} logged in successfully",
                'risk_level' => 'low',
            ],
            'logout' => [
                'description' => "User {$user->name} logged out",
                'risk_level' => 'low',
            ],
            'failed_login' => [
                'description' => "Failed login attempt for user {$user->email}",
                'risk_level' => 'medium',
            ],
            'password_change' => [
                'description' => "User {$user->name} changed password",
                'risk_level' => 'medium',
            ],
        ];
        
        $eventType = array_rand($events);
        $event = $events[$eventType];
        
        return [
            'event_type' => $eventType,
            'event_category' => 'authentication',
            'description' => $event['description'],
            'model_type' => 'App\\Models\\User',
            'model_id' => $user->id,
            'old_values' => null,
            'new_values' => null,
            'risk_level' => $event['risk_level'],
        ];
    }
    
    private function generateTransactionEvent(User $user, $exchanges): array
    {
        if ($exchanges->isEmpty()) {
            return $this->generateAuthEvent($user);
        }
        
        $exchange = $exchanges->random();
        
        $events = [
            'transaction_created' => [
                'description' => "User {$user->name} created currency exchange transaction #{$exchange->id}",
                'risk_level' => 'medium',
                'new_values' => [
                    'amount' => $exchange->from_amount,
                    'currency' => $exchange->from_currency_code,
                    'type' => $exchange->exchange_type,
                ],
            ],
            'transaction_approved' => [
                'description' => "User {$user->name} approved transaction #{$exchange->id}",
                'risk_level' => 'high',
                'old_values' => ['status' => 'pending'],
                'new_values' => ['status' => 'approved'],
            ],
            'transaction_cancelled' => [
                'description' => "User {$user->name} cancelled transaction #{$exchange->id}",
                'risk_level' => 'high',
                'old_values' => ['status' => 'pending'],
                'new_values' => ['status' => 'cancelled'],
            ],
            'rate_updated' => [
                'description' => "User {$user->name} updated exchange rate for transaction #{$exchange->id}",
                'risk_level' => 'high',
                'old_values' => ['rate' => $exchange->exchange_rate - 0.1],
                'new_values' => ['rate' => $exchange->exchange_rate],
            ],
        ];
        
        $eventType = array_rand($events);
        $event = $events[$eventType];
        
        return [
            'event_type' => $eventType,
            'event_category' => 'transaction',
            'description' => $event['description'],
            'model_type' => 'App\\Models\\ArenaDoviz\\CurrencyExchange',
            'model_id' => $exchange->id,
            'old_values' => $event['old_values'] ?? null,
            'new_values' => $event['new_values'] ?? null,
            'risk_level' => $event['risk_level'],
        ];
    }
    
    private function generateClientEvent(User $user, $clients): array
    {
        if ($clients->isEmpty()) {
            return $this->generateAuthEvent($user);
        }
        
        $client = $clients->random();
        
        $events = [
            'client_created' => [
                'description' => "User {$user->name} created new client profile: {$client->name}",
                'risk_level' => 'medium',
                'new_values' => ['name' => $client->name, 'account_number' => $client->account_number],
            ],
            'client_updated' => [
                'description' => "User {$user->name} updated client profile: {$client->name}",
                'risk_level' => 'medium',
                'old_values' => ['phone' => '+90 ************'],
                'new_values' => ['phone' => '+90 ************'],
            ],
            'kyc_approved' => [
                'description' => "User {$user->name} approved KYC for client: {$client->name}",
                'risk_level' => 'high',
                'old_values' => ['kyc_status' => 'pending'],
                'new_values' => ['kyc_status' => 'approved'],
            ],
            'credit_limit_changed' => [
                'description' => "User {$user->name} changed credit limit for client: {$client->name}",
                'risk_level' => 'high',
                'old_values' => ['credit_limit' => 50000],
                'new_values' => ['credit_limit' => 75000],
            ],
        ];
        
        $eventType = array_rand($events);
        $event = $events[$eventType];
        
        return [
            'event_type' => $eventType,
            'event_category' => 'client_management',
            'description' => $event['description'],
            'model_type' => 'App\\Models\\Common\\Client',
            'model_id' => $client->id,
            'old_values' => $event['old_values'] ?? null,
            'new_values' => $event['new_values'] ?? null,
            'risk_level' => $event['risk_level'],
        ];
    }
    
    private function generateSystemEvent(User $user): array
    {
        $events = [
            'rate_config_updated' => [
                'description' => "User {$user->name} updated exchange rate configuration",
                'risk_level' => 'high',
            ],
            'user_permissions_changed' => [
                'description' => "User {$user->name} modified user permissions",
                'risk_level' => 'critical',
            ],
            'system_backup' => [
                'description' => "System backup initiated by {$user->name}",
                'risk_level' => 'medium',
            ],
            'currency_added' => [
                'description' => "User {$user->name} added new currency to system",
                'risk_level' => 'high',
            ],
        ];
        
        $eventType = array_rand($events);
        $event = $events[$eventType];
        
        return [
            'event_type' => $eventType,
            'event_category' => 'system_config',
            'description' => $event['description'],
            'model_type' => null,
            'model_id' => null,
            'old_values' => null,
            'new_values' => null,
            'risk_level' => $event['risk_level'],
        ];
    }
    
    private function generateSecurityEvent(User $user): array
    {
        $events = [
            'suspicious_activity' => [
                'description' => "Suspicious activity detected for user {$user->name}",
                'risk_level' => 'critical',
            ],
            'multiple_failed_logins' => [
                'description' => "Multiple failed login attempts detected for {$user->email}",
                'risk_level' => 'high',
            ],
            'ip_blocked' => [
                'description' => "IP address blocked due to suspicious activity",
                'risk_level' => 'high',
            ],
            'session_expired' => [
                'description' => "User session expired for {$user->name}",
                'risk_level' => 'low',
            ],
        ];
        
        $eventType = array_rand($events);
        $event = $events[$eventType];
        
        return [
            'event_type' => $eventType,
            'event_category' => 'security',
            'description' => $event['description'],
            'model_type' => 'App\\Models\\User',
            'model_id' => $user->id,
            'old_values' => null,
            'new_values' => null,
            'risk_level' => $event['risk_level'],
        ];
    }
    
    private function generateReportingEvent(User $user): array
    {
        $events = [
            'report_generated' => [
                'description' => "User {$user->name} generated transaction report",
                'risk_level' => 'low',
            ],
            'data_exported' => [
                'description' => "User {$user->name} exported client data",
                'risk_level' => 'medium',
            ],
            'audit_report_accessed' => [
                'description' => "User {$user->name} accessed audit report",
                'risk_level' => 'medium',
            ],
        ];
        
        $eventType = array_rand($events);
        $event = $events[$eventType];
        
        return [
            'event_type' => $eventType,
            'event_category' => 'reporting',
            'description' => $event['description'],
            'model_type' => null,
            'model_id' => null,
            'old_values' => null,
            'new_values' => null,
            'risk_level' => $event['risk_level'],
        ];
    }
    
    private function weightedRandom(array $weights): string
    {
        $rand = rand(1, array_sum($weights));
        $cumulative = 0;
        
        foreach ($weights as $key => $weight) {
            $cumulative += $weight;
            if ($rand <= $cumulative) {
                return $key;
            }
        }
        
        return array_key_first($weights);
    }
    
    private function generateIpAddress(): string
    {
        $ips = [
            '192.168.1.' . rand(1, 254),
            '10.0.0.' . rand(1, 254),
            '172.16.0.' . rand(1, 254),
            '85.34.78.' . rand(1, 254), // Turkish IP range
            '94.103.82.' . rand(1, 254), // Turkish IP range
        ];
        
        return $ips[array_rand($ips)];
    }
    
    private function generateUserAgent(): string
    {
        $agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15',
        ];
        
        return $agents[array_rand($agents)];
    }
    
    private function generateSessionId(): string
    {
        return 'sess_' . bin2hex(random_bytes(16));
    }
    
    private function createSpecificAuditScenarios($users, $clients, $exchanges): void
    {
        // High-risk security event
        $admin = $users->where('email', '<EMAIL>')->first();
        if ($admin) {
            DB::table('audit_logs')->insert([
                'user_id' => $admin->id,
                'event_type' => 'admin_access',
                'event_category' => 'security',
                'description' => 'Administrator accessed sensitive system configuration',
                'model_type' => null,
                'model_id' => null,
                'old_values' => null,
                'new_values' => json_encode(['config_section' => 'security_settings']),
                'ip_address' => '*************',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'session_id' => 'sess_admin_' . bin2hex(random_bytes(8)),
                'risk_level' => 'critical',
                'created_at' => Carbon::now()->subDays(2),
            ]);
        }
        
        // Large transaction approval
        if (!$exchanges->isEmpty() && !$users->isEmpty()) {
            $manager = $users->where('role', 'exchange_manager')->first() ?? $users->first();
            $exchange = $exchanges->first();
            
            DB::table('audit_logs')->insert([
                'user_id' => $manager->id,
                'event_type' => 'large_transaction_approved',
                'event_category' => 'transaction',
                'description' => "Manager approved large transaction #{$exchange->id} worth {$exchange->from_amount} {$exchange->from_currency_code}",
                'model_type' => 'App\\Models\\ArenaDoviz\\CurrencyExchange',
                'model_id' => $exchange->id,
                'old_values' => json_encode(['status' => 'pending_approval']),
                'new_values' => json_encode(['status' => 'approved', 'approved_by' => $manager->id]),
                'ip_address' => '*************',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'session_id' => 'sess_mgr_' . bin2hex(random_bytes(8)),
                'risk_level' => 'high',
                'created_at' => Carbon::now()->subDays(1),
            ]);
        }
    }
}
