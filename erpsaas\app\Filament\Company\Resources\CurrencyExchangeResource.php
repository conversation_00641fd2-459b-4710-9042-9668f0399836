<?php

namespace App\Filament\Company\Resources;

use App\Enums\ArenaDoviz\ExchangeStatus;
use App\Enums\ArenaDoviz\ExchangeType;
use App\Filament\Company\Resources\CurrencyExchangeResource\Pages;
use App\Models\ArenaDoviz\CurrencyExchange;
use App\Models\Common\Client;
use App\Models\Setting\Currency;
use App\Services\ArenaDoviz\UserPermissionService;
use App\Utilities\Currency\CurrencyAccessor;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class CurrencyExchangeResource extends Resource
{
    protected static ?string $model = CurrencyExchange::class;

    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';

    protected static ?string $navigationLabel = 'Currency Exchanges';

    protected static ?string $modelLabel = 'Currency Exchange';

    protected static ?string $pluralModelLabel = 'Currency Exchanges';

    protected static ?int $navigationSort = 1;

    public static function canCreate(): bool
    {
        return app(UserPermissionService::class)->hasPermission('exchange:create');
    }

    public static function canEdit($record): bool
    {
        return app(UserPermissionService::class)->hasPermission('exchange:update');
    }

    public static function canDelete($record): bool
    {
        return app(UserPermissionService::class)->hasPermission('exchange:delete');
    }

    public static function canView($record): bool
    {
        return app(UserPermissionService::class)->hasPermission('exchange:read');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Exchange Details')
                    ->schema([
                        Forms\Components\Select::make('client_id')
                            ->label('Client')
                            ->relationship('client', 'name')
                            ->searchable()
                            ->preload()
                            ->required(),

                        Forms\Components\Select::make('location_id')
                            ->label('Location')
                            ->relationship('location', 'name')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->live(),

                        Forms\Components\Select::make('exchange_type')
                            ->label('Exchange Type')
                            ->options(ExchangeType::class)
                            ->required()
                            ->live(),

                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options(ExchangeStatus::class)
                            ->default(ExchangeStatus::PENDING)
                            ->required(),

                        Forms\Components\Select::make('iranian_bank_id')
                            ->label('Iranian Bank')
                            ->options(\App\Models\ArenaDoviz\IranianBank::getForDropdown())
                            ->searchable()
                            ->preload()
                            ->nullable()
                            ->visible(fn (Forms\Get $get) =>
                                str_contains($get('from_currency_code'), 'IRR_') ||
                                str_contains($get('to_currency_code'), 'IRR_')
                            ),
                    ])->columns(2),

                Forms\Components\Section::make('Currency & Amounts')
                    ->schema([
                        Forms\Components\Select::make('from_currency_code')
                            ->label('From Currency')
                            ->options(function () {
                                return app(\App\Services\ArenaDoviz\LocationCurrencyService::class)
                                    ->getAvailableLocationCurrencies();
                            })
                            ->searchable()
                            ->required()
                            ->live(),

                        Forms\Components\Select::make('to_currency_code')
                            ->label('To Currency')
                            ->options(function () {
                                return app(\App\Services\ArenaDoviz\LocationCurrencyService::class)
                                    ->getAvailableLocationCurrencies();
                            })
                            ->searchable()
                            ->required()
                            ->live(),

                        Forms\Components\TextInput::make('from_amount')
                            ->label('From Amount')
                            ->numeric()
                            ->step(0.0001)
                            ->required()
                            ->live()
                            ->formatStateUsing(fn ($state) => $state ? \App\Helpers\ArenaDoviz\NumberFormatter::formatNumber($state, 2) : '')
                            ->dehydrateStateUsing(fn ($state) => $state ? \App\Helpers\ArenaDoviz\NumberFormatter::parseNumber($state) : null)
                            ->afterStateUpdated(function ($state, Forms\Set $set, Forms\Get $get) {
                                $exchangeRate = $get('exchange_rate');
                                if ($state && $exchangeRate) {
                                    $amount = \App\Helpers\ArenaDoviz\NumberFormatter::parseNumber($state);
                                    $toAmount = round($amount * $exchangeRate, 4);
                                    $set('to_amount', \App\Helpers\ArenaDoviz\NumberFormatter::formatNumber($toAmount, 2));
                                }
                            }),

                        Forms\Components\TextInput::make('exchange_rate')
                            ->label('Exchange Rate')
                            ->numeric()
                            ->step(0.000001)
                            ->required()
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set, Forms\Get $get) {
                                $fromAmount = $get('from_amount');
                                if ($state && $fromAmount) {
                                    $set('to_amount', round($fromAmount * $state, 4));
                                }
                            }),

                        Forms\Components\TextInput::make('to_amount')
                            ->label('To Amount')
                            ->numeric()
                            ->step(0.0001)
                            ->required()
                            ->readOnly()
                            ->formatStateUsing(fn ($state) => $state ? \App\Helpers\ArenaDoviz\NumberFormatter::formatNumber($state, 2) : '')
                            ->dehydrateStateUsing(fn ($state) => $state ? \App\Helpers\ArenaDoviz\NumberFormatter::parseNumber($state) : null),

                        Forms\Components\TextInput::make('commission_rate')
                            ->label('Commission Rate (%)')
                            ->numeric()
                            ->step(0.01)
                            ->suffix('%')
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set, Forms\Get $get) {
                                $toAmount = $get('to_amount');
                                if ($state && $toAmount) {
                                    $commissionAmount = $toAmount * ($state / 100);
                                    $set('commission_amount', round($commissionAmount, 4));
                                    $set('net_amount', round($toAmount - $commissionAmount, 4));
                                }
                            }),

                        Forms\Components\TextInput::make('commission_amount')
                            ->label('Commission Amount')
                            ->numeric()
                            ->readOnly(),

                        Forms\Components\TextInput::make('net_amount')
                            ->label('Net Amount')
                            ->numeric()
                            ->readOnly(),
                    ])->columns(4),

                Forms\Components\Section::make('Additional Information')
                    ->schema([
                        Forms\Components\DateTimePicker::make('exchange_date')
                            ->label('Exchange Date')
                            ->default(now())
                            ->required(),

                        Forms\Components\DateTimePicker::make('settlement_date')
                            ->label('Settlement Date'),

                        Forms\Components\TextInput::make('reference_number')
                            ->label('Reference Number'),

                        Forms\Components\Textarea::make('notes')
                            ->label('Notes')
                            ->rows(3),
                    ])->columns(2),

                // Iranian Banking Details - Only for IRR transactions
                Forms\Components\Section::make('Iranian Banking Details')
                    ->schema([
                        Forms\Components\TextInput::make('iranian_account_number')
                            ->label('Iranian Account Number')
                            ->maxLength(50),

                        Forms\Components\TextInput::make('iranian_card_number')
                            ->label('Iranian Card Number')
                            ->maxLength(20),

                        Forms\Components\TextInput::make('iranian_tracking_code')
                            ->label('Iranian Tracking Code')
                            ->maxLength(50),

                        Forms\Components\DateTimePicker::make('iranian_transfer_date')
                            ->label('Iranian Transfer Date'),

                        Forms\Components\TextInput::make('iranian_transfer_fee')
                            ->label('Iranian Transfer Fee (IRR)')
                            ->numeric()
                            ->step(0.01),

                        Forms\Components\Textarea::make('iranian_transfer_notes')
                            ->label('Iranian Transfer Notes')
                            ->rows(2)
                            ->columnSpanFull(),
                    ])
                    ->columns(2)
                    ->visible(fn (Forms\Get $get) => $get('iranian_bank_id')),

                // Delivery Information - Only for transactions that require delivery
                Forms\Components\Section::make('Delivery Information')
                    ->schema([
                        Forms\Components\Select::make('delivery_method')
                            ->label('Delivery Method')
                            ->options([
                                'pickup' => 'Pickup from Office',
                                'courier' => 'Courier Delivery',
                                'bank_transfer' => 'Bank Transfer',
                                'cash' => 'Cash Delivery',
                            ])
                            ->required(),

                        Forms\Components\Textarea::make('delivery_address')
                            ->label('Delivery Address')
                            ->rows(3)
                            ->required()
                            ->visible(fn (Forms\Get $get) => in_array($get('delivery_method'), ['courier', 'cash'])),

                        Forms\Components\TextInput::make('delivery_contact_name')
                            ->label('Contact Name')
                            ->visible(fn (Forms\Get $get) => $get('delivery_method') === 'courier'),

                        Forms\Components\TextInput::make('delivery_contact_phone')
                            ->label('Contact Phone')
                            ->tel()
                            ->visible(fn (Forms\Get $get) => $get('delivery_method') === 'courier'),
                    ])
                    ->columns(2)
                    ->visible(function (Forms\Get $get) {
                        $exchangeType = $get('exchange_type');
                        if (!$exchangeType) return false;
                        $type = is_string($exchangeType) ? ExchangeType::from($exchangeType) : $exchangeType;
                        return $type->requiresDelivery();
                    }),

                // SWIFT Transaction Details - Only for SWIFT transactions
                Forms\Components\Section::make('SWIFT Transaction Details')
                    ->schema([
                        Forms\Components\TextInput::make('swift_code')
                            ->label('SWIFT/BIC Code')
                            ->required()
                            ->maxLength(11)
                            ->placeholder('e.g., TGBATRIS'),

                        Forms\Components\TextInput::make('correspondent_bank')
                            ->label('Correspondent Bank')
                            ->maxLength(255),

                        Forms\Components\TextInput::make('beneficiary_name')
                            ->label('Beneficiary Name')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('beneficiary_account')
                            ->label('Beneficiary Account Number')
                            ->required()
                            ->maxLength(50),

                        Forms\Components\Textarea::make('beneficiary_address')
                            ->label('Beneficiary Address')
                            ->rows(2),

                        Forms\Components\TextInput::make('purpose_of_payment')
                            ->label('Purpose of Payment')
                            ->maxLength(255)
                            ->placeholder('e.g., Trade payment, Personal transfer'),
                    ])
                    ->columns(2)
                    ->visible(function (Forms\Get $get) {
                        $exchangeType = $get('exchange_type');
                        if (!$exchangeType) return false;
                        $type = is_string($exchangeType) ? ExchangeType::from($exchangeType) : $exchangeType;
                        return $type->isSwiftTransaction();
                    }),

                // Internal Transfer Details - Only for internal transfers
                Forms\Components\Section::make('Internal Transfer Details')
                    ->schema([
                        Forms\Components\Select::make('from_account')
                            ->label('From Account')
                            ->options([
                                'main_vault' => 'Main Vault',
                                'branch_1' => 'Branch 1',
                                'branch_2' => 'Branch 2',
                                'online_wallet' => 'Online Wallet',
                            ])
                            ->required(),

                        Forms\Components\Select::make('to_account')
                            ->label('To Account')
                            ->options([
                                'main_vault' => 'Main Vault',
                                'branch_1' => 'Branch 1',
                                'branch_2' => 'Branch 2',
                                'online_wallet' => 'Online Wallet',
                            ])
                            ->required(),

                        Forms\Components\TextInput::make('transfer_reason')
                            ->label('Transfer Reason')
                            ->maxLength(255),
                    ])
                    ->columns(2)
                    ->visible(function (Forms\Get $get) {
                        $exchangeType = $get('exchange_type');
                        if (!$exchangeType) return false;
                        $type = is_string($exchangeType) ? ExchangeType::from($exchangeType) : $exchangeType;
                        return $type === ExchangeType::INTERNAL_TRANSFER;
                    }),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('exchange_number')
                    ->label('Exchange #')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('client.name')
                    ->label('Client')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\BadgeColumn::make('exchange_type')
                    ->label('Type'),

                Tables\Columns\BadgeColumn::make('status')
                    ->label('Status'),

                Tables\Columns\TextColumn::make('from_amount')
                    ->label('From Amount')
                    ->formatStateUsing(fn ($record) => $record->formatted_from_amount),

                Tables\Columns\TextColumn::make('to_amount')
                    ->label('To Amount')
                    ->formatStateUsing(fn ($record) => $record->formatted_to_amount),

                Tables\Columns\TextColumn::make('exchange_rate')
                    ->label('Rate')
                    ->numeric(6),

                Tables\Columns\TextColumn::make('exchange_date')
                    ->label('Date')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options(ExchangeStatus::class),
                Tables\Filters\SelectFilter::make('exchange_type')
                    ->options(ExchangeType::class),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn ($record) => $record->canBeModified()),
                Tables\Actions\Action::make('approve')
                    ->label('Approve')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->visible(fn ($record) => $record->isPending() && app(UserPermissionService::class)->canApproveExchanges())
                    ->requiresConfirmation()
                    ->action(function ($record) {
                        $record->update(['status' => ExchangeStatus::PROCESSING]);
                    }),
                Tables\Actions\Action::make('cancel')
                    ->label('Cancel')
                    ->icon('heroicon-o-x-circle')
                    ->color('danger')
                    ->visible(fn ($record) => $record->canBeCancelled() && app(UserPermissionService::class)->hasPermission('exchange:cancel'))
                    ->requiresConfirmation()
                    ->action(function ($record) {
                        $record->update(['status' => ExchangeStatus::CANCELLED]);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCurrencyExchanges::route('/'),
            'create' => Pages\CreateCurrencyExchange::route('/create'),
            'view' => Pages\ViewCurrencyExchange::route('/{record}'),
            'edit' => Pages\EditCurrencyExchange::route('/{record}/edit'),
        ];
    }
}
