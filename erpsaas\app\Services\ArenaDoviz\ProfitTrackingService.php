<?php

namespace App\Services\ArenaDoviz;

use App\Enums\ArenaDoviz\ExchangeType;
use App\Models\ArenaDoviz\CurrencyExchange;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ProfitTrackingService
{
    /**
     * Calculate profit for a specific currency pair and date range
     */
    public function calculateProfit(string $currencyPair, string $startDate, string $endDate): array
    {
        [$fromCurrency, $toCurrency] = explode('_TO_', $currencyPair);
        
        $buyTransactions = $this->getBuyTransactions($fromCurrency, $toCurrency, $startDate, $endDate);
        $sellTransactions = $this->getSellTransactions($fromCurrency, $toCurrency, $startDate, $endDate);
        
        return [
            'currency_pair' => $currencyPair,
            'period' => ['start' => $startDate, 'end' => $endDate],
            'buy_summary' => $this->summarizeTransactions($buyTransactions, 'buy'),
            'sell_summary' => $this->summarizeTransactions($sellTransactions, 'sell'),
            'profit_analysis' => $this->calculateProfitAnalysis($buyTransactions, $sellTransactions),
            'recommendations' => $this->generateRecommendations($buyTransactions, $sellTransactions),
        ];
    }

    /**
     * Get buy transactions for currency pair
     */
    private function getBuyTransactions(string $fromCurrency, string $toCurrency, string $startDate, string $endDate): Collection
    {
        return CurrencyExchange::where('exchange_type', ExchangeType::BUY)
            ->where('from_currency_code', 'LIKE', $fromCurrency . '%')
            ->where('to_currency_code', 'LIKE', $toCurrency . '%')
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->with(['client', 'location'])
            ->get();
    }

    /**
     * Get sell transactions for currency pair
     */
    private function getSellTransactions(string $fromCurrency, string $toCurrency, string $startDate, string $endDate): Collection
    {
        return CurrencyExchange::where('exchange_type', ExchangeType::SELL)
            ->where('from_currency_code', 'LIKE', $toCurrency . '%')
            ->where('to_currency_code', 'LIKE', $fromCurrency . '%')
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->with(['client', 'location'])
            ->get();
    }

    /**
     * Summarize transactions by type
     */
    private function summarizeTransactions(Collection $transactions, string $type): array
    {
        $totalAmount = $transactions->sum('from_amount');
        $totalValue = $transactions->sum('to_amount');
        $totalCommission = $transactions->sum('commission_amount');
        $avgRate = $transactions->avg('exchange_rate');
        $transactionCount = $transactions->count();

        return [
            'type' => $type,
            'transaction_count' => $transactionCount,
            'total_amount' => $totalAmount,
            'total_value' => $totalValue,
            'total_commission' => $totalCommission,
            'average_rate' => $avgRate,
            'formatted_total_amount' => \App\Helpers\ArenaDoviz\NumberFormatter::formatNumber($totalAmount, 2),
            'formatted_total_value' => \App\Helpers\ArenaDoviz\NumberFormatter::formatNumber($totalValue, 2),
            'formatted_commission' => \App\Helpers\ArenaDoviz\NumberFormatter::formatNumber($totalCommission, 2),
            'formatted_avg_rate' => \App\Helpers\ArenaDoviz\NumberFormatter::formatExchangeRate($avgRate),
        ];
    }

    /**
     * Calculate profit analysis
     */
    private function calculateProfitAnalysis(Collection $buyTransactions, Collection $sellTransactions): array
    {
        $buyAvgRate = $buyTransactions->avg('exchange_rate') ?: 0;
        $sellAvgRate = $sellTransactions->avg('exchange_rate') ?: 0;
        
        $buyVolume = $buyTransactions->sum('from_amount');
        $sellVolume = $sellTransactions->sum('from_amount');
        
        $buyValue = $buyTransactions->sum('to_amount');
        $sellValue = $sellTransactions->sum('to_amount');
        
        // Calculate theoretical profit based on rate difference
        $rateSpread = $sellAvgRate - $buyAvgRate;
        $rateSpreadPercent = $buyAvgRate > 0 ? ($rateSpread / $buyAvgRate) * 100 : 0;
        
        // Calculate actual profit (simplified - assumes matched volumes)
        $matchedVolume = min($buyVolume, $sellVolume);
        $theoreticalProfit = $matchedVolume * $rateSpread;
        
        // Calculate commission profit
        $totalCommissions = $buyTransactions->sum('commission_amount') + $sellTransactions->sum('commission_amount');
        
        return [
            'buy_avg_rate' => $buyAvgRate,
            'sell_avg_rate' => $sellAvgRate,
            'rate_spread' => $rateSpread,
            'rate_spread_percent' => $rateSpreadPercent,
            'buy_volume' => $buyVolume,
            'sell_volume' => $sellVolume,
            'matched_volume' => $matchedVolume,
            'unmatched_buy' => max(0, $buyVolume - $sellVolume),
            'unmatched_sell' => max(0, $sellVolume - $buyVolume),
            'theoretical_profit' => $theoreticalProfit,
            'commission_profit' => $totalCommissions,
            'total_estimated_profit' => $theoreticalProfit + $totalCommissions,
            'formatted_spread' => \App\Helpers\ArenaDoviz\NumberFormatter::formatExchangeRate($rateSpread),
            'formatted_profit' => \App\Helpers\ArenaDoviz\NumberFormatter::formatNumber($theoreticalProfit + $totalCommissions, 2),
        ];
    }

    /**
     * Generate recommendations based on analysis
     */
    private function generateRecommendations(Collection $buyTransactions, Collection $sellTransactions): array
    {
        $recommendations = [];
        
        $buyVolume = $buyTransactions->sum('from_amount');
        $sellVolume = $sellTransactions->sum('from_amount');
        
        // Volume imbalance recommendations
        if ($buyVolume > $sellVolume * 1.2) {
            $recommendations[] = [
                'type' => 'volume_imbalance',
                'priority' => 'high',
                'message' => 'High buy volume with insufficient sell transactions. Consider finding more sell opportunities.',
                'excess_volume' => $buyVolume - $sellVolume,
            ];
        } elseif ($sellVolume > $buyVolume * 1.2) {
            $recommendations[] = [
                'type' => 'volume_imbalance',
                'priority' => 'high',
                'message' => 'High sell volume with insufficient buy transactions. Consider increasing buy operations.',
                'excess_volume' => $sellVolume - $buyVolume,
            ];
        }
        
        // Rate analysis recommendations
        $buyAvgRate = $buyTransactions->avg('exchange_rate') ?: 0;
        $sellAvgRate = $sellTransactions->avg('exchange_rate') ?: 0;
        
        if ($sellAvgRate <= $buyAvgRate) {
            $recommendations[] = [
                'type' => 'rate_analysis',
                'priority' => 'critical',
                'message' => 'Sell rate is not higher than buy rate. Review pricing strategy.',
                'rate_difference' => $sellAvgRate - $buyAvgRate,
            ];
        }
        
        // Commission optimization
        $avgCommissionRate = ($buyTransactions->avg('commission_rate') + $sellTransactions->avg('commission_rate')) / 2;
        if ($avgCommissionRate < 0.25) {
            $recommendations[] = [
                'type' => 'commission',
                'priority' => 'medium',
                'message' => 'Commission rates are below recommended minimum. Consider increasing rates.',
                'current_rate' => $avgCommissionRate,
                'recommended_rate' => 0.25,
            ];
        }
        
        return $recommendations;
    }

    /**
     * Get profit summary by location
     */
    public function getProfitByLocation(string $startDate, string $endDate): array
    {
        $locations = DB::table('currency_exchanges')
            ->join('locations', 'currency_exchanges.location_id', '=', 'locations.id')
            ->select('locations.id', 'locations.name', 'locations.city')
            ->whereBetween('currency_exchanges.exchange_date', [$startDate, $endDate])
            ->groupBy('locations.id', 'locations.name', 'locations.city')
            ->get();

        $locationProfits = [];
        
        foreach ($locations as $location) {
            $buyTransactions = CurrencyExchange::where('location_id', $location->id)
                ->where('exchange_type', ExchangeType::BUY)
                ->whereBetween('exchange_date', [$startDate, $endDate])
                ->get();
                
            $sellTransactions = CurrencyExchange::where('location_id', $location->id)
                ->where('exchange_type', ExchangeType::SELL)
                ->whereBetween('exchange_date', [$startDate, $endDate])
                ->get();
            
            $locationProfits[] = [
                'location' => $location,
                'buy_summary' => $this->summarizeTransactions($buyTransactions, 'buy'),
                'sell_summary' => $this->summarizeTransactions($sellTransactions, 'sell'),
                'profit_analysis' => $this->calculateProfitAnalysis($buyTransactions, $sellTransactions),
            ];
        }
        
        return $locationProfits;
    }

    /**
     * Get daily profit trends
     */
    public function getDailyProfitTrends(string $startDate, string $endDate): array
    {
        $dailyData = DB::table('currency_exchanges')
            ->select(
                DB::raw('DATE(exchange_date) as date'),
                DB::raw('SUM(CASE WHEN exchange_type = "buy" THEN commission_amount ELSE 0 END) as buy_commission'),
                DB::raw('SUM(CASE WHEN exchange_type = "sell" THEN commission_amount ELSE 0 END) as sell_commission'),
                DB::raw('COUNT(CASE WHEN exchange_type = "buy" THEN 1 END) as buy_count'),
                DB::raw('COUNT(CASE WHEN exchange_type = "sell" THEN 1 END) as sell_count'),
                DB::raw('AVG(CASE WHEN exchange_type = "buy" THEN exchange_rate END) as avg_buy_rate'),
                DB::raw('AVG(CASE WHEN exchange_type = "sell" THEN exchange_rate END) as avg_sell_rate')
            )
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->whereIn('exchange_type', ['buy', 'sell'])
            ->groupBy(DB::raw('DATE(exchange_date)'))
            ->orderBy('date')
            ->get();

        return $dailyData->map(function ($day) {
            $totalCommission = $day->buy_commission + $day->sell_commission;
            $rateSpread = ($day->avg_sell_rate ?: 0) - ($day->avg_buy_rate ?: 0);
            
            return [
                'date' => $day->date,
                'buy_commission' => $day->buy_commission,
                'sell_commission' => $day->sell_commission,
                'total_commission' => $totalCommission,
                'buy_count' => $day->buy_count,
                'sell_count' => $day->sell_count,
                'avg_buy_rate' => $day->avg_buy_rate,
                'avg_sell_rate' => $day->avg_sell_rate,
                'rate_spread' => $rateSpread,
                'formatted_commission' => \App\Helpers\ArenaDoviz\NumberFormatter::formatNumber($totalCommission, 2),
                'formatted_spread' => \App\Helpers\ArenaDoviz\NumberFormatter::formatExchangeRate($rateSpread),
            ];
        })->toArray();
    }

    /**
     * Get top performing currency pairs
     */
    public function getTopCurrencyPairs(string $startDate, string $endDate, int $limit = 10): array
    {
        $pairs = DB::table('currency_exchanges')
            ->select(
                DB::raw('CONCAT(from_currency_code, "_TO_", to_currency_code) as currency_pair'),
                DB::raw('SUM(commission_amount) as total_commission'),
                DB::raw('COUNT(*) as transaction_count'),
                DB::raw('AVG(exchange_rate) as avg_rate'),
                DB::raw('SUM(from_amount) as total_volume')
            )
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->groupBy('from_currency_code', 'to_currency_code')
            ->orderBy('total_commission', 'desc')
            ->limit($limit)
            ->get();

        return $pairs->map(function ($pair) {
            return [
                'currency_pair' => $pair->currency_pair,
                'total_commission' => $pair->total_commission,
                'transaction_count' => $pair->transaction_count,
                'avg_rate' => $pair->avg_rate,
                'total_volume' => $pair->total_volume,
                'formatted_commission' => \App\Helpers\ArenaDoviz\NumberFormatter::formatNumber($pair->total_commission, 2),
                'formatted_volume' => \App\Helpers\ArenaDoviz\NumberFormatter::formatNumber($pair->total_volume, 2),
                'formatted_rate' => \App\Helpers\ArenaDoviz\NumberFormatter::formatExchangeRate($pair->avg_rate),
            ];
        })->toArray();
    }
}
