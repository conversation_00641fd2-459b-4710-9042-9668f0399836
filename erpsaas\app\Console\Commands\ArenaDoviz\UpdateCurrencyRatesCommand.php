<?php

namespace App\Console\Commands\ArenaDoviz;

use App\Services\ArenaDoviz\RealTimeRateService;
use Illuminate\Console\Command;

class UpdateCurrencyRatesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'arena-doviz:update-rates {--currency= : Specific currency to update} {--force : Force update even if recently updated}';

    /**
     * The console command description.
     */
    protected $description = 'Update Arena Doviz currency exchange rates from external APIs';

    /**
     * Execute the console command.
     */
    public function handle(RealTimeRateService $rateService)
    {
        $this->info('🔄 Updating Arena Doviz Currency Rates');
        $this->info('=====================================');

        $specificCurrency = $this->option('currency');
        $force = $this->option('force');

        if ($specificCurrency) {
            $this->info("Updating specific currency: {$specificCurrency}");
            // Implementation for specific currency update
            $this->error('Specific currency update not yet implemented');
            return 1;
        }

        // Check if rates need updating (unless forced)
        if (!$force && !$rateService->ratesNeedUpdate()) {
            $this->info('✅ All rates are up to date. Use --force to update anyway.');
            return 0;
        }

        $this->info('📊 Fetching rates from external APIs...');
        
        $results = $rateService->updateAllRates();
        
        // Display results in a table
        $tableData = [];
        $successCount = 0;
        $errorCount = 0;

        foreach ($results as $result) {
            $status = $result['success'] ? '✅ Success' : '❌ Failed';
            $change = '';
            
            if ($result['success'] && isset($result['old_rate'], $result['new_rate'])) {
                $changePercent = (($result['new_rate'] - $result['old_rate']) / $result['old_rate']) * 100;
                $changeIcon = $changePercent > 0 ? '📈' : ($changePercent < 0 ? '📉' : '➡️');
                $change = sprintf('%s %.2f%%', $changeIcon, abs($changePercent));
            }

            $tableData[] = [
                'Currency' => $result['currency'],
                'Status' => $status,
                'Old Rate' => $result['old_rate'] ? number_format($result['old_rate'], 6) : 'N/A',
                'New Rate' => $result['new_rate'] ? number_format($result['new_rate'], 6) : 'N/A',
                'Change' => $change,
                'Message' => $result['message'],
            ];

            if ($result['success']) {
                $successCount++;
            } else {
                $errorCount++;
            }
        }

        $this->table([
            'Currency', 'Status', 'Old Rate', 'New Rate', 'Change', 'Message'
        ], $tableData);

        // Summary
        $this->info('');
        $this->info('📋 Update Summary');
        $this->info('=================');
        $this->info("✅ Successful updates: {$successCount}");
        
        if ($errorCount > 0) {
            $this->error("❌ Failed updates: {$errorCount}");
        }

        $this->info("📊 Total currencies processed: " . count($results));

        // Check for significant changes
        $alerts = $rateService->getRateChangeAlerts(2.0);
        
        if (!empty($alerts)) {
            $this->warn('');
            $this->warn('⚠️  Significant Rate Changes Detected (>2%)');
            $this->warn('===============================================');
            
            foreach ($alerts as $alert) {
                $direction = $alert['direction'] === 'up' ? '📈' : '📉';
                $this->warn(sprintf(
                    '%s %s: %.2f%% change (%.6f → %.6f)',
                    $direction,
                    $alert['currency'],
                    $alert['change_percent'],
                    $alert['old_rate'],
                    $alert['new_rate']
                ));
            }
        }

        return $errorCount > 0 ? 1 : 0;
    }
}
