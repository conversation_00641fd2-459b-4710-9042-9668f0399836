<?php

namespace Database\Seeders\ArenaDoviz;

use App\Enums\ArenaDoviz\DeliveryStatus;
use App\Models\Common\Client;
use App\Models\ArenaDoviz\Courier;
use App\Models\ArenaDoviz\CurrencyExchange;
use App\Models\ArenaDoviz\Delivery;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class DeliverySeeder extends Seeder
{
    /**
     * Seed delivery records for Arena Doviz courier operations.
     * 
     * Creates realistic delivery data including:
     * - Various delivery statuses and tracking
     * - Courier assignments and routing
     * - Delivery addresses and recipient information
     * - Proof of delivery documentation
     * - Delivery fees and special instructions
     */
    public function run(): void
    {
        $this->command->info('📦 Seeding Arena Doviz deliveries...');

        // Set a temporary authenticated user for the CompanyOwned trait
        $adminUser = \App\Models\User::where('email', '<EMAIL>')->first();
        if ($adminUser) {
            Auth::login($adminUser);
        }

        $clients = Client::where('company_id', 1)->get();
        $couriers = Courier::where('company_id', 1)->get();
        $exchanges = CurrencyExchange::where('company_id', 1)
            ->whereNotNull('delivery_method')
            ->where('delivery_method', '!=', 'pickup')
            ->limit(30)
            ->get();
        
        if ($clients->isEmpty() || $couriers->isEmpty()) {
            $this->command->warn('No clients or couriers found. Please run ClientProfileSeeder and CourierSeeder first.');
            return;
        }
        
        $deliveryCount = 0;
        
        foreach ($exchanges as $exchange) {
            // Not all exchanges need deliveries (about 60% do)
            if (rand(1, 100) > 60) {
                continue;
            }
            
            $this->createDelivery($exchange, $clients, $couriers);
            $deliveryCount++;
        }
        
        // Create some additional standalone deliveries
        $this->createStandaloneDeliveries($clients, $couriers);
        $deliveryCount += 8;

        // Logout the temporary user
        Auth::logout();

        $this->command->info("✅ Created {$deliveryCount} delivery records");
    }
    
    private function createDelivery(CurrencyExchange $exchange, $clients, $couriers): void
    {
        $client = $clients->find($exchange->client_id);
        $courier = $this->assignCourier($couriers, $exchange->created_at);
        $status = $this->getDeliveryStatus($exchange->created_at);
        
        $deliveryData = $this->generateDeliveryData($client, $exchange, $status);
        
        Delivery::create([
            'company_id' => 1,
            'currency_exchange_id' => $exchange->id,
            'client_id' => $client->id,
            'courier_id' => $courier?->id,
            'status' => $status,
            'delivery_type' => $this->getDeliveryType(),
            'pickup_address' => $this->getPickupAddress(),
            'delivery_address' => $deliveryData['delivery_address'],
            'recipient_name' => $deliveryData['recipient_name'],
            'recipient_phone' => $deliveryData['recipient_phone'],
            'delivery_amount' => $exchange->to_amount,
            'currency_code' => $exchange->to_currency_code,
            'delivery_fee' => $this->calculateDeliveryFee($exchange->to_amount),
            'special_instructions' => $this->getSpecialInstructions(),
            'scheduled_date' => $exchange->created_at->addHours(rand(2, 48)),
            'assigned_at' => $courier ? $exchange->created_at->addHours(rand(1, 6)) : null,
            'picked_up_at' => $this->getTimestamp($status, 'picked_up', $exchange->created_at),
            'in_transit_at' => $this->getTimestamp($status, 'in_transit', $exchange->created_at),
            'arrived_at' => $this->getTimestamp($status, 'arrived', $exchange->created_at),
            'delivered_at' => $this->getTimestamp($status, 'delivered', $exchange->created_at),
            'delivery_proof_photo' => $this->getProofPhoto($status),
            'recipient_signature' => $this->getRecipientSignature($status),
            'delivery_notes' => $this->getDeliveryNotes($status),
            'created_by' => $exchange->created_by,
            'created_at' => $exchange->created_at,
            'updated_at' => $exchange->updated_at,
        ]);
    }
    
    private function assignCourier($couriers, Carbon $date): ?Courier
    {
        // 90% of deliveries have assigned couriers
        if (rand(1, 100) > 90) {
            return null;
        }
        
        // Filter available couriers based on date (simulate availability)
        $availableCouriers = $couriers->filter(function ($courier) use ($date) {
            // Simulate courier availability based on their status and working hours
            if ($courier->status->value === 'inactive') {
                return false;
            }
            
            $dayOfWeek = strtolower($date->format('l'));
            $workingHours = $courier->working_hours;
            
            return isset($workingHours[$dayOfWeek]) && $workingHours[$dayOfWeek] !== 'off';
        });
        
        return $availableCouriers->isNotEmpty() ? $availableCouriers->random() : null;
    }
    
    private function getDeliveryStatus(Carbon $createdAt): DeliveryStatus
    {
        $hoursAgo = $createdAt->diffInHours(Carbon::now());
        
        if ($hoursAgo > 72) {
            // Old deliveries: mostly delivered
            $statuses = [
                ['status' => DeliveryStatus::DELIVERED, 'weight' => 85],
                ['status' => DeliveryStatus::CANCELLED, 'weight' => 10],
                ['status' => DeliveryStatus::FAILED, 'weight' => 5],
            ];
        } elseif ($hoursAgo > 24) {
            // Recent deliveries: mix of statuses
            $statuses = [
                ['status' => DeliveryStatus::DELIVERED, 'weight' => 60],
                ['status' => DeliveryStatus::IN_TRANSIT, 'weight' => 20],
                ['status' => DeliveryStatus::PICKED_UP, 'weight' => 10],
                ['status' => DeliveryStatus::ASSIGNED, 'weight' => 5],
                ['status' => DeliveryStatus::CANCELLED, 'weight' => 3],
                ['status' => DeliveryStatus::FAILED, 'weight' => 2],
            ];
        } else {
            // Very recent: more in progress
            $statuses = [
                ['status' => DeliveryStatus::DELIVERED, 'weight' => 20],
                ['status' => DeliveryStatus::IN_TRANSIT, 'weight' => 25],
                ['status' => DeliveryStatus::PICKED_UP, 'weight' => 20],
                ['status' => DeliveryStatus::ASSIGNED, 'weight' => 25],
                ['status' => DeliveryStatus::PENDING, 'weight' => 8],
                ['status' => DeliveryStatus::CANCELLED, 'weight' => 1],
                ['status' => DeliveryStatus::FAILED, 'weight' => 1],
            ];
        }

        $rand = rand(1, 100);
        $cumulative = 0;

        foreach ($statuses as $statusData) {
            $cumulative += $statusData['weight'];
            if ($rand <= $cumulative) {
                return $statusData['status'];
            }
        }
        
        return DeliveryStatus::DELIVERED;
    }
    
    private function generateDeliveryData(Client $client, CurrencyExchange $exchange, DeliveryStatus $status): array
    {
        $contact = $client->primaryContact;
        
        return [
            'delivery_address' => $exchange->delivery_address ?? $this->generateDeliveryAddress(),
            'recipient_name' => $contact ? $contact->first_name . ' ' . $contact->last_name : $client->name,
            'recipient_phone' => $contact ? ($contact->phones[0] ?? '+90 ************') : '+90 ************',
        ];
    }
    
    private function getDeliveryType(): string
    {
        $types = ['standard', 'express', 'same_day', 'scheduled'];
        return $types[array_rand($types)];
    }
    
    private function getPickupAddress(): string
    {
        $addresses = [
            'Arena Doviz Main Office - Levent Mahallesi, Büyükdere Caddesi No:201, 34394 Şişli/İstanbul',
            'Arena Doviz Branch Office - Bağdat Caddesi No:150, 34728 Kadıköy/İstanbul',
            'Arena Doviz Vault - Maslak Mahallesi, Büyükdere Caddesi No:255, 34398 Sarıyer/İstanbul',
        ];
        
        return $addresses[array_rand($addresses)];
    }
    
    private function generateDeliveryAddress(): string
    {
        $addresses = [
            'Nişantaşı Mahallesi, Teşvikiye Caddesi No:45, 34365 Şişli/İstanbul',
            'Etiler Mahallesi, Nispetiye Caddesi No:78, 34337 Beşiktaş/İstanbul',
            'Bağdat Caddesi No:234, 34728 Kadıköy/İstanbul',
            'Acıbadem Mahallesi, Çeçen Sokak No:12, 34660 Üsküdar/İstanbul',
            'Levent Mahallesi, Büyükdere Caddesi No:120, 34394 Şişli/İstanbul',
            'Bebek Mahallesi, Cevdet Paşa Caddesi No:89, 34342 Beşiktaş/İstanbul',
            'Fenerbahçe Mahallesi, Kalamış Caddesi No:56, 34726 Kadıköy/İstanbul',
            'Maslak Mahallesi, Büyükdere Caddesi No:300, 34398 Sarıyer/İstanbul',
        ];
        
        return $addresses[array_rand($addresses)];
    }
    
    private function calculateDeliveryFee(float $amount): float
    {
        // Base fee + percentage of amount
        $baseFee = 50.00; // 50 TRY base fee
        $percentageFee = $amount * 0.001; // 0.1% of amount
        
        return min($baseFee + $percentageFee, 500.00); // Max 500 TRY
    }
    
    private function getSpecialInstructions(): ?string
    {
        if (rand(1, 100) > 40) {
            return null; // 60% have no special instructions
        }
        
        $instructions = [
            'Call recipient 30 minutes before delivery',
            'Delivery to office reception - ask for recipient',
            'Ring doorbell twice - apartment building',
            'Urgent delivery - contact immediately upon arrival',
            'Fragile items - handle with care',
            'High-value delivery - require ID verification',
            'Weekend delivery - confirm availability first',
            'Evening delivery preferred after 18:00',
        ];
        
        return $instructions[array_rand($instructions)];
    }
    
    private function getTimestamp(DeliveryStatus $status, string $stage, Carbon $baseTime): ?Carbon
    {
        $statusOrder = [
            'assigned' => 1,
            'picked_up' => 2,
            'in_transit' => 3,
            'arrived' => 4,
            'delivered' => 5,
        ];
        
        $currentStatusOrder = match ($status) {
            DeliveryStatus::ASSIGNED => 1,
            DeliveryStatus::PICKED_UP => 2,
            DeliveryStatus::IN_TRANSIT => 3,
            DeliveryStatus::ARRIVED => 4,
            DeliveryStatus::DELIVERED => 5,
            default => 0,
        };
        
        $stageOrder = $statusOrder[$stage] ?? 0;
        
        if ($stageOrder > $currentStatusOrder) {
            return null;
        }
        
        $hoursToAdd = match ($stage) {
            'assigned' => rand(1, 6),
            'picked_up' => rand(2, 8),
            'in_transit' => rand(3, 12),
            'arrived' => rand(4, 16),
            'delivered' => rand(5, 24),
            default => 0,
        };
        
        return $baseTime->copy()->addHours($hoursToAdd);
    }
    
    private function getProofPhoto(DeliveryStatus $status): ?string
    {
        if (!$status->isComplete() || rand(1, 100) > 80) {
            return null; // 20% have proof photos
        }
        
        return 'deliveries/' . date('Y/m/d') . '/proof_' . rand(100000, 999999) . '.jpg';
    }
    
    private function getRecipientSignature(DeliveryStatus $status): ?string
    {
        if (!$status->isComplete() || rand(1, 100) > 70) {
            return null; // 30% have signatures
        }
        
        // Base64 encoded signature placeholder
        return base64_encode('signature_data_' . rand(1000, 9999));
    }
    
    private function getDeliveryNotes(DeliveryStatus $status): ?string
    {
        $notes = match ($status) {
            DeliveryStatus::DELIVERED => [
                'Delivery completed successfully',
                'Package delivered to recipient in person',
                'Delivered to office reception as requested',
                'Customer satisfied with delivery service',
            ],
            DeliveryStatus::FAILED => [
                'Recipient not available - multiple attempts made',
                'Incorrect address provided',
                'Access denied to building',
                'Customer refused delivery',
            ],
            DeliveryStatus::CANCELLED => [
                'Delivery cancelled by customer request',
                'Customer picked up from office instead',
                'Delivery postponed to later date',
            ],
            default => [
                'Delivery in progress',
                'Standard delivery process',
                'No special notes',
            ],
        };
        
        return rand(1, 100) <= 60 ? $notes[array_rand($notes)] : null;
    }
    
    private function createStandaloneDeliveries($clients, $couriers): void
    {
        // Create some deliveries without associated currency exchanges
        // These represent direct delivery requests
        
        for ($i = 0; $i < 8; $i++) {
            $client = $clients->random();
            $courier = $this->assignCourier($couriers, Carbon::now()->subDays(rand(1, 15)));
            $createdAt = Carbon::now()->subDays(rand(1, 15))->addHours(rand(9, 17));
            $status = $this->getDeliveryStatus($createdAt);
            
            // Create a dummy currency exchange for the delivery
            $exchange = CurrencyExchange::create([
                'company_id' => 1,
                'client_id' => $client->id,
                'exchange_type' => 'transfer',
                'status' => 'completed',
                'from_currency_code' => 'USD',
                'to_currency_code' => 'TRY',
                'from_amount' => rand(500, 5000),
                'to_amount' => rand(500, 5000) * 32.5,
                'exchange_rate' => 32.5,
                'commission_rate' => 0.002,
                'commission_amount' => rand(500, 5000) * 32.5 * 0.002,
                'net_amount' => rand(500, 5000) * 32.5 * 0.998,
                'notes' => 'Direct delivery request',
                'exchange_date' => $createdAt,
                'settlement_date' => $createdAt,
                'delivery_method' => 'courier',
                'created_by' => rand(1, 5),
                'created_at' => $createdAt,
                'updated_at' => $createdAt,
            ]);
            
            $this->createDelivery($exchange, $clients, $couriers);
        }
    }
}
