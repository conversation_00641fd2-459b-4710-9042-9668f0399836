@props(['form'])

<div class="space-y-6">
    <!-- Transaction Type Selection -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Transaction Type</h3>
        <div class="grid grid-cols-5 gap-4">
            <label class="flex items-center space-x-2 cursor-pointer">
                <input type="radio" name="transaction_type" value="buy" class="text-blue-600 focus:ring-blue-500">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Buy</span>
            </label>
            <label class="flex items-center space-x-2 cursor-pointer">
                <input type="radio" name="transaction_type" value="sell" class="text-blue-600 focus:ring-blue-500">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Sell</span>
            </label>
            <label class="flex items-center space-x-2 cursor-pointer">
                <input type="radio" name="transaction_type" value="combined_exchange" class="text-blue-600 focus:ring-blue-500">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Combined Buy/Sell</span>
            </label>
            <label class="flex items-center space-x-2 cursor-pointer">
                <input type="radio" name="transaction_type" value="transfer" class="text-blue-600 focus:ring-blue-500">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Transfer</span>
            </label>
            <label class="flex items-center space-x-2 cursor-pointer">
                <input type="radio" name="transaction_type" value="swift" class="text-blue-600 focus:ring-blue-500">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">SWIFT</span>
            </label>
        </div>
    </div>

    <!-- Customer Information -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Customer Information</h3>
        <div class="grid grid-cols-2 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">From Customer</label>
                <div class="flex space-x-2">
                    <input type="text" placeholder="Search Customer..." class="flex-1 rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:border-blue-500 focus:ring-blue-500">
                    <button type="button" class="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        + New
                    </button>
                </div>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">To Customer</label>
                <div class="flex space-x-2">
                    <input type="text" placeholder="Search Customer..." class="flex-1 rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:border-blue-500 focus:ring-blue-500">
                    <button type="button" class="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        + New
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Currency and Location -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Currency & Location</h3>
        <div class="grid grid-cols-2 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Currency</label>
                <select class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:border-blue-500 focus:ring-blue-500">
                    <option value="USD_IST">USD_IST - US Dollar Istanbul</option>
                    <option value="USD_TBZ">USD_TBZ - US Dollar Tabriz</option>
                    <option value="USD_THR">USD_THR - US Dollar Tehran</option>
                    <option value="USD_DXB">USD_DXB - US Dollar Dubai</option>
                    <option value="EUR_IST">EUR_IST - Euro Istanbul</option>
                    <option value="EUR_TBZ">EUR_TBZ - Euro Tabriz</option>
                    <option value="TRY_IST">TRY_IST - Turkish Lira Istanbul</option>
                    <option value="IRR_TBZ">IRR_TBZ - Iranian Rial Tabriz</option>
                    <option value="IRR_THR">IRR_THR - Iranian Rial Tehran</option>
                    <option value="AED_DXB">AED_DXB - UAE Dirham Dubai</option>
                    <option value="CNY_SHA">CNY_SHA - Chinese Yuan Shanghai</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Location</label>
                <select class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:border-blue-500 focus:ring-blue-500">
                    <option value="IST">Istanbul (IST)</option>
                    <option value="TBZ">Tabriz (TBZ)</option>
                    <option value="THR">Tehran (THR)</option>
                    <option value="DXB">Dubai (DXB)</option>
                    <option value="SHA">Shanghai (SHA)</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Amount and Rates -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Amount & Rates</h3>
        <div class="grid grid-cols-2 gap-4 mb-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Amount</label>
                <input type="text" placeholder="1,000,000.00" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:border-blue-500 focus:ring-blue-500 font-mono amount-input">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Rate</label>
                <input type="text" placeholder="93,200.00" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:border-blue-500 focus:ring-blue-500 font-mono amount-input">
            </div>
        </div>
        <div class="grid grid-cols-2 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Buy Rate</label>
                <input type="text" placeholder="93,200" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:border-blue-500 focus:ring-blue-500 font-mono">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Sell Rate</label>
                <input type="text" placeholder="93,900" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:border-blue-500 focus:ring-blue-500 font-mono">
            </div>
        </div>
    </div>

    <!-- Commission -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Commission</h3>
        <div class="grid grid-cols-3 gap-4 mb-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Commission</label>
                <input type="text" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:border-blue-500 focus:ring-blue-500">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Type</label>
                <div class="flex space-x-4">
                    <label class="flex items-center">
                        <input type="radio" name="commission_type" value="percentage" class="text-blue-600 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">%</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="commission_type" value="fixed" class="text-blue-600 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Fixed</span>
                    </label>
                </div>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Currency</label>
                <select class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:border-blue-500 focus:ring-blue-500">
                    <option value="USD_IST">USD_IST</option>
                    <option value="EUR_IST">EUR_IST</option>
                    <option value="TRY_IST">TRY_IST</option>
                </select>
            </div>
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Deduct From</label>
            <div class="flex space-x-4">
                <label class="flex items-center">
                    <input type="radio" name="deduct_from" value="source" class="text-blue-600 focus:ring-blue-500">
                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Source Currency</span>
                </label>
                <label class="flex items-center">
                    <input type="radio" name="deduct_from" value="destination" class="text-blue-600 focus:ring-blue-500">
                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Destination Currency</span>
                </label>
            </div>
        </div>
    </div>

    <!-- Additional Information -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Additional Information</h3>
        <div class="grid grid-cols-2 gap-4 mb-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Iranian Bank</label>
                <select class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:border-blue-500 focus:ring-blue-500">
                    <option value="melli">Melli Bank</option>
                    <option value="saderat">Saderat Bank</option>
                    <option value="tejarat">Tejarat Bank</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Transaction #</label>
                <input type="text" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:border-blue-500 focus:ring-blue-500">
            </div>
        </div>
        <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Batch ID</label>
            <input type="text" placeholder="IRR_BATCH_001" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:border-blue-500 focus:ring-blue-500">
            <p class="text-xs text-gray-500 mt-1">For IRR transactions</p>
        </div>
        <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Notes</label>
            <textarea rows="3" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:border-blue-500 focus:ring-blue-500"></textarea>
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Documents</label>
            <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4 text-center">
                <input type="file" multiple class="hidden" id="documents">
                <label for="documents" class="cursor-pointer">
                    <div class="text-gray-500 dark:text-gray-400">
                        <svg class="mx-auto h-8 w-8 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                        <span class="text-sm">Choose Files or drag and drop</span>
                        <p class="text-xs text-gray-400 mt-1">Multiple files supported</p>
                    </div>
                </label>
            </div>
        </div>
    </div>

    <!-- Transaction Preview -->
    <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800 p-6">
        <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-4">Transaction Preview</h3>
        <div class="space-y-2 text-sm">
            <div class="flex justify-between">
                <span class="text-blue-700 dark:text-blue-300">Buy: 1,000,000 IRR → $1,070.92 (Rate: 93,200)</span>
            </div>
            <div class="flex justify-between">
                <span class="text-blue-700 dark:text-blue-300">Sell: $1,070.92 → 1,005,000 IRR (Rate: 93,900)</span>
            </div>
            <div class="flex justify-between">
                <span class="text-blue-700 dark:text-blue-300">Commission: $15.00 (1.5% from USD)</span>
            </div>
            <div class="flex justify-between font-semibold border-t border-blue-200 dark:border-blue-700 pt-2">
                <span class="text-blue-900 dark:text-blue-100">Company Profit: $5.92</span>
            </div>
        </div>
    </div>
</div>

@vite('resources/js/arena-doviz-formatting.js')
