<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('currency_exchanges', function (Blueprint $table) {
            // Add location support
            $table->foreignId('location_id')->nullable()->after('client_id')->constrained('locations')->nullOnDelete();
            
            // Extend currency code fields to support location-based codes (USD_IST, EUR_TBZ, etc.)
            $table->string('from_currency_code', 10)->change(); // Changed from 3 to 10 characters
            $table->string('to_currency_code', 10)->change();   // Changed from 3 to 10 characters
            
            // Add index for location-based queries
            $table->index(['location_id', 'exchange_date']);
            $table->index(['from_currency_code', 'to_currency_code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('currency_exchanges', function (Blueprint $table) {
            $table->dropForeign(['location_id']);
            $table->dropColumn('location_id');
            $table->dropIndex(['location_id', 'exchange_date']);
            $table->dropIndex(['from_currency_code', 'to_currency_code']);
            
            // Revert currency code fields back to 3 characters
            $table->string('from_currency_code', 3)->change();
            $table->string('to_currency_code', 3)->change();
        });
    }
};
