<?php

namespace Database\Seeders\ArenaDoviz;

use App\Enums\ArenaDoviz\ClientCategory;
use App\Enums\ArenaDoviz\ClientRiskLevel;
use App\Enums\ArenaDoviz\KycStatus;
use App\Models\Common\Client;
use App\Models\Common\Contact;
use App\Models\ArenaDoviz\ClientProfile;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class ClientProfileSeeder extends Seeder
{
    /**
     * Seed comprehensive client profiles for Arena Doviz.
     * 
     * Creates realistic client data including:
     * - Individual, Corporate, VIP, and Tourist clients
     * - KYC status and risk assessment
     * - Contact information and preferences
     * - Credit limits and commission rates
     */
    public function run(): void
    {
        $this->command->info('👤 Seeding Arena Doviz client profiles...');

        // Set a temporary authenticated user for the CompanyOwned trait
        $adminUser = \App\Models\User::where('email', '<EMAIL>')->first();
        if ($adminUser) {
            Auth::login($adminUser);
        }

        $clientsData = [
            // VIP Clients
            [
                'client' => [
                    'company_id' => 1,
                    'name' => 'Ahmet Yılmaz Holdings',
                    'currency_code' => 'USD',
                    'account_number' => 'VIP001',
                    'website' => 'https://ahmetyilmazholdings.com',
                    'notes' => 'High-value VIP client with regular large transactions',
                ],
                'contact' => [
                    'first_name' => 'Ahmet',
                    'last_name' => 'Yılmaz',
                    'email' => '<EMAIL>',
                    'phones' => ['+90 ************', '+90 ************'],
                ],
                'profile' => [
                    'category' => ClientCategory::VIP,
                    'risk_level' => ClientRiskLevel::LOW,
                    'kyc_status' => KycStatus::APPROVED,
                    'kyc_completed_at' => Carbon::now()->subMonths(2),
                    'kyc_expires_at' => Carbon::now()->addYear(),
                    'identification_number' => '***********',
                    'identification_type' => 'national_id',
                    'date_of_birth' => Carbon::parse('1975-03-15'),
                    'nationality' => 'Turkish',
                    'occupation' => 'Business Owner',
                    'source_of_funds' => 'Business Revenue',
                    'expected_monthly_volume' => 500000.00,
                    'preferred_currencies' => ['USD', 'EUR', 'AED'],
                    'credit_limit' => 100000.00,
                    'commission_rate' => 0.0015, // 0.15%
                    'vip_status' => true,
                    'referral_source' => 'Existing Client Referral',
                    'notes' => 'VIP client with excellent payment history. Prefers USD transactions.',
                ],
            ],
            
            [
                'client' => [
                    'company_id' => 1,
                    'name' => 'Fatma Özkan Import Export',
                    'currency_code' => 'EUR',
                    'account_number' => 'VIP002',
                    'website' => 'https://ozkantrading.com',
                    'notes' => 'VIP client specializing in European trade',
                ],
                'contact' => [
                    'first_name' => 'Fatma',
                    'last_name' => 'Özkan',
                    'email' => '<EMAIL>',
                    'phones' => ['+90 ************', '+90 ************'],
                ],
                'profile' => [
                    'category' => ClientCategory::VIP,
                    'risk_level' => ClientRiskLevel::LOW,
                    'kyc_status' => KycStatus::APPROVED,
                    'kyc_completed_at' => Carbon::now()->subMonths(3),
                    'kyc_expires_at' => Carbon::now()->addMonths(9),
                    'identification_number' => '***********',
                    'identification_type' => 'national_id',
                    'date_of_birth' => Carbon::parse('1980-07-22'),
                    'nationality' => 'Turkish',
                    'occupation' => 'Import/Export Business',
                    'source_of_funds' => 'International Trade',
                    'expected_monthly_volume' => 750000.00,
                    'preferred_currencies' => ['EUR', 'USD', 'GBP'],
                    'credit_limit' => 150000.00,
                    'commission_rate' => 0.0012, // 0.12%
                    'vip_status' => true,
                    'referral_source' => 'Bank Referral',
                    'notes' => 'Excellent VIP client with focus on European markets.',
                ],
            ],
            
            // Corporate Clients
            [
                'client' => [
                    'company_id' => 1,
                    'name' => 'Teknik Makina Ltd. Şti.',
                    'currency_code' => 'USD',
                    'account_number' => 'CORP001',
                    'website' => 'https://teknikmakina.com.tr',
                    'notes' => 'Manufacturing company with regular machinery imports',
                ],
                'contact' => [
                    'first_name' => 'Mehmet',
                    'last_name' => 'Demir',
                    'email' => '<EMAIL>',
                    'phones' => ['+90 ************', '+90 ************'],
                ],
                'profile' => [
                    'category' => ClientCategory::CORPORATE,
                    'risk_level' => ClientRiskLevel::LOW,
                    'kyc_status' => KycStatus::APPROVED,
                    'kyc_completed_at' => Carbon::now()->subMonth(),
                    'kyc_expires_at' => Carbon::now()->addYear(),
                    'identification_number' => '1***********3456',
                    'identification_type' => 'tax_number',
                    'nationality' => 'Turkish',
                    'occupation' => 'Manufacturing',
                    'source_of_funds' => 'Manufacturing Revenue',
                    'expected_monthly_volume' => 200000.00,
                    'preferred_currencies' => ['USD', 'EUR'],
                    'credit_limit' => 50000.00,
                    'commission_rate' => 0.0020, // 0.20%
                    'vip_status' => false,
                    'referral_source' => 'Online Search',
                    'notes' => 'Reliable corporate client with monthly machinery imports.',
                ],
            ],
            
            [
                'client' => [
                    'company_id' => 1,
                    'name' => 'Gıda Pazarlama A.Ş.',
                    'currency_code' => 'EUR',
                    'account_number' => 'CORP002',
                    'website' => 'https://gidapazarlama.com',
                    'notes' => 'Food import company with European suppliers',
                ],
                'contact' => [
                    'first_name' => 'Ayşe',
                    'last_name' => 'Kaya',
                    'email' => '<EMAIL>',
                    'phones' => ['+90 ************', '+90 ************'],
                ],
                'profile' => [
                    'category' => ClientCategory::CORPORATE,
                    'risk_level' => ClientRiskLevel::MEDIUM,
                    'kyc_status' => KycStatus::APPROVED,
                    'kyc_completed_at' => Carbon::now()->subWeeks(3),
                    'kyc_expires_at' => Carbon::now()->addMonths(11),
                    'identification_number' => '***********34567',
                    'identification_type' => 'tax_number',
                    'nationality' => 'Turkish',
                    'occupation' => 'Food Import/Export',
                    'source_of_funds' => 'Food Trade Revenue',
                    'expected_monthly_volume' => 150000.00,
                    'preferred_currencies' => ['EUR', 'USD'],
                    'credit_limit' => 30000.00,
                    'commission_rate' => 0.0022, // 0.22%
                    'vip_status' => false,
                    'referral_source' => 'Trade Association',
                    'notes' => 'Growing food import business with European focus.',
                ],
            ],
            
            // Individual Clients
            [
                'client' => [
                    'company_id' => 1,
                    'name' => 'Ali Veli',
                    'currency_code' => 'USD',
                    'account_number' => 'IND001',
                    'notes' => 'Individual client with regular remittance needs',
                ],
                'contact' => [
                    'first_name' => 'Ali',
                    'last_name' => 'Veli',
                    'email' => '<EMAIL>',
                    'phones' => ['+90 ************', '+90 ************'],
                ],
                'profile' => [
                    'category' => ClientCategory::INDIVIDUAL,
                    'risk_level' => ClientRiskLevel::LOW,
                    'kyc_status' => KycStatus::APPROVED,
                    'kyc_completed_at' => Carbon::now()->subWeeks(2),
                    'kyc_expires_at' => Carbon::now()->addYear(),
                    'identification_number' => '***********',
                    'identification_type' => 'national_id',
                    'date_of_birth' => Carbon::parse('1985-11-10'),
                    'nationality' => 'Turkish',
                    'occupation' => 'Software Engineer',
                    'source_of_funds' => 'Salary',
                    'expected_monthly_volume' => 5000.00,
                    'preferred_currencies' => ['USD'],
                    'credit_limit' => 10000.00,
                    'commission_rate' => 0.0025, // 0.25%
                    'vip_status' => false,
                    'referral_source' => 'Friend Referral',
                    'notes' => 'Regular individual client with monthly USD needs.',
                ],
            ],
        ];
        
        foreach ($clientsData as $data) {
            // Create client
            $client = Client::create($data['client']);
            
            // Create primary contact
            $contact = $client->primaryContact()->create(array_merge($data['contact'], [
                'is_primary' => true,
            ]));
            
            // Create client profile
            $profile = ClientProfile::create(array_merge($data['profile'], [
                'company_id' => 1,
                'client_id' => $client->id,
                'created_by' => 1, // Admin user
            ]));
        }

        // Logout the temporary user
        Auth::logout();

        $this->command->info('✅ Created ' . count($clientsData) . ' comprehensive client profiles');
    }
}
