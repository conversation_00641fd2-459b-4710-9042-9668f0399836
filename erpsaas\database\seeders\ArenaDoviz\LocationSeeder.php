<?php

namespace Database\Seeders\ArenaDoviz;

use App\Models\ArenaDoviz\Location;
use Illuminate\Database\Seeder;

class LocationSeeder extends Seeder
{
    /**
     * Seed locations for Arena Doviz exchange operations.
     * 
     * Based on documentation requirements:
     * - Istanbul as main location (Turkey timezone as default)
     * - Multi-location support (Tabriz, Tehran, Dubai, China)
     * - Location-based currency rates and operations
     */
    public function run(): void
    {
        $this->command->info('🌍 Seeding Arena Doviz locations...');

        $locations = [
            [
                'name' => 'Istanbul Office',
                'code' => 'IST',
                'city' => 'Istanbul',
                'country' => 'Turkey',
                'address' => 'Levent Mahallesi, Büyükdere Caddesi No:201, 34394 Şişli/İstanbul, Turkey',
                'timezone' => 'Europe/Istanbul',
                'phone' => '+90 ************',
                'email' => '<EMAIL>',
                'manager_name' => 'Mehmet Özkan',
                'working_hours' => [
                    'monday' => ['open' => '09:00', 'close' => '18:00'],
                    'tuesday' => ['open' => '09:00', 'close' => '18:00'],
                    'wednesday' => ['open' => '09:00', 'close' => '18:00'],
                    'thursday' => ['open' => '09:00', 'close' => '18:00'],
                    'friday' => ['open' => '09:00', 'close' => '18:00'],
                    'saturday' => ['open' => '09:00', 'close' => '17:00'],
                    'sunday' => ['closed' => true]
                ],
                'services' => ['currency_exchange', 'money_transfer', 'cash_deposit', 'delivery'],
                'max_transaction_amount' => 100000.00,
                'is_active' => true,
            ],
            
            [
                'name' => 'Tabriz Branch',
                'code' => 'TBZ',
                'city' => 'Tabriz',
                'country' => 'Iran',
                'address' => 'Shahid Madani Street, Tabriz, East Azerbaijan Province, Iran',
                'timezone' => 'Asia/Tehran',
                'phone' => '+98 41 3555 0201',
                'email' => '<EMAIL>',
                'manager_name' => 'Ali Rezaei',
                'working_hours' => [
                    'saturday' => ['open' => '08:00', 'close' => '17:00'],
                    'sunday' => ['open' => '08:00', 'close' => '17:00'],
                    'monday' => ['open' => '08:00', 'close' => '17:00'],
                    'tuesday' => ['open' => '08:00', 'close' => '17:00'],
                    'wednesday' => ['open' => '08:00', 'close' => '17:00'],
                    'thursday' => ['open' => '08:00', 'close' => '14:00'],
                    'friday' => ['closed' => true]
                ],
                'services' => ['currency_exchange', 'money_transfer', 'iranian_banking'],
                'max_transaction_amount' => 50000.00,
                'is_active' => true,
            ],
            
            [
                'name' => 'Tehran Office',
                'code' => 'THR',
                'city' => 'Tehran',
                'country' => 'Iran',
                'address' => 'Valiasr Street, Tehran, Tehran Province, Iran',
                'timezone' => 'Asia/Tehran',
                'phone' => '+98 21 8555 0301',
                'email' => '<EMAIL>',
                'manager_name' => 'Hassan Ahmadi',
                'working_hours' => [
                    'saturday' => ['open' => '08:00', 'close' => '17:00'],
                    'sunday' => ['open' => '08:00', 'close' => '17:00'],
                    'monday' => ['open' => '08:00', 'close' => '17:00'],
                    'tuesday' => ['open' => '08:00', 'close' => '17:00'],
                    'wednesday' => ['open' => '08:00', 'close' => '17:00'],
                    'thursday' => ['open' => '08:00', 'close' => '14:00'],
                    'friday' => ['closed' => true]
                ],
                'services' => ['currency_exchange', 'money_transfer', 'iranian_banking', 'swift_transfer'],
                'max_transaction_amount' => 75000.00,
                'is_active' => true,
            ],

            [
                'name' => 'Dubai Branch',
                'code' => 'DXB',
                'city' => 'Dubai',
                'country' => 'UAE',
                'address' => 'Sheikh Zayed Road, Dubai, United Arab Emirates',
                'timezone' => 'Asia/Dubai',
                'phone' => '+971 4 555 0401',
                'email' => '<EMAIL>',
                'manager_name' => 'Ahmed Al-Mansouri',
                'working_hours' => [
                    'sunday' => ['open' => '09:00', 'close' => '18:00'],
                    'monday' => ['open' => '09:00', 'close' => '18:00'],
                    'tuesday' => ['open' => '09:00', 'close' => '18:00'],
                    'wednesday' => ['open' => '09:00', 'close' => '18:00'],
                    'thursday' => ['open' => '09:00', 'close' => '18:00'],
                    'friday' => ['closed' => true],
                    'saturday' => ['open' => '09:00', 'close' => '17:00']
                ],
                'services' => ['currency_exchange', 'money_transfer', 'swift_transfer', 'delivery'],
                'max_transaction_amount' => 200000.00,
                'is_active' => true,
            ],

        ];
        
        foreach ($locations as $locationData) {
            Location::updateOrCreate(
                ['code' => $locationData['code']],
                array_merge($locationData, [
                    'company_id' => 1,
                    'created_by' => 1,
                ])
            );

            $this->command->info("Created: {$locationData['name']} ({$locationData['code']})");
        }

        $this->command->info('✅ Arena Doviz locations seeded successfully!');
    }
}
