<div class="space-y-6">
    <!-- Client Overview -->
    <x-filament::section>
        <x-slot name="heading">
            <div class="flex items-center gap-2">
                <x-heroicon-o-users class="w-5 h-5" />
                Client Profitability Report
            </div>
        </x-slot>
        
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg text-center">
                <div class="text-blue-600 dark:text-blue-400 text-sm font-medium">Total Clients</div>
                <div class="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {{ number_format($data['summary']['total_clients']) }}
                </div>
            </div>
            
            <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg text-center">
                <div class="text-green-600 dark:text-green-400 text-sm font-medium">Total Commission</div>
                <div class="text-2xl font-bold text-green-900 dark:text-green-100">
                    {{ number_format($data['summary']['total_commission'], 2) }} TRY
                </div>
            </div>
            
            <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg text-center">
                <div class="text-yellow-600 dark:text-yellow-400 text-sm font-medium">Avg Commission per Client</div>
                <div class="text-2xl font-bold text-yellow-900 dark:text-yellow-100">
                    {{ number_format($data['summary']['avg_commission_per_client'], 2) }} TRY
                </div>
            </div>
            
            <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg text-center">
                <div class="text-purple-600 dark:text-purple-400 text-sm font-medium">Client Segments</div>
                <div class="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {{ count($data['client_segments']) }}
                </div>
            </div>
        </div>
    </x-filament::section>

    <!-- Top Clients by Commission -->
    <x-filament::section>
        <x-slot name="heading">Top 20 Clients by Commission</x-slot>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-800">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Rank
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Client Name
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Total Commission (TRY)
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Transaction Count
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Total Volume (TRY)
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Avg Commission
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            First Transaction
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Last Transaction
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($data['top_clients'] as $index => $client)
                        <tr class="{{ $index < 3 ? 'bg-yellow-50 dark:bg-yellow-900/10' : '' }}">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                                @if($index === 0)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        🥇 #{{ $index + 1 }}
                                    </span>
                                @elseif($index === 1)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        🥈 #{{ $index + 1 }}
                                    </span>
                                @elseif($index === 2)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                        🥉 #{{ $index + 1 }}
                                    </span>
                                @else
                                    #{{ $index + 1 }}
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                                {{ $client->client->name ?? 'Unknown Client' }}
                                @if($client->client->email)
                                    <div class="text-xs text-gray-500">{{ $client->client->email }}</div>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600 dark:text-green-400 font-semibold">
                                {{ number_format($client->total_commission, 2) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {{ number_format($client->transaction_count) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {{ number_format($client->total_volume, 2) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 dark:text-blue-400">
                                {{ number_format($client->avg_commission, 2) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {{ \Carbon\Carbon::parse($client->first_transaction)->format('M d, Y') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {{ \Carbon\Carbon::parse($client->last_transaction)->format('M d, Y') }}
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </x-filament::section>

    <!-- Client Segmentation -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <x-filament::section>
            <x-slot name="heading">Client Value Segments</x-slot>
            
            <div class="space-y-4">
                <div class="flex justify-between items-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div>
                        <div class="font-semibold text-green-900 dark:text-green-100">High Value Clients</div>
                        <div class="text-sm text-green-600 dark:text-green-400">Above 2x average commission</div>
                    </div>
                    <div class="text-2xl font-bold text-green-900 dark:text-green-100">
                        {{ number_format($data['client_segments']['high_value']) }}
                    </div>
                </div>
                
                <div class="flex justify-between items-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div>
                        <div class="font-semibold text-blue-900 dark:text-blue-100">Medium Value Clients</div>
                        <div class="text-sm text-blue-600 dark:text-blue-400">Between 1x-2x average commission</div>
                    </div>
                    <div class="text-2xl font-bold text-blue-900 dark:text-blue-100">
                        {{ number_format($data['client_segments']['medium_value']) }}
                    </div>
                </div>
                
                <div class="flex justify-between items-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div>
                        <div class="font-semibold text-gray-900 dark:text-gray-100">Low Value Clients</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Below average commission</div>
                    </div>
                    <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        {{ number_format($data['client_segments']['low_value']) }}
                    </div>
                </div>
            </div>
        </x-filament::section>

        <x-filament::section>
            <x-slot name="heading">Retention Analysis</x-slot>
            
            <div class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <div class="text-blue-600 dark:text-blue-400 text-sm font-medium">Previous Period</div>
                        <div class="text-xl font-bold text-blue-900 dark:text-blue-100">
                            {{ number_format($data['retention_analysis']['previous_period_clients']) }}
                        </div>
                        <div class="text-xs text-blue-600 dark:text-blue-400">clients</div>
                    </div>
                    
                    <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <div class="text-green-600 dark:text-green-400 text-sm font-medium">Current Period</div>
                        <div class="text-xl font-bold text-green-900 dark:text-green-100">
                            {{ number_format($data['retention_analysis']['current_period_clients']) }}
                        </div>
                        <div class="text-xs text-green-600 dark:text-green-400">clients</div>
                    </div>
                </div>
                
                <div class="space-y-3">
                    <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <span class="text-sm font-medium">Retained Clients</span>
                        <span class="text-sm font-semibold text-green-600">
                            {{ number_format($data['retention_analysis']['retained_clients']) }}
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <span class="text-sm font-medium">New Clients</span>
                        <span class="text-sm font-semibold text-blue-600">
                            {{ number_format($data['retention_analysis']['new_clients']) }}
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <span class="text-sm font-medium">Lost Clients</span>
                        <span class="text-sm font-semibold text-red-600">
                            {{ number_format($data['retention_analysis']['lost_clients']) }}
                        </span>
                    </div>
                </div>
                
                <div class="grid grid-cols-2 gap-4 mt-4">
                    <div class="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <div class="text-green-600 dark:text-green-400 text-sm font-medium">Retention Rate</div>
                        <div class="text-lg font-bold text-green-900 dark:text-green-100">
                            {{ number_format($data['retention_analysis']['retention_rate'], 1) }}%
                        </div>
                    </div>
                    
                    <div class="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <div class="text-blue-600 dark:text-blue-400 text-sm font-medium">Growth Rate</div>
                        <div class="text-lg font-bold {{ $data['retention_analysis']['growth_rate'] >= 0 ? 'text-green-900 dark:text-green-100' : 'text-red-900 dark:text-red-100' }}">
                            {{ number_format($data['retention_analysis']['growth_rate'], 1) }}%
                        </div>
                    </div>
                </div>
            </div>
        </x-filament::section>
    </div>

    <!-- Key Insights -->
    <x-filament::section>
        <x-slot name="heading">Key Insights & Recommendations</x-slot>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
                <h4 class="font-semibold text-gray-900 dark:text-gray-100">Performance Highlights</h4>
                
                @php
                    $topClient = $data['top_clients']->first();
                    $retentionRate = $data['retention_analysis']['retention_rate'];
                    $highValueClients = $data['client_segments']['high_value'];
                    $totalClients = $data['summary']['total_clients'];
                @endphp
                
                <div class="space-y-3">
                    @if($topClient)
                        <div class="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                            <div class="text-green-800 dark:text-green-200 font-medium">Top Performer</div>
                            <div class="text-green-600 dark:text-green-400 text-sm mt-1">
                                {{ $topClient->client->name ?? 'Unknown Client' }} generated 
                                {{ number_format($topClient->total_commission, 2) }} TRY in commission
                            </div>
                        </div>
                    @endif
                    
                    <div class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <div class="text-blue-800 dark:text-blue-200 font-medium">Client Retention</div>
                        <div class="text-blue-600 dark:text-blue-400 text-sm mt-1">
                            {{ number_format($retentionRate, 1) }}% retention rate 
                            {{ $retentionRate >= 80 ? '(Excellent)' : ($retentionRate >= 60 ? '(Good)' : '(Needs Improvement)') }}
                        </div>
                    </div>
                    
                    <div class="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                        <div class="text-purple-800 dark:text-purple-200 font-medium">High Value Segment</div>
                        <div class="text-purple-600 dark:text-purple-400 text-sm mt-1">
                            {{ number_format($highValueClients) }} high-value clients 
                            ({{ $totalClients > 0 ? number_format(($highValueClients / $totalClients) * 100, 1) : '0.0' }}% of total)
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="space-y-4">
                <h4 class="font-semibold text-gray-900 dark:text-gray-100">Recommendations</h4>
                
                <div class="space-y-3">
                    @if($retentionRate < 70)
                        <div class="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                            <div class="text-yellow-800 dark:text-yellow-200 font-medium">Improve Retention</div>
                            <div class="text-yellow-600 dark:text-yellow-400 text-sm mt-1">
                                Focus on client engagement and loyalty programs to improve retention rate.
                            </div>
                        </div>
                    @endif
                    
                    @if($data['client_segments']['low_value'] > $data['client_segments']['high_value'] * 3)
                        <div class="p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                            <div class="text-orange-800 dark:text-orange-200 font-medium">Value Optimization</div>
                            <div class="text-orange-600 dark:text-orange-400 text-sm mt-1">
                                Consider strategies to move low-value clients to higher value segments.
                            </div>
                        </div>
                    @endif
                    
                    <div class="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <div class="text-green-800 dark:text-green-200 font-medium">VIP Program</div>
                        <div class="text-green-600 dark:text-green-400 text-sm mt-1">
                            Create exclusive benefits for top {{ min(20, $highValueClients) }} clients to increase loyalty.
                        </div>
                    </div>
                    
                    <div class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <div class="text-blue-800 dark:text-blue-200 font-medium">Cross-selling</div>
                        <div class="text-blue-600 dark:text-blue-400 text-sm mt-1">
                            Identify opportunities to offer additional services to existing clients.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </x-filament::section>
</div>
