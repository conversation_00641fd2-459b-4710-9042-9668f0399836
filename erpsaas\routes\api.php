<?php

use App\Http\Controllers\PlaidWebhookController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Arena Doviz API Routes
Route::middleware(['auth:sanctum'])->prefix('arena-doviz')->group(function () {
    // Currency rates
    Route::prefix('rates')->group(function () {
        Route::get('latest', [\App\Http\Controllers\Api\ArenaDoviz\CurrencyRateController::class, 'latest']);
        Route::get('status', [\App\Http\Controllers\Api\ArenaDoviz\CurrencyRateController::class, 'status']);
        Route::get('alerts', [\App\Http\Controllers\Api\ArenaDoviz\CurrencyRateController::class, 'alerts']);
        Route::post('update', [\App\Http\Controllers\Api\ArenaDoviz\CurrencyRateController::class, 'update']);
        Route::get('{currencyCode}', [\App\Http\Controllers\Api\ArenaDoviz\CurrencyRateController::class, 'show']);
        Route::get('{currencyCode}/history', [\App\Http\Controllers\Api\ArenaDoviz\CurrencyRateController::class, 'history']);
        Route::get('{currencyCode}/cached', [\App\Http\Controllers\Api\ArenaDoviz\CurrencyRateController::class, 'cached']);
    });
});

Route::post('/plaid/webhook', [PlaidWebhookController::class, 'handleWebhook']);
