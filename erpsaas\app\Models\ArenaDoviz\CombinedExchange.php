<?php

namespace App\Models\ArenaDoviz;

use App\Concerns\Blamable;
use App\Concerns\CompanyOwned;
use App\Enums\ArenaDoviz\ExchangeStatus;
use App\Models\Common\Client;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class CombinedExchange extends Model
{
    use Blamable;
    use CompanyOwned;
    use HasFactory;
    use SoftDeletes;

    protected $table = 'combined_exchanges';

    protected $fillable = [
        'company_id',
        'location_id',
        'buy_client_id',
        'sell_client_id',
        'combined_exchange_number',
        'status',
        'currency_code',
        'buy_amount',
        'sell_amount',
        'buy_rate',
        'sell_rate',
        'profit_amount',
        'profit_margin',
        'total_commission',
        'net_profit',
        'exchange_date',
        'settlement_date',
        'notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'status' => ExchangeStatus::class,
        'buy_amount' => 'decimal:2',
        'sell_amount' => 'decimal:2',
        'buy_rate' => 'decimal:6',
        'sell_rate' => 'decimal:6',
        'profit_amount' => 'decimal:2',
        'profit_margin' => 'decimal:4',
        'total_commission' => 'decimal:2',
        'net_profit' => 'decimal:2',
        'exchange_date' => 'datetime',
        'settlement_date' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($exchange) {
            if (empty($exchange->combined_exchange_number)) {
                $exchange->combined_exchange_number = static::generateExchangeNumber();
            }
        });

        static::saving(function ($exchange) {
            // Auto-calculate profit and margins
            $exchange->calculateProfitMetrics();
        });
    }

    /**
     * Get the location for this combined exchange
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    /**
     * Get the buy client
     */
    public function buyClient(): BelongsTo
    {
        return $this->belongsTo(Client::class, 'buy_client_id');
    }

    /**
     * Get the sell client
     */
    public function sellClient(): BelongsTo
    {
        return $this->belongsTo(Client::class, 'sell_client_id');
    }

    /**
     * Get related currency exchanges
     */
    public function currencyExchanges(): HasMany
    {
        return $this->hasMany(CurrencyExchange::class, 'combined_exchange_id');
    }

    /**
     * Generate unique combined exchange number
     */
    public static function generateExchangeNumber(): string
    {
        $prefix = 'CE';
        $year = date('Y');
        $month = date('m');
        
        $lastExchange = static::whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastExchange ? 
            (int) substr($lastExchange->combined_exchange_number, -6) + 1 : 1;

        return sprintf('%s%s%s%06d', $prefix, $year, $month, $sequence);
    }

    /**
     * Calculate profit metrics
     */
    public function calculateProfitMetrics(): void
    {
        if ($this->buy_amount && $this->sell_amount && $this->buy_rate && $this->sell_rate) {
            // Calculate profit amount (difference in rates * amount)
            $this->profit_amount = ($this->sell_rate - $this->buy_rate) * $this->buy_amount;
            
            // Calculate profit margin percentage
            $this->profit_margin = $this->buy_rate > 0 ? 
                (($this->sell_rate - $this->buy_rate) / $this->buy_rate) * 100 : 0;
            
            // Calculate net profit (profit - commission)
            $this->net_profit = $this->profit_amount - ($this->total_commission ?? 0);
        }
    }

    /**
     * Get formatted buy amount
     */
    public function getFormattedBuyAmountAttribute(): string
    {
        return \App\Helpers\ArenaDoviz\NumberFormatter::formatByCurrency($this->buy_amount, $this->currency_code);
    }

    /**
     * Get formatted sell amount
     */
    public function getFormattedSellAmountAttribute(): string
    {
        return \App\Helpers\ArenaDoviz\NumberFormatter::formatByCurrency($this->sell_amount, $this->currency_code);
    }

    /**
     * Get formatted profit amount
     */
    public function getFormattedProfitAmountAttribute(): string
    {
        return \App\Helpers\ArenaDoviz\NumberFormatter::formatByCurrency($this->profit_amount, $this->currency_code);
    }

    /**
     * Get formatted net profit
     */
    public function getFormattedNetProfitAttribute(): string
    {
        return \App\Helpers\ArenaDoviz\NumberFormatter::formatByCurrency($this->net_profit, $this->currency_code);
    }

    /**
     * Get formatted profit margin
     */
    public function getFormattedProfitMarginAttribute(): string
    {
        return \App\Helpers\ArenaDoviz\NumberFormatter::formatPercentage($this->profit_margin);
    }

    /**
     * Check if the exchange is profitable
     */
    public function isProfitable(): bool
    {
        return $this->net_profit > 0;
    }

    /**
     * Get profit status color
     */
    public function getProfitStatusColorAttribute(): string
    {
        if ($this->net_profit > 0) {
            return 'success';
        } elseif ($this->net_profit < 0) {
            return 'danger';
        } else {
            return 'warning';
        }
    }

    /**
     * Scope for profitable exchanges
     */
    public function scopeProfitable($query)
    {
        return $query->where('net_profit', '>', 0);
    }

    /**
     * Scope for today's exchanges
     */
    public function scopeToday($query)
    {
        return $query->whereDate('exchange_date', today());
    }

    /**
     * Scope for this month's exchanges
     */
    public function scopeThisMonth($query)
    {
        return $query->whereMonth('exchange_date', now()->month)
            ->whereYear('exchange_date', now()->year);
    }
}
