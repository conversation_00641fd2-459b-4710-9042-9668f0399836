# Arena Doviz Comprehensive Seed Data

This directory contains comprehensive seed data for the Arena Doviz currency exchange system. The seeders create realistic test data that covers all documented features and business scenarios.

## Overview

The Arena Doviz seed data system creates a complete, realistic dataset for a multi-location currency exchange business with the following features:

- **Multi-currency operations** (TRY, USD, EUR, GBP, AED, IRR, CHF, JPY, CAD, AUD)
- **Multi-location support** (Istanbul, Tabriz, Tehran, Dubai, Shanghai)
- **Comprehensive client management** with KYC and risk assessment
- **Full transaction lifecycle** including exchanges, SWIFT transfers, and cash deposits
- **Delivery and courier management** system
- **Historical data** for analytics and reporting
- **Complete audit trail** for compliance and security

## Seeder Structure

### Main Seeder
- **`ArenaDovizSeeder.php`** - Main orchestrator that calls all other seeders in the correct order

### Core Business Data
- **`CurrencySeeder.php`** - Creates 10 major currencies with realistic exchange rates
- **`LocationSeeder.php`** - Creates 5 business locations with working hours and contact details
- **`UserRoleSeeder.php`** - Creates 12 users with different roles and permissions

### Client Management
- **`ClientProfileSeeder.php`** - Creates diverse client profiles (VIP, Corporate, Individual, Tourist)
- **`ClientBalanceSeeder.php`** - Creates multi-currency balances for all clients

### Transaction Data
- **`CurrencyExchangeSeeder.php`** - Creates 6 months of exchange transactions (buy/sell/transfer)
- **`SwiftTransactionSeeder.php`** - Creates international SWIFT transfer records
- **`CashDepositSeeder.php`** - Creates cash deposit transactions with denomination tracking

### Operations Data
- **`CourierSeeder.php`** - Creates courier profiles with vehicles and service areas
- **`DeliverySeeder.php`** - Creates delivery records with tracking and proof of delivery

### Analytics Data
- **`ExchangeRateHistorySeeder.php`** - Creates 12 months of historical exchange rates
- **`AuditLogSeeder.php`** - Creates comprehensive audit trail for compliance

## Data Volume

The seeders create the following approximate data volumes:

| Entity | Count | Description |
|--------|-------|-------------|
| Currencies | 10 | Major international currencies |
| Locations | 5 | Business locations across different countries |
| Users | 12 | Staff with various roles and permissions |
| Clients | 8+ | Mix of VIP, Corporate, Individual, and test clients |
| Client Balances | 25+ | Multi-currency balances |
| Currency Exchanges | 1000+ | 6 months of transaction history |
| SWIFT Transactions | 25+ | International transfer records |
| Cash Deposits | 200+ | 3 months of cash deposit history |
| Couriers | 8 | Delivery personnel with different vehicles |
| Deliveries | 40+ | Delivery tracking records |
| Exchange Rates | 15,000+ | 12 months of daily rate history |
| Audit Logs | 500+ | 3 months of system activity logs |

## Client Categories

### VIP Clients (2 clients)
- **High transaction volumes** (500K-2M monthly)
- **Low commission rates** (0.12-0.15%)
- **High credit limits** (100K-150K)
- **Approved KYC status**
- **Premium service features**

### Corporate Clients (2 clients)
- **Business-level volumes** (150K-200K monthly)
- **Standard commission rates** (0.20-0.22%)
- **Moderate credit limits** (30K-50K)
- **Complete business documentation**
- **Regular trading patterns**

### Individual Clients (1+ clients)
- **Personal-level volumes** (2K-5K monthly)
- **Higher commission rates** (0.25%+)
- **Lower credit limits** (10K)
- **Personal KYC documentation**
- **Occasional transactions**

### Test Clients (3 clients)
- **Special scenarios** (zero balance, high volume, multi-currency)
- **Edge case testing**
- **System validation**

## Transaction Types

### Currency Exchange Transactions
- **Buy Transactions** (40%) - Customers purchasing foreign currency
- **Sell Transactions** (35%) - Customers selling foreign currency  
- **Transfer Transactions** (25%) - International transfers

### SWIFT Transactions
- **Incoming transfers** from international banks
- **Outgoing transfers** to international banks
- **Complete bank routing information**
- **Realistic processing times and fees**

### Cash Deposits
- **Multi-currency cash handling**
- **Denomination breakdown tracking**
- **Batch processing for Iranian Rial**
- **Verification and vault management**

## Geographic Coverage

### Istanbul Office (Main)
- **Turkish Lira operations**
- **European timezone**
- **Primary business hub**
- **Full service capabilities**

### Tabriz Branch
- **Iranian Rial specialization**
- **Tehran timezone**
- **Regional operations**
- **Cross-border expertise**

### Tehran Office
- **Iranian market focus**
- **Tehran timezone**
- **Government relations**
- **Compliance expertise**

### Dubai Branch
- **UAE Dirham operations**
- **Dubai timezone**
- **Middle East hub**
- **Trade finance focus**

### Shanghai Office
- **Asian market operations**
- **Shanghai timezone**
- **Chinese Yuan handling**
- **Regional expansion**

## Usage Instructions

### Running All Seeders
```bash
php artisan db:seed --class=ArenaDovizSeeder
```

### Running Individual Seeders
```bash
php artisan db:seed --class=Database\\Seeders\\ArenaDoviz\\CurrencySeeder
php artisan db:seed --class=Database\\Seeders\\ArenaDoviz\\ClientProfileSeeder
```

### Using Factories
```php
// Create VIP client with profile
$client = Client::factory()
    ->has(ClientProfile::factory()->vip())
    ->create();

// Create high-value transaction
$exchange = CurrencyExchange::factory()
    ->highValue()
    ->completed()
    ->create();
```

## Data Relationships

The seed data maintains proper foreign key relationships:

- **Clients** → **Client Profiles** (1:1)
- **Clients** → **Client Balances** (1:many)
- **Clients** → **Currency Exchanges** (1:many)
- **Currency Exchanges** → **SWIFT Transactions** (1:1)
- **Currency Exchanges** → **Cash Deposits** (1:1)
- **Currency Exchanges** → **Deliveries** (1:1)
- **Couriers** → **Deliveries** (1:many)
- **Users** → **All Operations** (created_by)

## Business Rules Compliance

The seed data follows all documented business rules:

- **KYC requirements** for different client categories
- **Risk assessment** based on transaction patterns
- **Commission rates** based on client tier and volume
- **Credit limits** aligned with risk levels
- **Delivery restrictions** based on amount and location
- **Working hours** respected for location-based operations
- **Currency-specific** denomination handling
- **Audit trail** for all sensitive operations

## Testing Scenarios

The seed data includes specific scenarios for testing:

- **Zero balance clients** for insufficient funds testing
- **High-value transactions** for approval workflow testing
- **Failed deliveries** for exception handling testing
- **Rejected cash deposits** for quality control testing
- **Expired KYC** for compliance testing
- **Multi-currency operations** for complex calculations
- **Cross-location transfers** for routing testing
- **Security events** for monitoring system testing

## Maintenance

To refresh the seed data:

1. **Backup existing data** if needed
2. **Run migrations** to ensure schema is current
3. **Clear existing data** if required
4. **Run seeders** in the correct order
5. **Verify data integrity** using built-in checks

The seeders are designed to be idempotent where possible, using `updateOrCreate` methods to avoid duplicates.
