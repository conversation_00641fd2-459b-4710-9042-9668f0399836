<?php

namespace Database\Seeders\ArenaDoviz;

use App\Enums\ArenaDoviz\ExchangeType;
use App\Enums\ArenaDoviz\ExchangeStatus;
use App\Models\Common\Client;
use App\Models\ArenaDoviz\CurrencyExchange;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class CurrencyExchangeSeeder extends Seeder
{
    /**
     * Seed comprehensive currency exchange transactions for Arena Doviz.
     * 
     * Creates realistic transaction data covering:
     * - Buy transactions (Customer → Exchange Office)
     * - Sell transactions (Exchange Office → Customer)
     * - Transfer transactions between customers
     * - Various transaction statuses and amounts
     * - Historical data spanning several months
     */
    public function run(): void
    {
        $this->command->info('💱 Seeding Arena Doviz currency exchange transactions...');

        // Set a temporary authenticated user for the CompanyOwned trait
        $adminUser = \App\Models\User::where('email', '<EMAIL>')->first();
        if ($adminUser) {
            Auth::login($adminUser);
        }

        $clients = Client::where('company_id', 1)->get();

        if ($clients->isEmpty()) {
            $this->command->warn('No clients found. Please run ClientProfileSeeder first.');
            Auth::logout();
            return;
        }

        // Create transactions for the last 6 months
        $startDate = Carbon::now()->subMonths(6);
        $endDate = Carbon::now();

        $transactionCount = 0;

        // Temporarily disable WhatsApp observer during seeding
        CurrencyExchange::unsetEventDispatcher();

        // Generate daily transactions
        for ($date = $startDate->copy(); $date->lte($endDate); $date->addDay()) {
            // Skip weekends for most transactions (some weekend activity)
            if ($date->isWeekend() && rand(1, 100) > 20) {
                continue;
            }

            // Generate 3-15 transactions per day
            $dailyTransactions = rand(3, 15);

            for ($i = 0; $i < $dailyTransactions; $i++) {
                $this->createRandomTransaction($clients, $date->copy()->addHours(rand(9, 18)));
                $transactionCount++;
            }
        }

        // Create some specific scenario transactions
        $this->createSpecificScenarios($clients);

        // Re-enable event dispatcher
        CurrencyExchange::setEventDispatcher(app('events'));

        // Logout the temporary user
        Auth::logout();

        $this->command->info("✅ Created {$transactionCount} currency exchange transactions");
    }
    
    private function createRandomTransaction($clients, Carbon $date): void
    {
        $client = $clients->random();
        $exchangeType = $this->getRandomExchangeType();
        $status = $this->getRandomStatus($date);

        $transactionData = $this->generateTransactionData($exchangeType, $status, $date);

        // Get valid user IDs
        $validUserIds = \App\Models\User::where('current_company_id', 1)->pluck('id')->toArray();
        $createdBy = !empty($validUserIds) ? $validUserIds[array_rand($validUserIds)] : 1;

        CurrencyExchange::create([
            'company_id' => 1,
            'client_id' => $client->id,
            'exchange_number' => $this->generateExchangeNumber($date),
            'exchange_type' => $exchangeType,
            'status' => $status,
            'from_currency_code' => $transactionData['from_currency'],
            'to_currency_code' => $transactionData['to_currency'],
            'from_amount' => $transactionData['from_amount'],
            'to_amount' => $transactionData['to_amount'],
            'exchange_rate' => $transactionData['exchange_rate'],
            'commission_rate' => $transactionData['commission_rate'],
            'commission_amount' => $transactionData['commission_amount'],
            'net_amount' => $transactionData['net_amount'],
            'notes' => $transactionData['notes'],
            'exchange_date' => $date,
            'settlement_date' => $status === ExchangeStatus::COMPLETED ? $date->copy()->addHours(rand(1, 24)) : null,
            'delivery_method' => $transactionData['delivery_method'],
            'delivery_address' => $transactionData['delivery_address'],
            'reference_number' => $this->generateReferenceNumber(),
            'created_by' => $createdBy,
            'created_at' => $date,
            'updated_at' => $date,
        ]);
    }
    
    private function getRandomExchangeType(): ExchangeType
    {
        $types = [
            ['type' => ExchangeType::BUY, 'weight' => 40],      // 40% buy transactions
            ['type' => ExchangeType::SELL, 'weight' => 35],     // 35% sell transactions
            ['type' => ExchangeType::TRANSFER, 'weight' => 25], // 25% transfer transactions
        ];

        $rand = rand(1, 100);
        $cumulative = 0;

        foreach ($types as $typeData) {
            $cumulative += $typeData['weight'];
            if ($rand <= $cumulative) {
                return $typeData['type'];
            }
        }

        return ExchangeType::BUY;
    }
    
    private function getRandomStatus(Carbon $date): ExchangeStatus
    {
        // Older transactions are more likely to be completed
        $daysAgo = $date->diffInDays(Carbon::now());

        if ($daysAgo > 7) {
            // Old transactions: mostly completed
            $statuses = [
                ['status' => ExchangeStatus::COMPLETED, 'weight' => 85],
                ['status' => ExchangeStatus::CANCELLED, 'weight' => 10],
                ['status' => ExchangeStatus::FAILED, 'weight' => 5],
            ];
        } elseif ($daysAgo > 1) {
            // Recent transactions: mix of statuses
            $statuses = [
                ['status' => ExchangeStatus::COMPLETED, 'weight' => 70],
                ['status' => ExchangeStatus::PROCESSING, 'weight' => 15],
                ['status' => ExchangeStatus::PENDING, 'weight' => 10],
                ['status' => ExchangeStatus::CANCELLED, 'weight' => 3],
                ['status' => ExchangeStatus::FAILED, 'weight' => 2],
            ];
        } else {
            // Very recent transactions: more pending/processing
            $statuses = [
                ['status' => ExchangeStatus::COMPLETED, 'weight' => 40],
                ['status' => ExchangeStatus::PROCESSING, 'weight' => 30],
                ['status' => ExchangeStatus::PENDING, 'weight' => 25],
                ['status' => ExchangeStatus::CANCELLED, 'weight' => 3],
                ['status' => ExchangeStatus::FAILED, 'weight' => 2],
            ];
        }

        $rand = rand(1, 100);
        $cumulative = 0;

        foreach ($statuses as $statusData) {
            $cumulative += $statusData['weight'];
            if ($rand <= $cumulative) {
                return $statusData['status'];
            }
        }

        return ExchangeStatus::COMPLETED;
    }
    
    private function generateTransactionData(ExchangeType $type, ExchangeStatus $status, Carbon $date): array
    {
        $currencyPairs = [
            ['TRY', 'USD', 32.50],
            ['USD', 'TRY', 0.0308],
            ['TRY', 'EUR', 35.20],
            ['EUR', 'TRY', 0.0284],
            ['TRY', 'AED', 8.85],
            ['AED', 'TRY', 0.1130],
            ['USD', 'EUR', 1.08],
            ['EUR', 'USD', 0.93],
        ];
        
        $pair = $currencyPairs[array_rand($currencyPairs)];
        $fromCurrency = $pair[0];
        $toCurrency = $pair[1];
        $baseRate = $pair[2];
        
        // Add some rate variation (±2%)
        $rateVariation = 1 + (rand(-200, 200) / 10000);
        $exchangeRate = $baseRate * $rateVariation;
        
        // Generate amount based on currency
        $fromAmount = $this->generateAmount($fromCurrency, $type);
        $toAmount = $fromAmount * $exchangeRate;
        
        // Calculate commission (0.15% - 0.30%)
        $commissionRate = rand(15, 30) / 10000; // 0.0015 to 0.0030
        $commissionAmount = $toAmount * $commissionRate;
        $netAmount = $toAmount - $commissionAmount;
        
        return [
            'from_currency' => $fromCurrency,
            'to_currency' => $toCurrency,
            'from_amount' => round($fromAmount, 4),
            'to_amount' => round($toAmount, 4),
            'exchange_rate' => round($exchangeRate, 6),
            'commission_rate' => $commissionRate,
            'commission_amount' => round($commissionAmount, 4),
            'net_amount' => round($netAmount, 4),
            'notes' => $this->generateTransactionNotes($type, $fromCurrency, $toCurrency),
            'delivery_method' => $this->getDeliveryMethod(),
            'delivery_address' => $this->getDeliveryAddress(),
        ];
    }
    
    private function generateAmount(string $currency, ExchangeType $type): float
    {
        $baseAmounts = [
            'TRY' => [1000, 50000],
            'USD' => [100, 5000],
            'EUR' => [100, 4500],
            'AED' => [500, 15000],
            'IRR' => [10000000, 500000000],
        ];
        
        $range = $baseAmounts[$currency] ?? [100, 1000];
        return rand($range[0], $range[1]);
    }
    
    private function generateTransactionNotes(ExchangeType $type, string $from, string $to): string
    {
        $notes = [
            "Regular {$type->value} transaction: {$from} to {$to}",
            "Client requested {$type->value} operation",
            "Standard exchange transaction",
            "Urgent {$type->value} request",
            "Monthly recurring transaction",
        ];
        
        return $notes[array_rand($notes)];
    }
    
    private function getDeliveryMethod(): ?string
    {
        $methods = ['pickup', 'courier', 'bank_transfer', null];
        return $methods[array_rand($methods)];
    }
    
    private function getDeliveryAddress(): ?string
    {
        if (rand(1, 100) > 60) {
            return null; // 40% have delivery address
        }
        
        $addresses = [
            'Levent Mahallesi, Büyükdere Caddesi, Şişli/İstanbul',
            'Bağdat Caddesi, Kadıköy/İstanbul',
            'Nişantaşı, Teşvikiye Mahallesi, Şişli/İstanbul',
            'Etiler Mahallesi, Beşiktaş/İstanbul',
        ];
        
        return $addresses[array_rand($addresses)];
    }
    
    private function generateReferenceNumber(): string
    {
        return 'REF' . date('Ymd') . rand(1000, 9999);
    }

    private function generateExchangeNumber(Carbon $date): string
    {
        $prefix = 'EX';
        $year = $date->format('Y');
        $month = $date->format('m');

        // Use a more robust counter for seeding
        static $counters = [];
        $key = $year . $month;

        if (!isset($counters[$key])) {
            $counters[$key] = 1;
        } else {
            $counters[$key]++;
        }

        return sprintf('%s%s%s%04d', $prefix, $year, $month, $counters[$key]);
    }
    
    private function createSpecificScenarios($clients): void
    {
        // Get valid user IDs
        $validUserIds = \App\Models\User::where('current_company_id', 1)->pluck('id')->toArray();
        $createdBy = !empty($validUserIds) ? $validUserIds[array_rand($validUserIds)] : 1;

        // Create some high-value VIP transactions
        $vipClient = $clients->where('account_number', 'VIP001')->first();
        if ($vipClient) {
            CurrencyExchange::create([
                'company_id' => 1,
                'client_id' => $vipClient->id,
                'exchange_number' => $this->generateExchangeNumber(Carbon::now()->subDays(5)),
                'exchange_type' => ExchangeType::BUY,
                'status' => ExchangeStatus::COMPLETED,
                'from_currency_code' => 'TRY',
                'to_currency_code' => 'USD',
                'from_amount' => 1000000.00,
                'to_amount' => 30769.23,
                'exchange_rate' => 32.5000,
                'commission_rate' => 0.0015,
                'commission_amount' => 46.15,
                'net_amount' => 30723.08,
                'notes' => 'Large VIP transaction - USD purchase',
                'exchange_date' => Carbon::now()->subDays(5),
                'settlement_date' => Carbon::now()->subDays(5)->addHours(2),
                'delivery_method' => 'pickup',
                'reference_number' => 'VIP' . date('Ymd') . '001',
                'created_by' => $createdBy,
            ]);
        }
    }
}
