<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('location_currencies', function (Blueprint $table) {
            $table->timestamp('last_updated')->nullable()->after('rate_date');
            $table->index('last_updated');
        });

        // Set initial last_updated values to current updated_at
        DB::table('location_currencies')->update([
            'last_updated' => DB::raw('updated_at')
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('location_currencies', function (Blueprint $table) {
            $table->dropIndex(['last_updated']);
            $table->dropColumn('last_updated');
        });
    }
};
