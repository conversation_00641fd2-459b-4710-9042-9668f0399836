<?php

namespace Database\Factories\ArenaDoviz;

use App\Enums\ArenaDoviz\ClientCategory;
use App\Enums\ArenaDoviz\ClientRiskLevel;
use App\Enums\ArenaDoviz\KycStatus;
use App\Models\ArenaDoviz\ClientProfile;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ArenaDoviz\ClientProfile>
 */
class ClientProfileFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ClientProfile::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $category = $this->faker->randomElement(ClientCategory::cases());
        $riskLevel = $this->faker->randomElement(ClientRiskLevel::cases());
        $kycStatus = $this->faker->randomElement(KycStatus::cases());
        
        $dateOfBirth = $this->faker->dateTimeBetween('-70 years', '-18 years');
        $kycCompletedAt = $kycStatus === KycStatus::APPROVED ? 
            $this->faker->dateTimeBetween('-1 year', 'now') : null;
        
        return [
            'company_id' => 1,
            'client_id' => null, // Will be set when creating with client
            'category' => $category,
            'risk_level' => $riskLevel,
            'kyc_status' => $kycStatus,
            'kyc_completed_at' => $kycCompletedAt,
            'kyc_expires_at' => $kycCompletedAt ? 
                Carbon::instance($kycCompletedAt)->addYear() : null,
            'identification_number' => $this->generateIdentificationNumber(),
            'identification_type' => $this->faker->randomElement(['national_id', 'passport', 'tax_number']),
            'date_of_birth' => $dateOfBirth,
            'nationality' => $this->faker->randomElement(['Turkish', 'Iranian', 'Syrian', 'Iraqi', 'Afghan']),
            'occupation' => $this->generateOccupation($category),
            'source_of_funds' => $this->generateSourceOfFunds($category),
            'expected_monthly_volume' => $this->generateMonthlyVolume($category),
            'preferred_currencies' => $this->generatePreferredCurrencies(),
            'credit_limit' => $this->generateCreditLimit($category),
            'commission_rate' => $this->generateCommissionRate($category),
            'vip_status' => $category === ClientCategory::VIP,
            'referral_source' => $this->faker->randomElement([
                'Online Search', 'Friend Referral', 'Bank Referral', 'Existing Client Referral',
                'Social Media', 'Advertisement', 'Walk-in', 'Trade Association'
            ]),
            'notes' => $this->faker->optional(0.7)->sentence(),
            'created_by' => 1,
        ];
    }

    /**
     * Generate VIP client profile
     */
    public function vip(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => ClientCategory::VIP,
            'risk_level' => ClientRiskLevel::LOW,
            'kyc_status' => KycStatus::APPROVED,
            'kyc_completed_at' => $this->faker->dateTimeBetween('-6 months', '-1 month'),
            'expected_monthly_volume' => $this->faker->numberBetween(500000, 2000000),
            'credit_limit' => $this->faker->numberBetween(100000, 500000),
            'commission_rate' => $this->faker->randomFloat(4, 0.0010, 0.0020),
            'vip_status' => true,
            'occupation' => $this->faker->randomElement([
                'Business Owner', 'Import/Export Business', 'Real Estate Developer',
                'Investment Manager', 'International Trader'
            ]),
            'source_of_funds' => $this->faker->randomElement([
                'Business Revenue', 'International Trade', 'Investment Income',
                'Real Estate Income', 'Professional Services'
            ]),
        ]);
    }

    /**
     * Generate corporate client profile
     */
    public function corporate(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => ClientCategory::CORPORATE,
            'risk_level' => $this->faker->randomElement([ClientRiskLevel::LOW, ClientRiskLevel::MEDIUM]),
            'kyc_status' => KycStatus::APPROVED,
            'kyc_completed_at' => $this->faker->dateTimeBetween('-3 months', '-1 week'),
            'identification_type' => 'tax_number',
            'identification_number' => $this->generateTaxNumber(),
            'date_of_birth' => null, // Corporate entities don't have birth dates
            'expected_monthly_volume' => $this->faker->numberBetween(100000, 800000),
            'credit_limit' => $this->faker->numberBetween(30000, 200000),
            'commission_rate' => $this->faker->randomFloat(4, 0.0018, 0.0025),
            'vip_status' => false,
            'occupation' => $this->faker->randomElement([
                'Manufacturing', 'Import/Export', 'Trading Company', 'Construction',
                'Technology Services', 'Food Industry', 'Textile Manufacturing'
            ]),
            'source_of_funds' => $this->faker->randomElement([
                'Manufacturing Revenue', 'Trading Revenue', 'Service Income',
                'Construction Projects', 'Technology Services'
            ]),
        ]);
    }

    /**
     * Generate individual client profile
     */
    public function individual(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => ClientCategory::INDIVIDUAL,
            'risk_level' => $this->faker->randomElement([ClientRiskLevel::LOW, ClientRiskLevel::MEDIUM]),
            'kyc_status' => $this->faker->randomElement([KycStatus::APPROVED, KycStatus::PENDING]),
            'expected_monthly_volume' => $this->faker->numberBetween(2000, 50000),
            'credit_limit' => $this->faker->numberBetween(5000, 50000),
            'commission_rate' => $this->faker->randomFloat(4, 0.0020, 0.0030),
            'vip_status' => false,
            'occupation' => $this->faker->randomElement([
                'Software Engineer', 'Teacher', 'Doctor', 'Lawyer', 'Accountant',
                'Sales Manager', 'Marketing Specialist', 'Consultant', 'Freelancer'
            ]),
            'source_of_funds' => $this->faker->randomElement([
                'Salary', 'Professional Income', 'Freelance Income', 'Pension',
                'Investment Returns', 'Family Support'
            ]),
        ]);
    }

    /**
     * Generate tourist client profile
     */
    public function tourist(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => ClientCategory::TOURIST,
            'risk_level' => ClientRiskLevel::MEDIUM,
            'kyc_status' => KycStatus::BASIC,
            'expected_monthly_volume' => $this->faker->numberBetween(1000, 10000),
            'credit_limit' => $this->faker->numberBetween(1000, 10000),
            'commission_rate' => $this->faker->randomFloat(4, 0.0025, 0.0035),
            'vip_status' => false,
            'nationality' => $this->faker->randomElement([
                'German', 'French', 'British', 'American', 'Canadian',
                'Australian', 'Dutch', 'Swedish', 'Norwegian'
            ]),
            'occupation' => $this->faker->randomElement([
                'Tourist', 'Business Traveler', 'Student', 'Retiree', 'Visitor'
            ]),
            'source_of_funds' => $this->faker->randomElement([
                'Tourism Budget', 'Travel Allowance', 'Personal Savings',
                'Business Travel Budget', 'Student Allowance'
            ]),
        ]);
    }

    /**
     * Generate high-risk client profile
     */
    public function highRisk(): static
    {
        return $this->state(fn (array $attributes) => [
            'risk_level' => ClientRiskLevel::HIGH,
            'kyc_status' => KycStatus::UNDER_REVIEW,
            'commission_rate' => $this->faker->randomFloat(4, 0.0030, 0.0050),
            'notes' => 'High-risk client - requires additional verification and monitoring',
        ]);
    }

    private function generateIdentificationNumber(): string
    {
        return $this->faker->numerify('###########'); // 11 digits for Turkish ID
    }

    private function generateTaxNumber(): string
    {
        return $this->faker->numerify('################'); // 16 digits for tax number
    }

    private function generateOccupation(ClientCategory $category): string
    {
        return match ($category) {
            ClientCategory::VIP => $this->faker->randomElement([
                'Business Owner', 'Investment Manager', 'Real Estate Developer',
                'International Trader', 'Import/Export Business'
            ]),
            ClientCategory::CORPORATE => $this->faker->randomElement([
                'Manufacturing', 'Import/Export', 'Trading Company', 'Construction',
                'Technology Services', 'Food Industry'
            ]),
            ClientCategory::INDIVIDUAL => $this->faker->randomElement([
                'Software Engineer', 'Teacher', 'Doctor', 'Lawyer', 'Accountant',
                'Sales Manager', 'Marketing Specialist'
            ]),
            ClientCategory::TOURIST => $this->faker->randomElement([
                'Tourist', 'Business Traveler', 'Student', 'Retiree'
            ]),
        };
    }

    private function generateSourceOfFunds(ClientCategory $category): string
    {
        return match ($category) {
            ClientCategory::VIP => $this->faker->randomElement([
                'Business Revenue', 'International Trade', 'Investment Income',
                'Real Estate Income', 'Professional Services'
            ]),
            ClientCategory::CORPORATE => $this->faker->randomElement([
                'Manufacturing Revenue', 'Trading Revenue', 'Service Income',
                'Construction Projects', 'Technology Services'
            ]),
            ClientCategory::INDIVIDUAL => $this->faker->randomElement([
                'Salary', 'Professional Income', 'Freelance Income',
                'Investment Returns', 'Family Support'
            ]),
            ClientCategory::TOURIST => $this->faker->randomElement([
                'Tourism Budget', 'Travel Allowance', 'Personal Savings',
                'Business Travel Budget'
            ]),
        };
    }

    private function generateMonthlyVolume(ClientCategory $category): float
    {
        return match ($category) {
            ClientCategory::VIP => $this->faker->numberBetween(500000, 2000000),
            ClientCategory::CORPORATE => $this->faker->numberBetween(100000, 800000),
            ClientCategory::INDIVIDUAL => $this->faker->numberBetween(2000, 50000),
            ClientCategory::TOURIST => $this->faker->numberBetween(1000, 10000),
        };
    }

    private function generatePreferredCurrencies(): array
    {
        $allCurrencies = ['USD', 'EUR', 'GBP', 'AED', 'IRR', 'CHF', 'JPY'];
        $count = $this->faker->numberBetween(1, 4);
        return $this->faker->randomElements($allCurrencies, $count);
    }

    private function generateCreditLimit(ClientCategory $category): float
    {
        return match ($category) {
            ClientCategory::VIP => $this->faker->numberBetween(100000, 500000),
            ClientCategory::CORPORATE => $this->faker->numberBetween(30000, 200000),
            ClientCategory::INDIVIDUAL => $this->faker->numberBetween(5000, 50000),
            ClientCategory::TOURIST => $this->faker->numberBetween(1000, 10000),
        };
    }

    private function generateCommissionRate(ClientCategory $category): float
    {
        return match ($category) {
            ClientCategory::VIP => $this->faker->randomFloat(4, 0.0010, 0.0020),
            ClientCategory::CORPORATE => $this->faker->randomFloat(4, 0.0018, 0.0025),
            ClientCategory::INDIVIDUAL => $this->faker->randomFloat(4, 0.0020, 0.0030),
            ClientCategory::TOURIST => $this->faker->randomFloat(4, 0.0025, 0.0035),
        };
    }
}
