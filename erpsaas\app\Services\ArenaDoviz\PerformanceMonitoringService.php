<?php

namespace App\Services\ArenaDoviz;

use App\Models\ArenaDoviz\CurrencyExchange;
use App\Models\ArenaDoviz\Delivery;
use App\Models\Company;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PerformanceMonitoringService
{
    /**
     * Get comprehensive performance metrics
     */
    public function getPerformanceMetrics(Company $company, ?Carbon $startDate = null, ?Carbon $endDate = null): array
    {
        $startDate = $startDate ?? now()->startOfDay();
        $endDate = $endDate ?? now()->endOfDay();

        return [
            'system_health' => $this->getSystemHealthMetrics($company),
            'transaction_performance' => $this->getTransactionPerformanceMetrics($company, $startDate, $endDate),
            'operational_efficiency' => $this->getOperationalEfficiencyMetrics($company, $startDate, $endDate),
            'service_quality' => $this->getServiceQualityMetrics($company, $startDate, $endDate),
            'resource_utilization' => $this->getResourceUtilizationMetrics($company),
            'alerts' => $this->getPerformanceAlerts($company),
        ];
    }

    /**
     * Get system health metrics
     */
    public function getSystemHealthMetrics(Company $company): array
    {
        $cacheKey = "system_health_{$company->id}";
        
        return Cache::remember($cacheKey, 300, function () use ($company) {
            $dbHealth = $this->checkDatabaseHealth();
            $cacheHealth = $this->checkCacheHealth();
            $queueHealth = $this->checkQueueHealth();
            $storageHealth = $this->checkStorageHealth();

            $overallHealth = $this->calculateOverallHealth([
                $dbHealth['status'],
                $cacheHealth['status'],
                $queueHealth['status'],
                $storageHealth['status'],
            ]);

            return [
                'overall_status' => $overallHealth,
                'database' => $dbHealth,
                'cache' => $cacheHealth,
                'queue' => $queueHealth,
                'storage' => $storageHealth,
                'uptime' => $this->getSystemUptime(),
                'last_check' => now()->toISOString(),
            ];
        });
    }

    /**
     * Get transaction performance metrics
     */
    public function getTransactionPerformanceMetrics(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $transactions = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->get();

        $processingTimes = $transactions->filter(function ($transaction) {
            return $transaction->settlement_date && $transaction->exchange_date;
        })->map(function ($transaction) {
            return $transaction->exchange_date->diffInMinutes($transaction->settlement_date);
        });

        $successRate = $transactions->count() > 0 
            ? ($transactions->where('status', 'completed')->count() / $transactions->count()) * 100 
            : 0;

        $avgProcessingTime = $processingTimes->avg() ?? 0;
        $p95ProcessingTime = $processingTimes->count() > 0 
            ? $processingTimes->sort()->values()[intval($processingTimes->count() * 0.95)] ?? 0
            : 0;

        return [
            'total_transactions' => $transactions->count(),
            'successful_transactions' => $transactions->where('status', 'completed')->count(),
            'failed_transactions' => $transactions->where('status', 'failed')->count(),
            'pending_transactions' => $transactions->where('status', 'pending')->count(),
            'success_rate' => $successRate,
            'avg_processing_time' => $avgProcessingTime,
            'p95_processing_time' => $p95ProcessingTime,
            'throughput' => $this->calculateThroughput($transactions, $startDate, $endDate),
            'error_rate' => 100 - $successRate,
            'performance_trend' => $this->calculatePerformanceTrend($company, $startDate, $endDate),
        ];
    }

    /**
     * Get operational efficiency metrics
     */
    public function getOperationalEfficiencyMetrics(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $deliveries = Delivery::where('company_id', $company->id)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();

        $deliveryMetrics = $this->calculateDeliveryMetrics($deliveries);
        $staffEfficiency = $this->calculateStaffEfficiency($company, $startDate, $endDate);
        $resourceUtilization = $this->calculateResourceUtilization($company, $startDate, $endDate);

        return [
            'delivery_performance' => $deliveryMetrics,
            'staff_efficiency' => $staffEfficiency,
            'resource_utilization' => $resourceUtilization,
            'automation_rate' => $this->calculateAutomationRate($company, $startDate, $endDate),
            'cost_efficiency' => $this->calculateCostEfficiency($company, $startDate, $endDate),
        ];
    }

    /**
     * Get service quality metrics
     */
    public function getServiceQualityMetrics(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $transactions = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->get();

        $qualityMetrics = [
            'accuracy_rate' => $this->calculateAccuracyRate($transactions),
            'customer_satisfaction' => $this->calculateCustomerSatisfaction($company, $startDate, $endDate),
            'first_time_resolution' => $this->calculateFirstTimeResolution($transactions),
            'response_time' => $this->calculateResponseTime($company, $startDate, $endDate),
            'compliance_score' => $this->calculateComplianceScore($company, $startDate, $endDate),
        ];

        return $qualityMetrics;
    }

    /**
     * Get resource utilization metrics
     */
    public function getResourceUtilizationMetrics(Company $company): array
    {
        return [
            'cpu_usage' => $this->getCpuUsage(),
            'memory_usage' => $this->getMemoryUsage(),
            'disk_usage' => $this->getDiskUsage(),
            'network_usage' => $this->getNetworkUsage(),
            'database_connections' => $this->getDatabaseConnections(),
            'active_sessions' => $this->getActiveSessions(),
        ];
    }

    /**
     * Get performance alerts
     */
    public function getPerformanceAlerts(Company $company): array
    {
        $alerts = [];

        // Check for high error rates
        $recentTransactions = CurrencyExchange::where('company_id', $company->id)
            ->where('exchange_date', '>=', now()->subHour())
            ->get();

        if ($recentTransactions->count() > 0) {
            $errorRate = ($recentTransactions->where('status', 'failed')->count() / $recentTransactions->count()) * 100;
            if ($errorRate > 5) {
                $alerts[] = [
                    'type' => 'error_rate',
                    'severity' => $errorRate > 10 ? 'critical' : 'warning',
                    'message' => "High error rate detected: {$errorRate}%",
                    'timestamp' => now()->toISOString(),
                ];
            }
        }

        // Check for slow processing times
        $avgProcessingTime = $this->getAverageProcessingTime($company, now()->subHour(), now());
        if ($avgProcessingTime > 30) {
            $alerts[] = [
                'type' => 'processing_time',
                'severity' => $avgProcessingTime > 60 ? 'critical' : 'warning',
                'message' => "Slow processing time detected: {$avgProcessingTime} minutes average",
                'timestamp' => now()->toISOString(),
            ];
        }

        // Check system resources
        $memoryUsage = $this->getMemoryUsage();
        if ($memoryUsage > 85) {
            $alerts[] = [
                'type' => 'memory_usage',
                'severity' => $memoryUsage > 95 ? 'critical' : 'warning',
                'message' => "High memory usage: {$memoryUsage}%",
                'timestamp' => now()->toISOString(),
            ];
        }

        return $alerts;
    }

    /**
     * Check database health
     */
    private function checkDatabaseHealth(): array
    {
        try {
            $start = microtime(true);
            DB::select('SELECT 1');
            $responseTime = (microtime(true) - $start) * 1000;

            $connectionCount = DB::select("SHOW STATUS LIKE 'Threads_connected'")[0]->Value ?? 0;
            $maxConnections = DB::select("SHOW VARIABLES LIKE 'max_connections'")[0]->Value ?? 100;

            return [
                'status' => 'healthy',
                'response_time' => round($responseTime, 2),
                'connections' => $connectionCount,
                'max_connections' => $maxConnections,
                'connection_usage' => round(($connectionCount / $maxConnections) * 100, 2),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
                'response_time' => null,
            ];
        }
    }

    /**
     * Check cache health
     */
    private function checkCacheHealth(): array
    {
        try {
            $start = microtime(true);
            Cache::put('health_check', 'ok', 10);
            $value = Cache::get('health_check');
            $responseTime = (microtime(true) - $start) * 1000;

            return [
                'status' => $value === 'ok' ? 'healthy' : 'unhealthy',
                'response_time' => round($responseTime, 2),
                'driver' => config('cache.default'),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
                'response_time' => null,
            ];
        }
    }

    /**
     * Check queue health
     */
    private function checkQueueHealth(): array
    {
        try {
            $failedJobs = DB::table('failed_jobs')->count();
            $pendingJobs = DB::table('jobs')->count();

            return [
                'status' => $failedJobs < 10 ? 'healthy' : 'warning',
                'pending_jobs' => $pendingJobs,
                'failed_jobs' => $failedJobs,
                'driver' => config('queue.default'),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check storage health
     */
    private function checkStorageHealth(): array
    {
        try {
            $storagePath = storage_path();
            $totalSpace = disk_total_space($storagePath);
            $freeSpace = disk_free_space($storagePath);
            $usedSpace = $totalSpace - $freeSpace;
            $usagePercentage = ($usedSpace / $totalSpace) * 100;

            return [
                'status' => $usagePercentage < 90 ? 'healthy' : 'warning',
                'total_space' => $totalSpace,
                'free_space' => $freeSpace,
                'used_space' => $usedSpace,
                'usage_percentage' => round($usagePercentage, 2),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Calculate overall health status
     */
    private function calculateOverallHealth(array $statuses): string
    {
        if (in_array('unhealthy', $statuses)) {
            return 'unhealthy';
        }
        if (in_array('warning', $statuses)) {
            return 'warning';
        }
        return 'healthy';
    }

    /**
     * Get system uptime
     */
    private function getSystemUptime(): array
    {
        // This is a simplified implementation
        // In production, you'd track actual application uptime
        return [
            'uptime_seconds' => time() - strtotime('today'),
            'uptime_percentage' => 99.9, // Placeholder
        ];
    }

    /**
     * Calculate throughput
     */
    private function calculateThroughput($transactions, Carbon $startDate, Carbon $endDate): float
    {
        $hours = $startDate->diffInHours($endDate);
        return $hours > 0 ? $transactions->count() / $hours : 0;
    }

    /**
     * Calculate performance trend
     */
    private function calculatePerformanceTrend(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        $currentPeriod = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->where('status', 'completed')
            ->count();

        $previousStart = $startDate->copy()->subDays($startDate->diffInDays($endDate) + 1);
        $previousEnd = $startDate->copy()->subDay();

        $previousPeriod = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$previousStart, $previousEnd])
            ->where('status', 'completed')
            ->count();

        $trend = $previousPeriod > 0 ? (($currentPeriod - $previousPeriod) / $previousPeriod) * 100 : 0;

        return [
            'current_period' => $currentPeriod,
            'previous_period' => $previousPeriod,
            'trend_percentage' => round($trend, 2),
            'trend_direction' => $trend > 0 ? 'up' : ($trend < 0 ? 'down' : 'stable'),
        ];
    }

    /**
     * Calculate delivery metrics
     */
    private function calculateDeliveryMetrics($deliveries): array
    {
        $totalDeliveries = $deliveries->count();
        $completedDeliveries = $deliveries->where('status', 'delivered')->count();
        
        $deliveryTimes = $deliveries->filter(function ($delivery) {
            return $delivery->assigned_at && $delivery->delivered_at;
        })->map(function ($delivery) {
            return $delivery->assigned_at->diffInMinutes($delivery->delivered_at);
        });

        return [
            'total_deliveries' => $totalDeliveries,
            'completed_deliveries' => $completedDeliveries,
            'completion_rate' => $totalDeliveries > 0 ? ($completedDeliveries / $totalDeliveries) * 100 : 0,
            'avg_delivery_time' => $deliveryTimes->avg() ?? 0,
            'on_time_delivery_rate' => $this->calculateOnTimeDeliveryRate($deliveries),
        ];
    }

    /**
     * Calculate staff efficiency (placeholder)
     */
    private function calculateStaffEfficiency(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        // Placeholder implementation
        return [
            'transactions_per_staff' => 0,
            'avg_handling_time' => 0,
            'productivity_score' => 0,
        ];
    }

    /**
     * Calculate resource utilization (placeholder)
     */
    private function calculateResourceUtilization(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        return [
            'system_utilization' => 75, // Placeholder
            'peak_usage_time' => '14:00',
            'efficiency_score' => 85,
        ];
    }

    /**
     * Calculate automation rate (placeholder)
     */
    private function calculateAutomationRate(Company $company, Carbon $startDate, Carbon $endDate): float
    {
        // Placeholder - would track automated vs manual processes
        return 80.0;
    }

    /**
     * Calculate cost efficiency (placeholder)
     */
    private function calculateCostEfficiency(Company $company, Carbon $startDate, Carbon $endDate): array
    {
        return [
            'cost_per_transaction' => 0,
            'efficiency_trend' => 'improving',
            'cost_savings' => 0,
        ];
    }

    /**
     * Calculate accuracy rate
     */
    private function calculateAccuracyRate($transactions): float
    {
        $totalTransactions = $transactions->count();
        $accurateTransactions = $transactions->where('status', 'completed')->count();
        
        return $totalTransactions > 0 ? ($accurateTransactions / $totalTransactions) * 100 : 0;
    }

    /**
     * Calculate customer satisfaction (placeholder)
     */
    private function calculateCustomerSatisfaction(Company $company, Carbon $startDate, Carbon $endDate): float
    {
        // Placeholder - would integrate with customer feedback system
        return 4.5; // Out of 5
    }

    /**
     * Calculate first time resolution
     */
    private function calculateFirstTimeResolution($transactions): float
    {
        // Simplified - assumes completed transactions are resolved first time
        $totalTransactions = $transactions->count();
        $resolvedFirstTime = $transactions->where('status', 'completed')->count();
        
        return $totalTransactions > 0 ? ($resolvedFirstTime / $totalTransactions) * 100 : 0;
    }

    /**
     * Calculate response time (placeholder)
     */
    private function calculateResponseTime(Company $company, Carbon $startDate, Carbon $endDate): float
    {
        // Placeholder - would track actual response times
        return 2.5; // Minutes
    }

    /**
     * Calculate compliance score (placeholder)
     */
    private function calculateComplianceScore(Company $company, Carbon $startDate, Carbon $endDate): float
    {
        // Placeholder - would check compliance requirements
        return 95.0; // Percentage
    }

    /**
     * Get CPU usage (placeholder)
     */
    private function getCpuUsage(): float
    {
        // Placeholder - would use system monitoring
        return 45.0;
    }

    /**
     * Get memory usage
     */
    private function getMemoryUsage(): float
    {
        $memoryLimit = $this->convertToBytes(ini_get('memory_limit'));
        $memoryUsage = memory_get_usage(true);
        
        return $memoryLimit > 0 ? ($memoryUsage / $memoryLimit) * 100 : 0;
    }

    /**
     * Get disk usage
     */
    private function getDiskUsage(): float
    {
        $storagePath = storage_path();
        $totalSpace = disk_total_space($storagePath);
        $freeSpace = disk_free_space($storagePath);
        
        return $totalSpace > 0 ? (($totalSpace - $freeSpace) / $totalSpace) * 100 : 0;
    }

    /**
     * Get network usage (placeholder)
     */
    private function getNetworkUsage(): array
    {
        return [
            'incoming' => 0,
            'outgoing' => 0,
            'total' => 0,
        ];
    }

    /**
     * Get database connections
     */
    private function getDatabaseConnections(): int
    {
        try {
            return DB::select("SHOW STATUS LIKE 'Threads_connected'")[0]->Value ?? 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get active sessions (placeholder)
     */
    private function getActiveSessions(): int
    {
        // Placeholder - would track active user sessions
        return 0;
    }

    /**
     * Get average processing time
     */
    private function getAverageProcessingTime(Company $company, Carbon $startDate, Carbon $endDate): float
    {
        $transactions = CurrencyExchange::where('company_id', $company->id)
            ->whereBetween('exchange_date', [$startDate, $endDate])
            ->whereNotNull('settlement_date')
            ->get();

        if ($transactions->isEmpty()) {
            return 0;
        }

        $processingTimes = $transactions->map(function ($transaction) {
            return $transaction->exchange_date->diffInMinutes($transaction->settlement_date);
        });

        return $processingTimes->avg();
    }

    /**
     * Calculate on-time delivery rate
     */
    private function calculateOnTimeDeliveryRate($deliveries): float
    {
        $deliveriesWithSLA = $deliveries->filter(function ($delivery) {
            return $delivery->assigned_at && $delivery->delivered_at;
        });

        if ($deliveriesWithSLA->isEmpty()) {
            return 0;
        }

        $onTimeDeliveries = $deliveriesWithSLA->filter(function ($delivery) {
            $slaMinutes = 120; // 2 hours SLA
            $actualMinutes = $delivery->assigned_at->diffInMinutes($delivery->delivered_at);
            return $actualMinutes <= $slaMinutes;
        });

        return ($onTimeDeliveries->count() / $deliveriesWithSLA->count()) * 100;
    }

    /**
     * Convert memory limit string to bytes
     */
    private function convertToBytes(string $value): int
    {
        $value = trim($value);
        $last = strtolower($value[strlen($value) - 1]);
        $value = (int) $value;

        switch ($last) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }

        return $value;
    }
}
