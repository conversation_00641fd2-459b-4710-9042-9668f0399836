<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('debit_note_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->constrained()->cascadeOnDelete();
            $table->foreignId('debit_note_id')->constrained()->cascadeOnDelete();
            
            // Item details
            $table->enum('item_type', [
                'service_charge', 'delivery_fee', 'commission', 'penalty', 
                'adjustment', 'currency_exchange', 'transfer_fee', 'other'
            ])->default('service_charge');
            $table->text('description');
            
            // Pricing
            $table->decimal('quantity', 10, 4)->default(1.0000);
            $table->decimal('unit_price', 15, 2);
            $table->decimal('total_amount', 15, 2);
            $table->string('currency_code', 10);
            
            // Reference and notes
            $table->string('reference_number', 100)->nullable();
            $table->text('notes')->nullable();
            
            // Audit fields
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes
            $table->index(['debit_note_id', 'item_type']);
            $table->index(['company_id', 'item_type']);
            $table->index('currency_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('debit_note_items');
    }
};
