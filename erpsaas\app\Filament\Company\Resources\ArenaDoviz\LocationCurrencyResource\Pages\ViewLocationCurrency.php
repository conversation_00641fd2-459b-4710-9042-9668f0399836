<?php

namespace App\Filament\Company\Resources\ArenaDoviz\LocationCurrencyResource\Pages;

use App\Filament\Company\Resources\ArenaDoviz\LocationCurrencyResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewLocationCurrency extends ViewRecord
{
    protected static string $resource = LocationCurrencyResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
