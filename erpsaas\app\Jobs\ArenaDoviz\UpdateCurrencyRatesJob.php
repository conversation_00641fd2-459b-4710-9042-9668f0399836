<?php

namespace App\Jobs\ArenaDoviz;

use App\Services\ArenaDoviz\RealTimeRateService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class UpdateCurrencyRatesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 300;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(RealTimeRateService $rateService): void
    {
        Log::info('Starting currency rate update job');

        try {
            $results = $rateService->updateAllRates();
            
            $successCount = collect($results)->where('success', true)->count();
            $totalCount = count($results);
            
            Log::info('Currency rate update completed', [
                'success_count' => $successCount,
                'total_count' => $totalCount,
                'results' => $results,
            ]);

            // Check for significant rate changes and send alerts
            $alerts = $rateService->getRateChangeAlerts(2.0); // 2% threshold
            
            if (!empty($alerts)) {
                Log::warning('Significant currency rate changes detected', [
                    'alerts' => $alerts,
                ]);
                
                // Here you could dispatch notification jobs
                // dispatch(new SendRateChangeAlertsJob($alerts));
            }

        } catch (\Exception $e) {
            Log::error('Currency rate update job failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Currency rate update job failed permanently', [
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);
    }
}
